# IVD智能顾问 - AI模型API配置指南

## 📋 概述

本文档详细说明了IVD智能顾问系统中AI模型的API配置，包括DeepSeek、通义千问等模型的集成方案。

## 🤖 支持的AI模型

### 1. DeepSeek 模型
- **DeepSeek-V3-0324** (默认主模型)
- **DeepSeek-R1-0528** (推理增强模型)

### 2. 通义千问模型
- **Qwen3** (基础模型)
- **Qwen-Max** (高级模型)

## 🔧 API配置架构

### 环境变量配置
```bash
# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 通义千问API配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 默认模型配置
DEFAULT_AI_MODEL=deepseek-v3-0324
FALLBACK_AI_MODEL=qwen3
```

### 云函数环境变量设置
1. 在微信开发者工具中打开云函数
2. 右键云函数 -> 云函数配置 -> 环境变量
3. 添加上述环境变量

## 📁 文件结构

```
services/
├── aiService.js          # AI服务主入口
├── providers/
│   ├── deepseekProvider.js   # DeepSeek API提供者
│   ├── qwenProvider.js       # 通义千问API提供者
│   └── baseProvider.js      # 基础提供者类
├── models/
│   ├── modelConfig.js        # 模型配置
│   └── modelManager.js       # 模型管理器
└── utils/
    ├── apiClient.js          # API客户端
    └── errorHandler.js       # 错误处理
```

## 🔑 API密钥管理

### 1. 云函数配置
```javascript
// cloudfunctions/getApiConfig/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  const { provider } = event
  
  // 从环境变量获取API密钥
  const apiKeys = {
    deepseek: process.env.DEEPSEEK_API_KEY,
    qwen: process.env.QWEN_API_KEY
  }
  
  return {
    success: true,
    data: {
      apiKey: apiKeys[provider],
      baseUrl: getBaseUrl(provider)
    }
  }
}

function getBaseUrl(provider) {
  const baseUrls = {
    deepseek: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
    qwen: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1'
  }
  return baseUrls[provider]
}
```

### 2. 安全配置
- API密钥存储在云函数环境变量中
- 前端代码不包含任何API密钥
- 使用云函数代理所有API请求

## 🌐 API提供者实现

### DeepSeek Provider
```javascript
// services/providers/deepseekProvider.js
class DeepSeekProvider extends BaseProvider {
  constructor() {
    super('deepseek')
    this.models = {
      'deepseek-v3-0324': {
        name: 'DeepSeek-V3-0324',
        maxTokens: 4096,
        temperature: 0.7
      },
      'deepseek-r1-0528': {
        name: 'DeepSeek-R1-0528',
        maxTokens: 4096,
        temperature: 0.7
      }
    }
  }

  async sendMessage(message, modelId, options = {}) {
    const config = await this.getApiConfig()
    
    const response = await this.apiClient.post('/chat/completions', {
      model: modelId,
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt(options.category)
        },
        {
          role: 'user',
          content: message
        }
      ],
      max_tokens: options.maxTokens || 1500,
      temperature: options.temperature || 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      baseURL: config.baseUrl
    })

    return this.formatResponse(response.data)
  }
}
```

### 通义千问 Provider
```javascript
// services/providers/qwenProvider.js
class QwenProvider extends BaseProvider {
  constructor() {
    super('qwen')
    this.models = {
      'qwen3': {
        name: 'Qwen3',
        maxTokens: 2048,
        temperature: 0.7
      },
      'qwen-max': {
        name: 'Qwen-Max',
        maxTokens: 4096,
        temperature: 0.7
      }
    }
  }

  async sendMessage(message, modelId, options = {}) {
    const config = await this.getApiConfig()
    
    // 通义千问使用OpenAI兼容格式
    const response = await this.apiClient.post('/chat/completions', {
      model: modelId,
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt(options.category)
        },
        {
          role: 'user',
          content: message
        }
      ],
      max_tokens: options.maxTokens || 1500,
      temperature: options.temperature || 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      baseURL: config.baseUrl
    })

    return this.formatResponse(response.data)
  }
}
```

## 🎯 模型配置

### 默认模型设置
```javascript
// services/models/modelConfig.js
const MODEL_CONFIG = {
  default: 'deepseek-v3-0324',
  fallback: 'qwen3',
  
  models: {
    'deepseek-v3-0324': {
      provider: 'deepseek',
      name: 'DeepSeek-V3-0324',
      description: 'DeepSeek最新V3模型，推理能力强，性价比极高',
      tier: 'free',
      maxTokens: 4096,
      features: ['强推理能力', '高性价比', '中文优化']
    },
    'deepseek-r1-0528': {
      provider: 'deepseek',
      name: 'DeepSeek-R1-0528',
      description: 'DeepSeek推理增强模型，专注复杂推理任务',
      tier: 'basic',
      maxTokens: 4096,
      features: ['推理增强', '逻辑分析', '复杂问题']
    },
    'qwen3': {
      provider: 'qwen',
      name: 'Qwen3',
      description: '通义千问3代模型，平衡性能与效率',
      tier: 'free',
      maxTokens: 2048,
      features: ['中文优化', '多领域知识', '快速响应']
    },
    'qwen-max': {
      provider: 'qwen',
      name: 'Qwen-Max',
      description: '通义千问最强模型，顶级性能表现',
      tier: 'standard',
      maxTokens: 4096,
      features: ['顶级性能', '复杂推理', '专业知识']
    }
  }
}
```

## 🔄 模型切换机制

### 自动降级策略
```javascript
// services/models/modelManager.js
class ModelManager {
  async selectModel(preferredModel, userTier) {
    // 1. 检查用户权限
    if (!this.hasModelAccess(preferredModel, userTier)) {
      return this.getFallbackModel(userTier)
    }
    
    // 2. 检查模型可用性
    if (!(await this.isModelAvailable(preferredModel))) {
      return this.getFallbackModel(userTier)
    }
    
    return preferredModel
  }
  
  getFallbackModel(userTier) {
    const fallbackChain = {
      'premium': ['deepseek-v3-0324', 'qwen-max', 'qwen3'],
      'standard': ['deepseek-v3-0324', 'qwen3'],
      'basic': ['deepseek-v3-0324', 'qwen3'],
      'free': ['deepseek-v3-0324', 'qwen3']
    }
    
    return fallbackChain[userTier]?.[0] || 'deepseek-v3-0324'
  }
}
```

## 🚨 错误处理

### API错误处理策略
```javascript
// services/utils/errorHandler.js
class APIErrorHandler {
  handleError(error, provider, modelId) {
    const errorCode = error.response?.status || error.code
    
    switch (errorCode) {
      case 401:
        return this.handleAuthError(provider)
      case 429:
        return this.handleRateLimitError(provider, modelId)
      case 500:
        return this.handleServerError(provider, modelId)
      default:
        return this.handleGenericError(error)
    }
  }
  
  async handleRateLimitError(provider, modelId) {
    // 自动切换到备用模型
    const fallbackModel = this.getFallbackModel(modelId)
    if (fallbackModel) {
      return { shouldRetry: true, fallbackModel }
    }
    return { shouldRetry: false, error: '服务繁忙，请稍后重试' }
  }
}
```

## 📊 监控和日志

### API调用监控
```javascript
// 监控API调用性能和成功率
const apiMetrics = {
  totalCalls: 0,
  successCalls: 0,
  errorCalls: 0,
  averageResponseTime: 0,
  modelUsage: {}
}

function logAPICall(provider, modelId, responseTime, success) {
  apiMetrics.totalCalls++
  if (success) {
    apiMetrics.successCalls++
  } else {
    apiMetrics.errorCalls++
  }
  
  // 更新平均响应时间
  apiMetrics.averageResponseTime = 
    (apiMetrics.averageResponseTime * (apiMetrics.totalCalls - 1) + responseTime) / 
    apiMetrics.totalCalls
  
  // 记录模型使用情况
  if (!apiMetrics.modelUsage[modelId]) {
    apiMetrics.modelUsage[modelId] = 0
  }
  apiMetrics.modelUsage[modelId]++
}
```

## 🔧 调试和测试

### 本地测试配置
```javascript
// 本地测试时的模拟配置
const TEST_CONFIG = {
  enableMockAPI: process.env.NODE_ENV === 'development',
  mockResponses: {
    'deepseek-v3-0324': '这是DeepSeek-V3的模拟响应',
    'qwen3': '这是Qwen3的模拟响应'
  }
}
```

### API测试脚本
```bash
# 测试DeepSeek API
curl -X POST "https://api.deepseek.com/chat/completions" \
  -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-v3-0324",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
  }'

# 测试通义千问API
curl -X POST "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions" \
  -H "Authorization: Bearer $QWEN_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen3",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
  }'
```

## 📈 性能优化

### 1. 请求缓存
- 实现智能缓存机制
- 相似问题复用答案
- 减少API调用次数

### 2. 并发控制
- 限制同时进行的API请求数量
- 实现请求队列管理
- 防止API限流

### 3. 响应优化
- 流式响应支持
- 分块传输
- 实时显示生成内容

## 🔒 安全最佳实践

1. **API密钥安全**
   - 使用环境变量存储
   - 定期轮换密钥
   - 监控异常使用

2. **请求验证**
   - 用户身份验证
   - 请求频率限制
   - 内容安全检查

3. **数据保护**
   - 敏感信息过滤
   - 传输加密
   - 日志脱敏

---

**此配置指南确保了IVD智能顾问系统的AI模型集成稳定、安全、高效运行。**
