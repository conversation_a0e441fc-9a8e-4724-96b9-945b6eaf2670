/* pages/settings/settings.wxss */

/* 用户信息区域 */
.user-section {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border: none;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  overflow: hidden;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.default-avatar {
  font-size: 60rpx;
  color: #666;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.login-btn {
  padding: 15rpx 30rpx;
  background: #1976D2;
  color: #fff;
  border-radius: 25rpx;
  font-size: 26rpx;
}

.login-btn:active {
  background: #1565C0;
}

/* 设置区域 */
.settings-section,
.professional-section,
.app-info-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.setting-item:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.setting-icon {
  width: 80rpx;
  height: 80rpx;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  background: #f0f0f0;
  border-radius: 50%;
}

.setting-content {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.setting-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.setting-arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 20rpx;
}

.setting-toggle {
  margin-left: 20rpx;
}

/* 危险操作区域 */
.danger-section {
  padding: 30rpx;
  background: #f5f5f5;
}

.danger-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.danger-btn {
  width: 100%;
  padding: 25rpx;
  background: #F44336;
  color: #fff;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  outline: none;
}

.danger-btn:active {
  background: #D32F2F;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  border-radius: 50%;
  background: #f0f0f0;
}

.modal-close:active {
  background: #e0e0e0;
}

/* 专业领域选择 */
.field-list,
.experience-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.field-option,
.experience-option {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
}

.field-option:active,
.experience-option:active {
  background: #f8f9fa;
}

.field-option.selected,
.experience-option.selected {
  background: rgba(25, 118, 210, 0.1);
  color: #1976D2;
  font-weight: bold;
}

.field-option.selected::after,
.experience-option.selected::after {
  content: '✓';
  float: right;
  color: #1976D2;
  font-weight: bold;
}

/* 专业设置特殊样式 */
.professional-section {
  background: #f8f9fa;
}

.professional-section .setting-icon {
  background: linear-gradient(135deg, #1976D2, #1565C0);
  color: #fff;
}

/* 应用信息区域 */
.app-info-section {
  background: #f5f5f5;
}

.app-info-section .setting-icon {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: #fff;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .user-avatar {
    width: 100rpx;
    height: 100rpx;
    margin-right: 20rpx;
  }
  
  .setting-icon {
    width: 60rpx;
    height: 60rpx;
    font-size: 30rpx;
    margin-right: 20rpx;
  }
  
  .setting-item {
    padding: 25rpx;
  }
}
