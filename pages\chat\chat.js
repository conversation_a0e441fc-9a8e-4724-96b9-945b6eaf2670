// pages/chat/chat.js
const app = getApp();
const { aiService } = require('../../utils/aiService');
const { ivdKnowledge } = require('../../utils/ivdKnowledge');
const { errorHandler } = require('../../utils/apiUtils');

Page({
  data: {
    messages: [],
    inputValue: '',
    isLoading: false,
    scrollTop: 0,
    scrollIntoView: '',
    currentModel: {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      description: '快速响应，适合日常咨询',
      status: 'online',
      statusText: '在线'
    },
    showTemplates: true,
    showModelModal: false,
    selectedModelId: 'gpt-3.5-turbo',
    consultationTemplates: [
      {
        id: 'rd_process',
        icon: '🔬',
        title: '研发流程',
        description: '产品开发步骤',
        content: '请详细介绍IVD产品的研发流程，包括需求分析、技术评估、产品设计等关键步骤。'
      },
      {
        id: 'registration',
        icon: '📋',
        title: '注册申报',
        description: 'NMPA注册指导',
        content: '我想了解IVD产品在中国NMPA的注册申报流程，需要准备哪些材料？'
      },
      {
        id: 'market_strategy',
        icon: '📈',
        title: '市场策略',
        description: '销售渠道建设',
        content: '请分析IVD产品的市场销售策略，包括渠道建设、定价策略等。'
      },
      {
        id: 'quality_system',
        icon: '✅',
        title: '质量体系',
        description: 'ISO13485认证',
        content: '请介绍IVD企业建立ISO13485质量管理体系的要点和注意事项。'
      }
    ],
    availableModels: [
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: '快速响应，适合日常咨询',
        status: 'online',
        statusText: '在线'
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: '更强推理能力，深度分析',
        status: 'online',
        statusText: '在线'
      },
      {
        id: 'claude-3',
        name: 'Claude-3',
        description: '擅长长文本分析',
        status: 'online',
        statusText: '在线'
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        description: '多模态AI助手',
        status: 'online',
        statusText: '在线'
      }
    ],
    suggestions: [
      '如何进行IVD产品的市场调研？',
      'NMPA注册需要多长时间？',
      '质量管理体系建立的关键要素',
      '如何选择合适的销售渠道？'
    ]
  },

  onLoad(options) {
    console.log('聊天页面加载', options);
    
    // 从全局数据加载当前模型
    this.loadCurrentModel();
    
    // 处理页面参数
    if (options.category) {
      this.handleCategoryChat(options.category, options.title);
    }
    
    if (options.chatId) {
      this.loadChatHistory(options.chatId);
    }
  },

  onShow() {
    // 页面显示时滚动到底部
    this.scrollToBottom();
  },

  // 加载当前AI模型
  loadCurrentModel() {
    const selectedModel = app.globalData.selectedAIModel;
    const model = this.data.availableModels.find(m => m.id === selectedModel);
    
    if (model) {
      this.setData({
        currentModel: model,
        selectedModelId: selectedModel
      });
    }
  },

  // 处理分类聊天
  handleCategoryChat(category, title) {
    const knowledge = app.getIVDKnowledge(category);
    if (knowledge) {
      // 设置相关的建议问题
      const suggestions = knowledge.topics.map(topic => `请介绍${topic}的相关内容`);
      this.setData({
        suggestions: suggestions.slice(0, 4)
      });
      
      // 发送欢迎消息
      const welcomeMessage = `您好！我是您的IVD专业顾问。您想了解${title}相关的内容，我可以为您详细介绍。请选择您感兴趣的话题或直接提问。`;
      this.addMessage('assistant', welcomeMessage);
    }
  },

  // 输入变化处理
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息
  async sendMessage() {
    const content = this.data.inputValue.trim();
    if (!content || this.data.isLoading) return;

    // 添加用户消息
    this.addMessage('user', content);
    
    // 清空输入框
    this.setData({
      inputValue: '',
      isLoading: true,
      showTemplates: false
    });

    try {
      // 调用AI API
      const response = await this.callAIAPI(content);
      
      // 添加AI回复
      this.addMessage('assistant', response);
      
    } catch (error) {
      console.error('AI API调用失败:', error);
      this.addMessage('assistant', '抱歉，我暂时无法回答您的问题。请稍后再试。');
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  // 调用AI API
  async callAIAPI(message) {
    try {
      // 获取当前模型ID
      const modelId = this.data.currentModel.id;

      // 获取上下文信息
      const category = this.getCurrentCategory();

      // 生成智能建议
      const suggestions = ivdKnowledge.generateSuggestions(message, category);
      this.setData({
        suggestions: suggestions.slice(0, 4)
      });

      // 调用AI服务
      const response = await aiService.sendMessage(message, modelId, {
        category,
        maxTokens: 1500,
        temperature: 0.7
      });

      return response;
    } catch (error) {
      console.error('AI API调用失败:', error);

      // 使用错误处理器
      const errorResult = errorHandler.handleError(error, 'AI API调用');

      // 如果可以重试，返回本地回复
      if (errorResult.shouldRetry) {
        return this.generateIVDResponse(message);
      }

      throw new Error(errorResult.userMessage);
    }
  },

  // 获取当前分类
  getCurrentCategory() {
    // 可以根据页面参数或消息内容判断分类
    const message = this.data.messages;
    if (message.length === 0) return null;

    // 简单的分类判断逻辑
    const lastUserMessage = message.filter(m => m.role === 'user').pop();
    if (!lastUserMessage) return null;

    const content = lastUserMessage.content.toLowerCase();
    if (content.includes('研发') || content.includes('开发')) return 'rd';
    if (content.includes('注册') || content.includes('申报')) return 'registration';
    if (content.includes('销售') || content.includes('市场')) return 'sales';
    if (content.includes('质量') || content.includes('ISO')) return 'quality';

    return null;
  },

  // 生成IVD相关回复
  generateIVDResponse(message) {
    const msg = message.toLowerCase();
    
    if (msg.includes('研发') || msg.includes('开发')) {
      return `关于IVD产品研发流程，主要包括以下几个关键步骤：

1. **需求分析与市场调研**
   - 分析目标市场和用户需求
   - 评估技术可行性和商业价值

2. **产品设计与开发**
   - 确定技术路线和产品规格
   - 进行原型设计和验证

3. **临床试验设计**
   - 制定临床试验方案
   - 收集临床数据验证产品性能

4. **质量管理体系建立**
   - 建立ISO13485质量管理体系
   - 确保产品质量和安全性

您想了解哪个具体环节的详细信息？`;
    }
    
    if (msg.includes('注册') || msg.includes('申报')) {
      return `IVD产品注册申报是产品上市的关键环节：

**NMPA注册流程：**
1. 产品分类确定（I类、II类、III类）
2. 技术文档准备
3. 临床试验（如需要）
4. 注册申请提交
5. 技术审评
6. 现场检查（如需要）
7. 注册证书颁发

**关键材料：**
- 产品技术要求
- 研究资料
- 临床评价资料
- 质量管理体系文件

整个流程通常需要6-18个月，具体时间取决于产品类别和复杂程度。`;
    }
    
    if (msg.includes('销售') || msg.includes('市场')) {
      return `IVD产品市场销售策略建议：

**市场定位：**
- 明确目标客户群体（医院、实验室、诊所）
- 分析竞争对手和市场空间

**渠道建设：**
- 直销团队建设
- 经销商网络布局
- 线上销售平台

**价格策略：**
- 成本加成定价
- 竞争导向定价
- 价值导向定价

**客户关系管理：**
- 建立客户档案
- 定期客户拜访
- 技术支持服务

您希望深入了解哪个方面的策略？`;
    }
    
    // 默认回复
    return `感谢您的咨询！作为IVD专业顾问，我可以为您提供以下方面的专业建议：

🔬 **产品研发流程** - 从需求分析到产品上市的完整流程
📋 **注册申报指导** - NMPA、CE、FDA等注册要求
📈 **市场销售策略** - 市场定位、渠道建设、客户管理
✅ **质量管理体系** - ISO13485体系建立和维护

请告诉我您具体想了解哪个方面，我会为您提供详细的专业指导。`;
  },

  // 添加消息
  addMessage(role, content) {
    const message = {
      id: this.generateId(),
      role,
      content,
      timestamp: Date.now(),
      timeStr: this.formatTime(Date.now())
    };

    this.setData({
      messages: [...this.data.messages, message]
    });

    // 保存到全局数据
    app.addChatMessage(message);

    // 如果是AI回复，同步到云端
    if (role === 'assistant') {
      this.syncChatToCloud();
    }

    // 滚动到底部
    this.scrollToBottom();
  },

  // 同步聊天记录到云端
  async syncChatToCloud() {
    try {
      // 检查是否启用云端同步
      const cloudSyncEnabled = wx.getStorageSync('cloudSyncEnabled');
      if (!cloudSyncEnabled) return;

      // 检查用户是否已登录
      if (!app.globalData.userInfo || app.globalData.userInfo.isGuest) return;

      const { cloudSyncManager } = require('../../utils/cloudSync');

      // 同步最近的聊天记录
      cloudSyncManager.addSyncTask({
        type: 'chatHistory',
        data: {
          messages: this.data.messages.slice(-10), // 最近10条消息
          sessionId: this.data.sessionId || this.generateSessionId(),
          model: this.data.currentModel.id,
          category: this.getCurrentCategory()
        }
      });

    } catch (error) {
      console.error('同步聊天记录失败:', error);
    }
  },

  // 生成会话ID
  generateSessionId() {
    if (!this.data.sessionId) {
      this.setData({
        sessionId: Date.now().toString(36) + Math.random().toString(36).substr(2)
      });
    }
    return this.data.sessionId;
  },

  // 使用模板
  useTemplate(e) {
    const template = e.currentTarget.dataset.template;
    this.setData({
      inputValue: template.content
    });
  },

  // 使用建议
  useSuggestion(e) {
    const text = e.currentTarget.dataset.text;
    this.setData({
      inputValue: text
    });
  },

  // 切换模型
  switchModel() {
    this.setData({
      showModelModal: true
    });
  },

  // 选择模型
  selectModel(e) {
    const model = e.currentTarget.dataset.model;
    app.globalData.selectedAIModel = model.id;
    
    this.setData({
      currentModel: model,
      selectedModelId: model.id,
      showModelModal: false
    });
    
    wx.showToast({
      title: `已切换到${model.name}`,
      icon: 'success'
    });
  },

  // 隐藏模型选择弹窗
  hideModelModal() {
    this.setData({
      showModelModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 清空对话
  clearChat() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前对话吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            messages: [],
            showTemplates: true
          });
          wx.showToast({
            title: '对话已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 复制消息
  copyMessage(e) {
    const content = e.currentTarget.dataset.content;
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 重新生成回复
  async regenerateResponse(e) {
    const messageId = e.currentTarget.dataset.id;
    const messageIndex = this.data.messages.findIndex(m => m.id === messageId);
    
    if (messageIndex > 0) {
      const userMessage = this.data.messages[messageIndex - 1];
      
      // 移除当前AI回复
      const newMessages = this.data.messages.slice(0, messageIndex);
      this.setData({
        messages: newMessages,
        isLoading: true
      });
      
      try {
        // 重新调用API
        const response = await this.callAIAPI(userMessage.content);
        this.addMessage('assistant', response);
      } catch (error) {
        this.addMessage('assistant', '抱歉，重新生成失败，请稍后再试。');
      } finally {
        this.setData({
          isLoading: false
        });
      }
    }
  },

  // 语音输入
  startVoiceInput() {
    wx.showToast({
      title: '语音功能开发中',
      icon: 'none'
    });
  },

  // 滚动到底部
  scrollToBottom() {
    if (this.data.messages.length > 0) {
      const lastMessage = this.data.messages[this.data.messages.length - 1];
      this.setData({
        scrollIntoView: `msg-${lastMessage.id}`
      });
    }
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业的医疗器械咨询助手',
      path: '/pages/index/index'
    };
  }
});
