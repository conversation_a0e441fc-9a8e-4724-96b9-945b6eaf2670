// cloudfunctions/payment/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 付费系统云函数
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @returns {Object} 操作结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  const { action, data } = event
  
  try {
    switch (action) {
      case 'getUserSubscription':
        return await getUserSubscription(OPENID)
      case 'createOrder':
        return await createOrder(OPENID, data)
      case 'verifyPayment':
        return await verifyPayment(OPENID, data)
      case 'getUsageStats':
        return await getUsageStats(OPENID)
      case 'checkModelAccess':
        return await checkModelAccess(OPENID, data)
      case 'recordUsage':
        return await recordUsage(OPENID, data)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('付费系统操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      error: error.message
    }
  }
}

/**
 * 获取用户订阅信息
 */
async function getUserSubscription(openid) {
  const subscriptionCollection = db.collection('user_subscriptions')
  const result = await subscriptionCollection.where({ openid }).get()
  
  if (result.data.length > 0) {
    const subscription = result.data[0]
    const now = new Date()
    
    // 检查订阅是否过期
    const isExpired = subscription.expiresAt && new Date(subscription.expiresAt) < now
    
    return {
      success: true,
      data: {
        ...subscription,
        isExpired,
        isActive: !isExpired && subscription.status === 'active'
      }
    }
  } else {
    // 创建免费用户记录
    const freeSubscription = {
      openid,
      tier: 'free',
      status: 'active',
      createdAt: new Date(),
      usage: {
        daily: 0,
        monthly: 0,
        total: 0
      },
      limits: {
        dailyLimit: 10,
        monthlyLimit: 100,
        modelAccess: ['gpt-3.5-turbo', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo']
      }
    }
    
    await subscriptionCollection.add({
      data: freeSubscription
    })
    
    return {
      success: true,
      data: {
        ...freeSubscription,
        isExpired: false,
        isActive: true
      }
    }
  }
}

/**
 * 创建订单
 */
async function createOrder(openid, orderData) {
  const { planType, duration } = orderData
  
  // 订阅计划配置
  const plans = {
    basic: {
      name: '基础版',
      price: 19.9,
      dailyLimit: 100,
      monthlyLimit: 2000,
      modelAccess: [
        'gpt-3.5-turbo', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo',
        'claude-3-sonnet', 'gemini-pro', 'deepseek-coder', 'qwen-plus', 'hunyuan-pro'
      ]
    },
    standard: {
      name: '标准版',
      price: 39.9,
      dailyLimit: 300,
      monthlyLimit: 6000,
      modelAccess: [
        'gpt-3.5-turbo', 'gpt-4', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo',
        'claude-3-sonnet', 'gemini-pro', 'deepseek-coder', 'qwen-plus', 'hunyuan-pro'
      ]
    },
    premium: {
      name: '专业版',
      price: 99.9,
      dailyLimit: 1000,
      monthlyLimit: 20000,
      modelAccess: [
        'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'claude-3-sonnet', 'claude-3-opus',
        'gemini-pro', 'gemini-ultra', 'deepseek-chat', 'deepseek-coder',
        'qwen-turbo', 'qwen-plus', 'qwen-max', 'hunyuan-turbo', 'hunyuan-pro'
      ]
    }
  }
  
  const plan = plans[planType]
  if (!plan) {
    return {
      success: false,
      message: '无效的订阅计划'
    }
  }
  
  // 计算价格（按月计算）
  const totalPrice = plan.price * duration
  
  // 创建订单记录
  const orderCollection = db.collection('orders')
  const order = {
    openid,
    planType,
    planName: plan.name,
    duration,
    price: plan.price,
    totalPrice,
    status: 'pending',
    createdAt: new Date(),
    orderId: generateOrderId()
  }
  
  const result = await orderCollection.add({
    data: order
  })
  
  // 这里应该调用微信支付API创建支付订单
  // 为了演示，我们直接返回订单信息
  
  return {
    success: true,
    data: {
      orderId: order.orderId,
      totalPrice,
      planName: plan.name,
      duration,
      // 实际应用中这里应该返回微信支付参数
      paymentParams: {
        timeStamp: Date.now().toString(),
        nonceStr: generateNonceStr(),
        package: `prepay_id=${order.orderId}`,
        signType: 'RSA',
        paySign: 'mock_sign'
      }
    }
  }
}

/**
 * 验证支付结果
 */
async function verifyPayment(openid, paymentData) {
  const { orderId } = paymentData
  
  // 这里应该调用微信支付API验证支付结果
  // 为了演示，我们模拟支付成功
  
  const orderCollection = db.collection('orders')
  const subscriptionCollection = db.collection('user_subscriptions')
  
  // 获取订单信息
  const orderResult = await orderCollection.where({
    openid,
    orderId,
    status: 'pending'
  }).get()
  
  if (orderResult.data.length === 0) {
    return {
      success: false,
      message: '订单不存在或已处理'
    }
  }
  
  const order = orderResult.data[0]
  
  // 更新订单状态
  await orderCollection.doc(order._id).update({
    data: {
      status: 'paid',
      paidAt: new Date()
    }
  })
  
  // 更新用户订阅
  const now = new Date()
  const expiresAt = new Date(now.getTime() + order.duration * 30 * 24 * 60 * 60 * 1000) // 按月计算
  
  const plans = {
    basic: {
      dailyLimit: 100,
      monthlyLimit: 2000,
      modelAccess: [
        'gpt-3.5-turbo', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo',
        'claude-3-sonnet', 'gemini-pro', 'deepseek-coder', 'qwen-plus', 'hunyuan-pro'
      ]
    },
    standard: {
      dailyLimit: 300,
      monthlyLimit: 6000,
      modelAccess: [
        'gpt-3.5-turbo', 'gpt-4', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo',
        'claude-3-sonnet', 'gemini-pro', 'deepseek-coder', 'qwen-plus', 'hunyuan-pro'
      ]
    },
    premium: {
      dailyLimit: 1000,
      monthlyLimit: 20000,
      modelAccess: [
        'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'claude-3-sonnet', 'claude-3-opus',
        'gemini-pro', 'gemini-ultra', 'deepseek-chat', 'deepseek-coder',
        'qwen-turbo', 'qwen-plus', 'qwen-max', 'hunyuan-turbo', 'hunyuan-pro'
      ]
    }
  }
  
  const planConfig = plans[order.planType]
  
  await subscriptionCollection.where({ openid }).update({
    data: {
      tier: order.planType,
      status: 'active',
      expiresAt,
      limits: planConfig,
      updatedAt: new Date()
    }
  })
  
  return {
    success: true,
    message: '支付成功，订阅已激活',
    data: {
      tier: order.planType,
      expiresAt
    }
  }
}

/**
 * 获取使用统计
 */
async function getUsageStats(openid) {
  const usageCollection = db.collection('usage_records')
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  
  // 获取今日使用量
  const dailyResult = await usageCollection.where({
    openid,
    createdAt: db.command.gte(today)
  }).count()
  
  // 获取本月使用量
  const monthlyResult = await usageCollection.where({
    openid,
    createdAt: db.command.gte(thisMonth)
  }).count()
  
  // 获取总使用量
  const totalResult = await usageCollection.where({ openid }).count()
  
  return {
    success: true,
    data: {
      daily: dailyResult.total,
      monthly: monthlyResult.total,
      total: totalResult.total
    }
  }
}

/**
 * 检查模型访问权限
 */
async function checkModelAccess(openid, { modelId }) {
  const subscription = await getUserSubscription(openid)
  
  if (!subscription.success) {
    return {
      success: false,
      message: '获取订阅信息失败'
    }
  }
  
  const { data: subscriptionData } = subscription
  const hasAccess = subscriptionData.limits.modelAccess.includes(modelId)
  
  return {
    success: true,
    data: {
      hasAccess,
      tier: subscriptionData.tier,
      modelId
    }
  }
}

/**
 * 记录使用量
 */
async function recordUsage(openid, usageData) {
  const usageCollection = db.collection('usage_records')
  
  const record = {
    openid,
    modelId: usageData.modelId,
    tokens: usageData.tokens || 1,
    category: usageData.category,
    createdAt: new Date()
  }
  
  await usageCollection.add({
    data: record
  })
  
  return {
    success: true,
    message: '使用记录已保存'
  }
}

/**
 * 生成订单ID
 */
function generateOrderId() {
  return 'ORDER_' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5).toUpperCase()
}

/**
 * 生成随机字符串
 */
function generateNonceStr() {
  return Math.random().toString(36).substr(2, 15)
}
