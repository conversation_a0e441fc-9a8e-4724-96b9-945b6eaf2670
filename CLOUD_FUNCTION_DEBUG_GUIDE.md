# IVD智能顾问 - 云函数配置调试指南

## 📋 云函数概览

### 已部署的云函数
1. **login** - 用户登录认证
2. **payment** - 付费订阅系统
3. **userdata** - 用户数据管理
4. **syncUserData** - 数据同步
5. **getApiConfig** - API配置获取
6. **apiConfig** - API配置管理
7. **initDB** - 数据库初始化

## 🔍 云函数调用检查清单

### 1. Login 云函数验证

#### 功能检查
- [x] 传统微信登录 (`loginType: 'wechat'`)
- [x] 一键登录 (`loginType: 'wechat_oneclick'`)
- [x] 用户数据创建和更新
- [x] 云存储同步
- [x] 登录历史记录

#### 调用示例
```javascript
// 一键登录
const result = await cloudCall.login({
  code: 'wx_login_code',
  loginType: 'wechat_oneclick'
})

// 传统登录
const result = await cloudCall.login({
  code: 'wx_login_code',
  userInfo: userProfileData,
  loginType: 'wechat'
})
```

#### 预期返回
```javascript
{
  success: true,
  data: {
    openid: 'user_openid',
    unionid: 'user_unionid',
    userId: 'database_user_id',
    nickName: '用户昵称',
    avatarUrl: '头像URL',
    isNewUser: false,
    loginType: 'wechat_oneclick',
    cloudStorageEnabled: true
  }
}
```

### 2. Payment 云函数验证

#### 功能检查
- [x] 获取用户订阅信息 (`getUserSubscription`)
- [x] 创建订单 (`createOrder`)
- [x] 验证支付 (`verifyPayment`)
- [x] 获取使用统计 (`getUsageStats`)
- [x] 检查模型访问权限 (`checkModelAccess`)
- [x] 记录使用量 (`recordUsage`)

#### 调用示例
```javascript
// 获取订阅信息
const subscription = await cloudCall.payment('getUserSubscription')

// 创建订单
const order = await cloudCall.payment('createOrder', {
  planType: 'standard',
  duration: 1
})

// 验证支付
const payment = await cloudCall.payment('verifyPayment', {
  orderId: 'ORDER_123456'
})
```

#### 订阅计划配置
```javascript
const plans = {
  free: {
    dailyLimit: 10,
    monthlyLimit: 100,
    modelAccess: ['deepseek-v3-0324', 'qwen3']
  },
  basic: {
    dailyLimit: 100,
    monthlyLimit: 2000,
    modelAccess: ['deepseek-v3-0324', 'deepseek-r1-0528', 'qwen3']
  },
  standard: {
    dailyLimit: 300,
    monthlyLimit: 6000,
    modelAccess: ['deepseek-v3-0324', 'deepseek-r1-0528', 'qwen3', 'qwen-max']
  },
  premium: {
    dailyLimit: 1000,
    monthlyLimit: 20000,
    modelAccess: ['deepseek-v3-0324', 'deepseek-r1-0528', 'qwen3', 'qwen-max']
  }
}
```

### 3. UserData 云函数验证

#### 功能检查
- [x] 获取用户信息 (`getUserInfo`)
- [x] 更新用户信息 (`updateUserInfo`)
- [x] 保存偏好设置 (`savePreferences`)
- [x] 获取偏好设置 (`getPreferences`)
- [x] 保存聊天记录 (`saveChatHistory`)
- [x] 获取聊天记录 (`getChatHistory`)
- [x] 删除聊天记录 (`deleteChatHistory`)
- [x] API密钥管理 (`saveAPIKeys`, `getAPIKeys`)

#### 调用示例
```javascript
// 保存聊天记录
const chatResult = await wx.cloud.callFunction({
  name: 'userdata',
  data: {
    action: 'saveChatHistory',
    data: {
      sessionId: 'session_123',
      messages: [...],
      model: 'deepseek-v3-0324',
      category: 'rd'
    }
  }
})
```

### 4. SyncUserData 云函数验证

#### 功能检查
- [x] 数据同步 (`sync`)
- [x] 数据上传 (`upload`)
- [x] 数据下载 (`download`)
- [x] 云存储集成

#### 调用示例
```javascript
// 同步用户数据
const syncResult = await wx.cloud.callFunction({
  name: 'syncUserData',
  data: {
    action: 'sync',
    userInfo: app.globalData.userInfo
  }
})
```

## 🐛 常见问题诊断

### 1. 云函数不存在错误
```
Error: errCode: -501000 | errMsg: FunctionName parameter could not be found
```

**解决方案:**
1. 检查云函数是否已部署
2. 验证云函数名称拼写
3. 确认云开发环境ID正确

### 2. 权限错误
```
Error: errCode: -1 | errMsg: system error
```

**解决方案:**
1. 检查云函数权限设置
2. 验证用户身份认证
3. 确认数据库权限配置

### 3. 数据库操作失败
```
Error: database operation failed
```

**解决方案:**
1. 检查数据库集合是否存在
2. 验证数据格式是否正确
3. 确认数据库索引配置

### 4. 云存储操作失败
```
Error: cloud storage operation failed
```

**解决方案:**
1. 检查云存储权限
2. 验证文件路径格式
3. 确认存储空间配额

## 🔧 调试工具和方法

### 1. 云函数日志查看
```javascript
// 在云函数中添加详细日志
console.log('函数开始执行:', event)
console.log('用户身份:', wxContext.OPENID)
console.log('操作结果:', result)
```

### 2. 本地调试
```bash
# 使用微信开发者工具
1. 右键云函数 -> 本地调试
2. 设置断点
3. 查看变量值
4. 单步执行
```

### 3. 云端调试
```javascript
// 使用云函数测试
const testData = {
  action: 'getUserSubscription'
}

// 在开发者工具中测试
wx.cloud.callFunction({
  name: 'payment',
  data: testData
}).then(console.log).catch(console.error)
```

## 📊 性能监控

### 1. 响应时间监控
```javascript
// 在云函数中添加性能监控
const startTime = Date.now()
// ... 业务逻辑
const endTime = Date.now()
console.log(`执行时间: ${endTime - startTime}ms`)
```

### 2. 错误率统计
```javascript
// 错误统计
const errorStats = {
  total: 0,
  success: 0,
  errors: 0
}
```

### 3. 资源使用监控
- 内存使用量
- CPU使用率
- 网络请求次数
- 数据库查询次数

## 🔒 安全配置检查

### 1. 数据库安全规则
```javascript
// 用户数据访问规则
{
  "read": "auth.openid == resource.openid",
  "write": "auth.openid == resource.openid"
}
```

### 2. 云函数权限
- 最小权限原则
- 用户身份验证
- 数据访问控制

### 3. API密钥安全
- 加密存储
- 访问权限控制
- 定期轮换

## 🚀 部署检查清单

### 部署前检查
- [ ] 代码语法检查
- [ ] 依赖包安装
- [ ] 环境变量配置
- [ ] 权限设置验证

### 部署后验证
- [ ] 云函数可正常调用
- [ ] 数据库操作正常
- [ ] 云存储功能正常
- [ ] 错误处理正确

### 回滚准备
- [ ] 备份当前版本
- [ ] 准备回滚脚本
- [ ] 监控部署状态
- [ ] 快速响应机制

## 📈 优化建议

### 1. 性能优化
- 减少数据库查询次数
- 使用数据库索引
- 缓存常用数据
- 异步处理非关键操作

### 2. 成本优化
- 合理设置云函数规格
- 优化代码执行效率
- 减少不必要的网络请求
- 使用CDN加速静态资源

### 3. 可维护性
- 统一错误处理
- 完善日志记录
- 模块化代码结构
- 自动化测试

## 📞 故障排除流程

### 1. 问题识别
1. 收集错误信息
2. 查看云函数日志
3. 检查用户反馈
4. 分析错误模式

### 2. 问题定位
1. 复现问题场景
2. 检查相关配置
3. 验证数据完整性
4. 分析调用链路

### 3. 问题解决
1. 制定修复方案
2. 测试修复效果
3. 部署修复版本
4. 验证问题解决

### 4. 预防措施
1. 完善监控告警
2. 优化错误处理
3. 加强测试覆盖
4. 建立应急预案

---

**使用此指南可以系统性地检查和调试云函数配置，确保IVD智能顾问系统的稳定运行。**
