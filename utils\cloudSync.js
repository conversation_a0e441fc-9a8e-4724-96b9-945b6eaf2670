// utils/cloudSync.js
// 云端数据同步工具

/**
 * 云端数据同步管理器
 */
class CloudSyncManager {
  constructor() {
    this.isInitialized = false
    this.syncQueue = []
    this.isSyncing = false
    this.lastSyncTime = null
    this.syncInterval = 30000 // 30秒同步一次
    this.autoSyncTimer = null
  }

  /**
   * 初始化云开发
   */
  init() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      return false
    }

    try {
      wx.cloud.init({
        env: 'your-env-id', // 替换为你的云开发环境ID
        traceUser: true
      })
      
      this.isInitialized = true
      console.log('云开发初始化成功')
      
      // 启动自动同步
      this.startAutoSync()
      
      return true
    } catch (error) {
      console.error('云开发初始化失败:', error)
      return false
    }
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    const app = getApp()
    return !!(app.globalData.userInfo && app.globalData.userInfo.openid)
  }

  /**
   * 同步用户偏好设置到云端
   */
  async syncUserPreferences(preferences) {
    if (!this.isLoggedIn()) {
      console.log('用户未登录，跳过偏好设置同步')
      return { success: false, message: '用户未登录' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'savePreferences',
          data: preferences
        }
      })

      if (result.result.success) {
        console.log('用户偏好设置同步成功')
        return { success: true }
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('用户偏好设置同步失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从云端获取用户偏好设置
   */
  async getUserPreferences() {
    if (!this.isLoggedIn()) {
      return { success: false, message: '用户未登录' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getPreferences'
        }
      })

      if (result.result.success) {
        return { success: true, data: result.result.data }
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('获取用户偏好设置失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 同步聊天记录到云端
   */
  async syncChatHistory(chatData) {
    if (!this.isLoggedIn()) {
      console.log('用户未登录，跳过聊天记录同步')
      return { success: false, message: '用户未登录' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'saveChatHistory',
          data: chatData
        }
      })

      if (result.result.success) {
        console.log('聊天记录同步成功')
        return { success: true, data: result.result.data }
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('聊天记录同步失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从云端获取聊天记录
   */
  async getChatHistory(params = {}) {
    if (!this.isLoggedIn()) {
      return { success: false, message: '用户未登录' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getChatHistory',
          data: params
        }
      })

      if (result.result.success) {
        return { success: true, data: result.result.data }
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('获取聊天记录失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 同步API密钥到云端
   */
  async syncAPIKeys(apiKeys) {
    if (!this.isLoggedIn()) {
      console.log('用户未登录，跳过API密钥同步')
      return { success: false, message: '用户未登录' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'saveAPIKeys',
          data: apiKeys
        }
      })

      if (result.result.success) {
        console.log('API密钥同步成功')
        return { success: true }
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('API密钥同步失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从云端获取API密钥
   */
  async getAPIKeys() {
    if (!this.isLoggedIn()) {
      return { success: false, message: '用户未登录' }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getAPIKeys'
        }
      })

      if (result.result.success) {
        return { success: true, data: result.result.data }
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      console.error('获取API密钥失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 添加同步任务到队列
   */
  addSyncTask(task) {
    this.syncQueue.push({
      ...task,
      timestamp: Date.now(),
      retryCount: 0
    })
    
    // 如果不在同步中，立即开始同步
    if (!this.isSyncing) {
      this.processSyncQueue()
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return
    }

    this.isSyncing = true

    while (this.syncQueue.length > 0) {
      const task = this.syncQueue.shift()
      
      try {
        await this.executeSyncTask(task)
      } catch (error) {
        console.error('同步任务执行失败:', error)
        
        // 重试机制
        if (task.retryCount < 3) {
          task.retryCount++
          this.syncQueue.unshift(task) // 重新加入队列头部
        }
      }
    }

    this.isSyncing = false
    this.lastSyncTime = Date.now()
  }

  /**
   * 执行同步任务
   */
  async executeSyncTask(task) {
    switch (task.type) {
      case 'preferences':
        return await this.syncUserPreferences(task.data)
      case 'chatHistory':
        return await this.syncChatHistory(task.data)
      case 'apiKeys':
        return await this.syncAPIKeys(task.data)
      default:
        throw new Error(`未知的同步任务类型: ${task.type}`)
    }
  }

  /**
   * 启动自动同步
   */
  startAutoSync() {
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer)
    }

    this.autoSyncTimer = setInterval(() => {
      this.autoSyncData()
    }, this.syncInterval)

    console.log('自动同步已启动')
  }

  /**
   * 停止自动同步
   */
  stopAutoSync() {
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer)
      this.autoSyncTimer = null
    }
    console.log('自动同步已停止')
  }

  /**
   * 自动同步数据
   */
  async autoSyncData() {
    if (!this.isLoggedIn()) {
      return
    }

    try {
      const app = getApp()
      
      // 同步用户偏好设置
      const { userPreferencesManager } = require('./userPreferences')
      const preferences = userPreferencesManager.preferences
      if (preferences) {
        this.addSyncTask({
          type: 'preferences',
          data: preferences
        })
      }

      // 同步API密钥
      if (app.globalData.apiKeys) {
        this.addSyncTask({
          type: 'apiKeys',
          data: app.globalData.apiKeys
        })
      }

      // 同步最新的聊天记录
      const recentChats = app.globalData.chatHistory.slice(-10) // 最近10条
      if (recentChats.length > 0) {
        this.addSyncTask({
          type: 'chatHistory',
          data: {
            messages: recentChats,
            sessionId: this.generateSessionId()
          }
        })
      }

    } catch (error) {
      console.error('自动同步失败:', error)
    }
  }

  /**
   * 手动同步所有数据
   */
  async syncAllData() {
    if (!this.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '同步中...'
    })

    try {
      const app = getApp()
      const results = []

      // 同步用户偏好设置
      const { userPreferencesManager } = require('./userPreferences')
      const preferencesResult = await this.syncUserPreferences(userPreferencesManager.preferences)
      results.push({ type: '偏好设置', ...preferencesResult })

      // 同步API密钥
      const apiKeysResult = await this.syncAPIKeys(app.globalData.apiKeys)
      results.push({ type: 'API密钥', ...apiKeysResult })

      // 同步聊天记录
      if (app.globalData.chatHistory.length > 0) {
        const chatResult = await this.syncChatHistory({
          messages: app.globalData.chatHistory,
          sessionId: this.generateSessionId()
        })
        results.push({ type: '聊天记录', ...chatResult })
      }

      wx.hideLoading()

      const successCount = results.filter(r => r.success).length
      const totalCount = results.length

      wx.showToast({
        title: `同步完成 ${successCount}/${totalCount}`,
        icon: successCount === totalCount ? 'success' : 'none'
      })

      return results

    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '同步失败',
        icon: 'error'
      })
      console.error('手动同步失败:', error)
    }
  }

  /**
   * 从云端恢复所有数据
   */
  async restoreAllData() {
    if (!this.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '恢复中...'
    })

    try {
      const app = getApp()

      // 恢复用户偏好设置
      const preferencesResult = await this.getUserPreferences()
      if (preferencesResult.success) {
        const { userPreferencesManager } = require('./userPreferences')
        userPreferencesManager.preferences = preferencesResult.data
        userPreferencesManager.savePreferences()
      }

      // 恢复API密钥
      const apiKeysResult = await this.getAPIKeys()
      if (apiKeysResult.success) {
        app.globalData.apiKeys = {
          ...app.globalData.apiKeys,
          ...apiKeysResult.data
        }
        app.saveUserSettings()
      }

      // 恢复聊天记录
      const chatResult = await this.getChatHistory({ limit: 50 })
      if (chatResult.success && chatResult.data.length > 0) {
        // 合并云端聊天记录
        const cloudMessages = []
        chatResult.data.forEach(session => {
          if (session.messages) {
            cloudMessages.push(...session.messages)
          }
        })
        
        app.globalData.chatHistory = cloudMessages
        app.saveUserSettings()
      }

      wx.hideLoading()
      wx.showToast({
        title: '数据恢复成功',
        icon: 'success'
      })

    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '数据恢复失败',
        icon: 'error'
      })
      console.error('数据恢复失败:', error)
    }
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isInitialized: this.isInitialized,
      isLoggedIn: this.isLoggedIn(),
      isSyncing: this.isSyncing,
      queueLength: this.syncQueue.length,
      lastSyncTime: this.lastSyncTime,
      autoSyncEnabled: !!this.autoSyncTimer
    }
  }

  /**
   * 清除同步队列
   */
  clearSyncQueue() {
    this.syncQueue = []
    console.log('同步队列已清空')
  }
}

// 创建全局实例
const cloudSyncManager = new CloudSyncManager()

module.exports = {
  cloudSyncManager,
  CloudSyncManager
}
