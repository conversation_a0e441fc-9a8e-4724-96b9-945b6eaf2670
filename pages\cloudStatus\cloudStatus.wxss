/* pages/cloudStatus/cloudStatus.wxss */

/* 头部区域 */
.header-section {
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl);
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.header-subtitle {
  display: block;
  font-size: var(--font-size-base);
  opacity: 0.9;
  margin-bottom: var(--spacing-sm);
}

.last-check {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

.header-icon {
  font-size: 80rpx;
}

/* 状态概览 */
.status-summary {
  margin-bottom: var(--spacing-lg);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.summary-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: var(--spacing-md);
}

.summary-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-base);
}

.summary-value {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.summary-value.success {
  color: var(--success-color);
}

.summary-value.error {
  color: var(--error-color);
}

.summary-value.warning {
  color: var(--warning-color);
}

.summary-label {
  display: block;
  font-size: var(--font-size-sm);
}

/* 云函数列表 */
.functions-list {
  margin-bottom: var(--spacing-lg);
}

.function-item {
  position: relative;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-lg);
}

.function-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.function-info {
  flex: 1;
}

.function-name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.function-desc {
  display: block;
  font-size: var(--font-size-sm);
}

.function-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-base);
}

.status-healthy {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.status-error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

.status-warning {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.status-icon {
  font-size: var(--font-size-sm);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.function-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-xs);
}

.detail-value {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.function-error {
  padding: var(--spacing-sm);
  background: rgba(255, 77, 79, 0.05);
  border-radius: var(--radius-base);
  border-left: 4rpx solid var(--error-color);
}

.error-text {
  font-size: var(--font-size-sm);
  color: var(--error-color);
}

.function-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  padding: 4rpx var(--spacing-xs);
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-small);
}

.badge-text {
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* 建议区域 */
.recommendations {
  margin-bottom: var(--spacing-lg);
}

.recommendations-header {
  margin-bottom: var(--spacing-lg);
}

.recommendations-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-base);
}

.recommendation-priority {
  padding: 4rpx var(--spacing-xs);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
  white-space: nowrap;
}

.priority-high {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

.priority-medium {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.priority-low {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.priority-info {
  background: rgba(46, 124, 232, 0.1);
  color: var(--primary-color);
}

.recommendation-content {
  flex: 1;
}

.recommendation-message {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.recommendation-action {
  display: block;
  font-size: var(--font-size-sm);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-large);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

/* 部署指南弹窗 */
.guide-step {
  margin-bottom: var(--spacing-lg);
}

.step-title {
  display: block;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.step-content {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-loose);
}
