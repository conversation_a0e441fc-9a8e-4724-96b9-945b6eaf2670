// utils/loginDiagnostic.js
// 登录问题诊断工具

const { logger } = require('./logger')

/**
 * 登录诊断工具
 */
class LoginDiagnostic {
  constructor() {
    this.diagnosticResults = []
  }

  /**
   * 执行完整诊断
   */
  async runFullDiagnostic() {
    logger.info('开始登录诊断')
    this.diagnosticResults = []

    // 检查基础环境
    await this.checkBasicEnvironment()
    
    // 检查云开发环境
    await this.checkCloudEnvironment()
    
    // 检查网络连接
    await this.checkNetworkConnection()
    
    // 检查权限设置
    await this.checkPermissions()
    
    // 检查云函数
    await this.checkCloudFunctions()

    return this.generateReport()
  }

  /**
   * 检查基础环境
   */
  async checkBasicEnvironment() {
    try {
      // 检查微信版本
      const systemInfo = wx.getSystemInfoSync()
      this.addResult('微信版本', systemInfo.version, 'info')
      
      // 检查基础库版本
      this.addResult('基础库版本', systemInfo.SDKVersion, 'info')
      
      // 检查平台
      this.addResult('运行平台', systemInfo.platform, 'info')
      
      // 检查是否支持云开发
      if (wx.cloud) {
        this.addResult('云开发支持', '支持', 'success')
      } else {
        this.addResult('云开发支持', '不支持，请升级基础库到2.2.3+', 'error')
      }
      
      // 检查是否支持wx.login
      if (wx.login) {
        this.addResult('wx.login支持', '支持', 'success')
      } else {
        this.addResult('wx.login支持', '不支持', 'error')
      }

    } catch (error) {
      this.addResult('基础环境检查', `失败: ${error.message}`, 'error')
    }
  }

  /**
   * 检查云开发环境
   */
  async checkCloudEnvironment() {
    try {
      if (!wx.cloud) {
        this.addResult('云开发初始化', '云开发不可用', 'error')
        return
      }

      // 检查云开发是否已初始化
      try {
        // 尝试调用一个简单的云开发API
        const result = await wx.cloud.callFunction({
          name: 'test',
          data: {}
        })
        this.addResult('云开发连接', '连接正常', 'success')
      } catch (error) {
        if (error.errCode === -1) {
          this.addResult('云开发初始化', '未初始化或环境ID错误', 'error')
        } else if (error.errCode === 70002) {
          this.addResult('云函数', 'test云函数不存在（正常）', 'warning')
          this.addResult('云开发连接', '连接正常', 'success')
        } else {
          this.addResult('云开发连接', `连接失败: ${error.errMsg}`, 'error')
        }
      }

    } catch (error) {
      this.addResult('云开发环境检查', `失败: ${error.message}`, 'error')
    }
  }

  /**
   * 检查网络连接
   */
  async checkNetworkConnection() {
    try {
      const networkType = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: resolve,
          fail: reject
        })
      })

      if (networkType.networkType === 'none') {
        this.addResult('网络连接', '无网络连接', 'error')
      } else {
        this.addResult('网络连接', `${networkType.networkType}`, 'success')
      }

    } catch (error) {
      this.addResult('网络连接检查', `失败: ${error.message}`, 'error')
    }
  }

  /**
   * 检查权限设置
   */
  async checkPermissions() {
    try {
      // 检查用户信息权限
      const setting = await new Promise((resolve, reject) => {
        wx.getSetting({
          success: resolve,
          fail: reject
        })
      })

      if (setting.authSetting['scope.userInfo']) {
        this.addResult('用户信息权限', '已授权', 'success')
      } else {
        this.addResult('用户信息权限', '未授权（正常，新版本不需要）', 'info')
      }

    } catch (error) {
      this.addResult('权限检查', `失败: ${error.message}`, 'error')
    }
  }

  /**
   * 检查云函数
   */
  async checkCloudFunctions() {
    try {
      // 检查login云函数
      const loginResult = await wx.cloud.callFunction({
        name: 'login',
        data: {
          code: 'test_code',
          loginType: 'test'
        }
      })

      if (loginResult.result) {
        this.addResult('login云函数', '存在且可调用', 'success')
      } else {
        this.addResult('login云函数', '调用异常', 'warning')
      }

    } catch (error) {
      if (error.errCode === 70002) {
        this.addResult('login云函数', '不存在，请部署login云函数', 'error')
      } else {
        this.addResult('login云函数', `调用失败: ${error.errMsg}`, 'error')
      }
    }
  }

  /**
   * 添加诊断结果
   */
  addResult(item, result, level) {
    this.diagnosticResults.push({
      item,
      result,
      level,
      timestamp: new Date().toISOString()
    })
    
    logger.info(`诊断: ${item} - ${result}`)
  }

  /**
   * 生成诊断报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      results: this.diagnosticResults,
      summary: this.generateSummary()
    }

    logger.info('登录诊断完成', report)
    return report
  }

  /**
   * 生成摘要
   */
  generateSummary() {
    const total = this.diagnosticResults.length
    const errors = this.diagnosticResults.filter(r => r.level === 'error').length
    const warnings = this.diagnosticResults.filter(r => r.level === 'warning').length
    const success = this.diagnosticResults.filter(r => r.level === 'success').length

    let status = 'healthy'
    if (errors > 0) {
      status = 'error'
    } else if (warnings > 0) {
      status = 'warning'
    }

    return {
      status,
      total,
      errors,
      warnings,
      success,
      recommendations: this.generateRecommendations()
    }
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = []
    
    this.diagnosticResults.forEach(result => {
      if (result.level === 'error') {
        switch (result.item) {
          case '云开发支持':
            recommendations.push('请升级微信小程序基础库到2.2.3或更高版本')
            break
          case '云开发初始化':
            recommendations.push('请检查云开发环境ID配置，确保已正确初始化')
            break
          case 'login云函数':
            recommendations.push('请部署login云函数到云开发环境')
            break
          case '网络连接':
            recommendations.push('请检查网络连接，确保设备联网')
            break
        }
      }
    })

    if (recommendations.length === 0) {
      recommendations.push('系统状态良好，如仍有问题请查看详细日志')
    }

    return recommendations
  }

  /**
   * 显示诊断报告
   */
  showReport(report) {
    const { summary } = report
    let title = '登录诊断报告'
    let content = `状态: ${summary.status}\n`
    content += `检查项: ${summary.total}\n`
    content += `错误: ${summary.errors}\n`
    content += `警告: ${summary.warnings}\n`
    content += `正常: ${summary.success}\n\n`
    
    if (summary.recommendations.length > 0) {
      content += '建议:\n'
      summary.recommendations.forEach((rec, index) => {
        content += `${index + 1}. ${rec}\n`
      })
    }

    wx.showModal({
      title,
      content,
      showCancel: false,
      confirmText: '知道了'
    })
  }

  /**
   * 快速检查登录状态
   */
  async quickCheck() {
    const issues = []

    // 检查云开发
    if (!wx.cloud) {
      issues.push('云开发不可用，请升级基础库')
    }

    // 检查网络
    try {
      const network = await new Promise((resolve, reject) => {
        wx.getNetworkType({ success: resolve, fail: reject })
      })
      if (network.networkType === 'none') {
        issues.push('无网络连接')
      }
    } catch (error) {
      issues.push('网络检查失败')
    }

    return {
      hasIssues: issues.length > 0,
      issues
    }
  }
}

// 创建全局实例
const loginDiagnostic = new LoginDiagnostic()

module.exports = {
  loginDiagnostic,
  LoginDiagnostic
}
