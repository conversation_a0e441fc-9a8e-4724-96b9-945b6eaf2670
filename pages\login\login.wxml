<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="background-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 登录内容 -->
  <view class="login-content">
    <!-- Logo和标题 -->
    <view class="logo-section">
      <view class="logo-icon">🔬</view>
      <text class="app-title">IVD智能顾问</text>
      <text class="app-subtitle">您的专属研发、注册、销售顾问</text>
    </view>

    <!-- 功能介绍 -->
    <view class="features-section">
      <view class="feature-item">
        <view class="feature-icon">🤖</view>
        <view class="feature-content">
          <text class="feature-title">多AI模型支持</text>
          <text class="feature-desc">GPT-4、<PERSON>、Gemini等多种AI模型</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">🎯</view>
        <view class="feature-content">
          <text class="feature-title">专业领域覆盖</text>
          <text class="feature-desc">研发、注册、销售、质量管理全覆盖</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">💬</view>
        <view class="feature-content">
          <text class="feature-title">智能对话体验</text>
          <text class="feature-desc">上下文理解、专业术语识别</text>
        </view>
      </view>
    </view>

    <!-- 登录按钮区域 -->
    <view class="login-section">
      <view class="login-tips">
        <text>登录后可享受个性化服务</text>
      </view>

      <!-- 微信一键登录 -->
      <button
        class="login-btn wechat-login btn btn-primary btn-large"
        bindtap="onWechatLogin"
        wx:if="{{!isLoggedIn}}"
        hover-class="btn-hover"
      >
        <view class="btn-content">
          <view class="wechat-icon">👤</view>
          <text class="btn-text">微信一键登录</text>
        </view>
      </button>

      <!-- 测试登录按钮（开发用） -->
      <button
        class="login-btn test-login btn btn-outline btn-large"
        bindtap="onTestLogin"
        wx:if="{{!isLoggedIn}}"
        hover-class="btn-hover"
      >
        <view class="btn-content">
          <view class="test-icon">🔧</view>
          <text class="btn-text">测试登录</text>
        </view>
      </button>

      <!-- 已登录状态 -->
      <view class="logged-in-section" wx:if="{{isLoggedIn}}">
        <view class="user-info">
          <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
          <view class="user-details">
            <text class="user-name">{{userInfo.nickName}}</text>
            <text class="user-status">已登录</text>
          </view>
        </view>
        
        <button class="action-btn primary" bindtap="goToHome">
          <text>开始使用</text>
        </button>
        
        <button class="action-btn secondary" bindtap="logout">
          <text>退出登录</text>
        </button>
      </view>

      <!-- 游客模式 -->
      <view class="guest-section" wx:if="{{!isLoggedIn}}">
        <view class="guest-tips">
          <text>也可以先体验基础功能</text>
        </view>
        <button class="guest-btn" bindtap="guestLogin">
          <text>游客模式</text>
        </button>
      </view>

      <!-- 诊断工具 -->
      <view class="diagnostic-section" wx:if="{{!isLoggedIn}}">
        <button class="diagnostic-btn" bindtap="runDiagnostic">
          <text>🔧 登录问题诊断</text>
        </button>
      </view>
    </view>

    <!-- 服务条款 -->
    <view class="terms-section">
      <text class="terms-text">
        登录即表示同意
        <text class="terms-link" bindtap="showUserAgreement">《用户协议》</text>
        和
        <text class="terms-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>

<!-- 用户协议弹窗 -->
<view class="modal-overlay" wx:if="{{showAgreementModal}}" bindtap="hideAgreementModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">用户协议</text>
      <view class="modal-close" bindtap="hideAgreementModal">×</view>
    </view>
    <scroll-view class="modal-body" scroll-y>
      <text class="agreement-text">
        欢迎使用IVD智能顾问小程序！

        1. 服务说明
        本小程序为IVD行业从业者提供专业的AI咨询服务，包括产品研发、注册申报、市场销售等方面的指导。

        2. 用户责任
        - 提供真实、准确的个人信息
        - 合理使用AI咨询服务
        - 不得利用本服务进行违法活动

        3. 服务限制
        - AI回复仅供参考，不构成专业建议
        - 重要决策请咨询相关专业人士
        - 服务可用性可能受到技术限制

        4. 隐私保护
        我们重视您的隐私，详见《隐私政策》

        5. 免责声明
        本服务按"现状"提供，我们不对服务的准确性、完整性或适用性作出保证。

        更新时间：2024年1月1日
      </text>
    </scroll-view>
    <view class="modal-footer">
      <button class="modal-btn" bindtap="hideAgreementModal">我已阅读</button>
    </view>
  </view>
</view>

<!-- 隐私政策弹窗 -->
<view class="modal-overlay" wx:if="{{showPrivacyModal}}" bindtap="hidePrivacyModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">隐私政策</text>
      <view class="modal-close" bindtap="hidePrivacyModal">×</view>
    </view>
    <scroll-view class="modal-body" scroll-y>
      <text class="privacy-text">
        IVD智能顾问隐私政策

        1. 信息收集
        我们可能收集以下信息：
        - 微信授权的基本信息（昵称、头像等）
        - 您的咨询内容和偏好设置
        - 使用统计数据（匿名）

        2. 信息使用
        收集的信息用于：
        - 提供个性化的AI咨询服务
        - 改进产品功能和用户体验
        - 技术支持和客户服务

        3. 信息保护
        - 所有数据采用加密存储
        - 严格限制数据访问权限
        - 不会向第三方出售个人信息

        4. 数据存储
        - 数据存储在微信云开发环境
        - 符合相关法律法规要求
        - 您可以随时删除个人数据

        5. 联系我们
        如有隐私相关问题，请联系：
        邮箱：<EMAIL>

        更新时间：2024年1月1日
      </text>
    </scroll-view>
    <view class="modal-footer">
      <button class="modal-btn" bindtap="hidePrivacyModal">我已阅读</button>
    </view>
  </view>
</view>
