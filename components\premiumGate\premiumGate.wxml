<!-- components/premiumGate/premiumGate.wxml -->
<!-- 付费功能门控组件 -->

<view class="premium-gate-container" wx:if="{{!hasAccess}}">
  <!-- 功能锁定提示 -->
  <view class="premium-gate card gradient-bg">
    <view class="gate-content">
      <view class="lock-icon">🔒</view>
      <text class="gate-title text-inverse">{{title || '高级功能'}}</text>
      <text class="gate-description text-inverse">{{description || '此功能需要付费订阅'}}</text>
      
      <!-- 订阅计划信息 -->
      <view class="plan-info" wx:if="{{requiredPlan}}">
        <text class="plan-text text-inverse">需要订阅：{{planNames[requiredPlan] || requiredPlan}}</text>
      </view>
      
      <!-- 使用限制信息 -->
      <view class="limit-info" wx:if="{{limitType}}">
        <text class="limit-text text-inverse">
          {{limitType === 'daily' ? '今日' : '本月'}}使用次数已达上限
        </text>
        <text class="reset-text text-inverse" wx:if="{{resetTime}}">
          重置时间：{{resetTimeText}}
        </text>
      </view>
      
      <!-- 操作按钮 -->
      <view class="gate-actions">
        <button class="btn btn-secondary btn-large" bindtap="onUpgrade" wx:if="{{showUpgrade}}">
          <text>立即升级</text>
        </button>
        <button class="btn btn-outline btn-large" bindtap="onViewPlans" wx:if="{{showViewPlans}}">
          <text>查看订阅计划</text>
        </button>
        <button class="btn btn-ghost btn-small" bindtap="onDismiss" wx:if="{{dismissible}}">
          <text>稍后再说</text>
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 功能内容（有权限时显示） -->
<view class="premium-content" wx:if="{{hasAccess}}">
  <slot></slot>
</view>

<!-- 使用量提示 -->
<view class="usage-indicator" wx:if="{{hasAccess && showUsage}}">
  <view class="usage-bar">
    <view class="usage-fill" style="width: {{usagePercentage}}%;"></view>
  </view>
  <text class="usage-text text-small text-tertiary">
    {{usageType === 'daily' ? '今日' : '本月'}}已使用 {{usedCount}}/{{totalLimit}}
  </text>
</view>
