/* styles/design-system.wxss */
/* IVD智能顾问 - 小程序设计系统 */

/* ==================== 设计令牌 ==================== */

/* 颜色系统 - 符合小程序设计规范 */
:root {
  /* 主色调 - 微信绿系 */
  --primary-color: #07C160;
  --primary-light: #4DD865;
  --primary-dark: #06AD56;
  --primary-gradient: linear-gradient(135deg, #07C160 0%, #4DD865 100%);

  /* 辅助色 - 科技蓝 */
  --secondary-color: #576B95;
  --secondary-light: #7B8BB2;
  --secondary-dark: #485578;

  /* 功能色 */
  --success-color: #07C160;
  --warning-color: #FA9D3B;
  --error-color: #FA5151;
  --info-color: #10AEFF;

  /* 中性色 - 小程序标准色阶 */
  --text-primary: #000000;
  --text-secondary: #353535;
  --text-tertiary: #888888;
  --text-quaternary: #CCCCCC;
  --text-inverse: #FFFFFF;
  --text-placeholder: #B2B2B2;

  /* 背景色 - 小程序标准背景 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F7F7F7;
  --bg-tertiary: #EEEEEE;
  --bg-quaternary: #E5E5E5;
  --bg-overlay: rgba(0, 0, 0, 0.6);
  --bg-mask: rgba(0, 0, 0, 0.4);

  /* 边框色 - 小程序标准边框 */
  --border-light: #EEEEEE;
  --border-base: #E5E5E5;
  --border-dark: #CCCCCC;
  
  /* 阴影 - 小程序轻量化阴影 */
  --shadow-light: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  --shadow-base: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  --shadow-card: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);

  /* 圆角 - 小程序标准圆角 */
  --radius-small: 4rpx;
  --radius-base: 8rpx;
  --radius-large: 12rpx;
  --radius-xl: 16rpx;
  --radius-round: 50%;
  --radius-button: 6rpx;

  /* 间距 - 小程序标准间距 */
  --spacing-xs: 4rpx;
  --spacing-sm: 8rpx;
  --spacing-md: 12rpx;
  --spacing-lg: 16rpx;
  --spacing-xl: 24rpx;
  --spacing-xxl: 32rpx;
  --spacing-page: 16rpx;
  --spacing-section: 24rpx;

  /* 字体大小 - 小程序标准字号 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;
  --font-size-title: 34rpx;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-base: 1.4;
  --line-height-loose: 1.6;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==================== 基础样式 ==================== */

/* 页面容器 - 小程序标准布局 */
.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', Arial, sans-serif;
  padding-bottom: env(safe-area-inset-bottom);
}

.content-container {
  padding: var(--spacing-page);
}

.section-container {
  margin-bottom: var(--spacing-section);
}

/* 卡片组件 - 小程序风格 */
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-base);
  border: 1rpx solid var(--border-light);
}

.card-hover {
  transition: all var(--transition-base);
}

.card-hover:active {
  background: var(--bg-tertiary);
  transform: scale(0.98);
}

.card-plain {
  box-shadow: none;
  border: none;
}

.card-bordered {
  border: 1rpx solid var(--border-base);
}

/* 渐变背景 */
.gradient-bg {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
  color: var(--text-inverse);
}

/* ==================== 文字系统 ==================== */

/* 标题 */
.title-primary {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.title-secondary {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.title-tertiary {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-base);
}

/* 正文 */
.text-body {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: var(--line-height-base);
}

.text-caption {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-base);
}

.text-small {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  line-height: var(--line-height-base);
}

/* 文字颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-inverse { color: var(--text-inverse); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

/* ==================== 按钮系统 ==================== */

/* 基础按钮 - 小程序标准按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: var(--radius-button);
  font-size: var(--font-size-base);
  font-weight: 400;
  text-align: center;
  transition: all var(--transition-base);
  border: none;
  position: relative;
  overflow: hidden;
  line-height: 1.4;
  min-height: 80rpx;
  box-sizing: border-box;
}

.btn::after {
  border: none;
}

/* 按钮尺寸 */
.btn-mini {
  padding: 8rpx 16rpx;
  font-size: var(--font-size-sm);
  min-height: 60rpx;
  border-radius: var(--radius-small);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: var(--font-size-sm);
  min-height: 70rpx;
}

.btn-large {
  padding: 20rpx 40rpx;
  font-size: var(--font-size-lg);
  min-height: 96rpx;
  border-radius: var(--radius-base);
}

/* 主要按钮 - 微信绿 */
.btn-primary {
  background: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:active {
  background: var(--primary-dark);
  transform: scale(0.98);
}

/* 次要按钮 */
.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-inverse);
}

.btn-secondary:active {
  background: var(--secondary-dark);
  transform: scale(0.98);
}

/* 默认按钮 */
.btn-default {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-base);
}

.btn-default:active {
  background: var(--bg-tertiary);
  transform: scale(0.98);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:hover,
.btn-outline.btn-hover {
  background: var(--primary-color);
  color: var(--text-inverse);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 2rpx solid var(--border-base);
}

.btn-ghost:hover,
.btn-ghost.btn-hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.btn-text:hover,
.btn-text.btn-hover {
  background: rgba(46, 124, 232, 0.06);
}

/* 危险按钮 */
.btn-danger {
  background: var(--error-color);
  color: var(--text-inverse);
}

.btn-danger:hover,
.btn-danger.btn-hover {
  background: #FF7875;
}

/* 禁用状态 */
.btn:disabled,
.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ==================== 输入组件 ==================== */

.input-group {
  margin-bottom: var(--spacing-lg);
}

.input-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.input-field {
  width: 100%;
  padding: var(--spacing-md);
  border: 2rpx solid var(--border-base);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: border-color var(--transition-base);
}

.input-field:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-field.error {
  border-color: var(--error-color);
}

/* ==================== 状态指示器 ==================== */

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4rpx var(--spacing-xs);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-online {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.status-offline {
  background: rgba(140, 140, 140, 0.1);
  color: var(--text-tertiary);
}

.status-error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

/* ==================== 动画效果 ==================== */

.fade-in {
  animation: fadeIn var(--transition-base) ease-out;
}

.slide-up {
  animation: slideUp var(--transition-base) ease-out;
}

.scale-in {
  animation: scaleIn var(--transition-base) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(40rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* ==================== 响应式设计 ==================== */

/* 小屏幕适配 */
@media (max-width: 375px) {
  .content-container {
    padding: var(--spacing-sm);
  }
  
  .card {
    padding: var(--spacing-md);
  }
  
  .btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* 大屏幕适配 */
@media (min-width: 768px) {
  .content-container {
    max-width: 750rpx;
    margin: 0 auto;
  }
}
