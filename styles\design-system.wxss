/* styles/design-system.wxss */
/* IVD智能顾问 - 统一设计系统 */

/* ==================== 设计令牌 ==================== */

/* 颜色系统 */
:root {
  /* 主色调 - 医疗蓝 */
  --primary-color: #2E7CE8;
  --primary-light: #5A9EF4;
  --primary-dark: #1F5FBF;
  --primary-gradient: linear-gradient(135deg, #2E7CE8 0%, #5A9EF4 100%);
  
  /* 辅助色 - 科技绿 */
  --secondary-color: #00A870;
  --secondary-light: #33C48C;
  --secondary-dark: #008A5D;
  
  /* 功能色 */
  --success-color: #52C41A;
  --warning-color: #FAAD14;
  --error-color: #FF4D4F;
  --info-color: #1890FF;
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8C8C8C;
  --text-quaternary: #BFBFBF;
  --text-inverse: #FFFFFF;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-tertiary: #F5F5F5;
  --bg-quaternary: #F0F0F0;
  --bg-overlay: rgba(0, 0, 0, 0.45);
  
  /* 边框色 */
  --border-light: #F0F0F0;
  --border-base: #D9D9D9;
  --border-dark: #BFBFBF;
  
  /* 阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-dark: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
  
  /* 圆角 */
  --radius-small: 8rpx;
  --radius-base: 12rpx;
  --radius-large: 16rpx;
  --radius-xl: 24rpx;
  --radius-round: 50%;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-xxl: 48rpx;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 42rpx;
  --font-size-xxxl: 48rpx;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-base: 1.4;
  --line-height-loose: 1.6;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==================== 基础样式 ==================== */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.content-container {
  padding: var(--spacing-md);
}

/* 卡片组件 */
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: box-shadow var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-base);
}

.card.elevated {
  box-shadow: var(--shadow-base);
}

.card.elevated:hover {
  box-shadow: var(--shadow-dark);
}

/* 渐变背景 */
.gradient-bg {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
  color: var(--text-inverse);
}

/* ==================== 文字系统 ==================== */

/* 标题 */
.title-primary {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.title-secondary {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.title-tertiary {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-base);
}

/* 正文 */
.text-body {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: var(--line-height-base);
}

.text-caption {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-base);
}

.text-small {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  line-height: var(--line-height-base);
}

/* 文字颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-inverse { color: var(--text-inverse); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

/* ==================== 按钮系统 ==================== */

/* 基础按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  transition: all var(--transition-base);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn::after {
  border: none;
}

/* 按钮尺寸 */
.btn-small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-large);
}

/* 主要按钮 */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-light);
}

.btn-primary:hover,
.btn-primary.btn-hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-2rpx);
}

/* 次要按钮 */
.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-inverse);
}

.btn-secondary:hover,
.btn-secondary.btn-hover {
  background: var(--secondary-dark);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:hover,
.btn-outline.btn-hover {
  background: var(--primary-color);
  color: var(--text-inverse);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 2rpx solid var(--border-base);
}

.btn-ghost:hover,
.btn-ghost.btn-hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.btn-text:hover,
.btn-text.btn-hover {
  background: rgba(46, 124, 232, 0.06);
}

/* 危险按钮 */
.btn-danger {
  background: var(--error-color);
  color: var(--text-inverse);
}

.btn-danger:hover,
.btn-danger.btn-hover {
  background: #FF7875;
}

/* 禁用状态 */
.btn:disabled,
.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ==================== 输入组件 ==================== */

.input-group {
  margin-bottom: var(--spacing-lg);
}

.input-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.input-field {
  width: 100%;
  padding: var(--spacing-md);
  border: 2rpx solid var(--border-base);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: border-color var(--transition-base);
}

.input-field:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-field.error {
  border-color: var(--error-color);
}

/* ==================== 状态指示器 ==================== */

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4rpx var(--spacing-xs);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-online {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.status-offline {
  background: rgba(140, 140, 140, 0.1);
  color: var(--text-tertiary);
}

.status-error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

/* ==================== 动画效果 ==================== */

.fade-in {
  animation: fadeIn var(--transition-base) ease-out;
}

.slide-up {
  animation: slideUp var(--transition-base) ease-out;
}

.scale-in {
  animation: scaleIn var(--transition-base) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(40rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* ==================== 响应式设计 ==================== */

/* 小屏幕适配 */
@media (max-width: 375px) {
  .content-container {
    padding: var(--spacing-sm);
  }
  
  .card {
    padding: var(--spacing-md);
  }
  
  .btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* 大屏幕适配 */
@media (min-width: 768px) {
  .content-container {
    max-width: 750rpx;
    margin: 0 auto;
  }
}
