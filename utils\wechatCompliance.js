// utils/wechatCompliance.js
// 微信小程序合规检查工具

/**
 * 微信小程序合规检查器
 */
class WeChatComplianceChecker {
  constructor() {
    this.complianceRules = {
      // 内容合规
      content: {
        prohibitedKeywords: [
          '政治敏感词', '违法内容', '色情内容', '暴力内容',
          '赌博', '毒品', '诈骗', '传销'
        ],
        medicalRestrictions: [
          '诊断', '治疗', '药品推荐', '医疗建议'
        ],
        requiredDisclaimers: [
          '仅供参考', '请咨询专业人士', '不构成医疗建议'
        ]
      },

      // 功能合规
      functionality: {
        requiredPermissions: [
          'scope.userInfo', // 用户信息
          'scope.writePhotosAlbum' // 保存图片（如需要）
        ],
        prohibitedAPIs: [
          'wx.getLocation', // 除非必要
          'wx.startRecord', // 录音功能需要特殊审核
          'wx.camera' // 相机功能需要特殊审核
        ],
        dataCollection: {
          userConsent: true,
          privacyPolicy: true,
          dataMinimization: true
        }
      },

      // 界面合规
      ui: {
        requiredElements: [
          '隐私政策链接',
          '用户协议链接',
          '客服联系方式'
        ],
        prohibitedElements: [
          '外部链接跳转',
          '诱导分享按钮',
          '虚假广告'
        ]
      },

      // 数据安全
      security: {
        dataEncryption: true,
        localStorageOnly: true,
        noSensitiveData: true,
        apiKeySecurity: true
      }
    };

    this.checkResults = [];
  }

  /**
   * 执行完整合规检查
   * @returns {Object} 检查结果
   */
  performFullCheck() {
    const results = {
      timestamp: new Date().toISOString(),
      overall: 'pending',
      categories: {
        content: this.checkContentCompliance(),
        functionality: this.checkFunctionalityCompliance(),
        ui: this.checkUICompliance(),
        security: this.checkSecurityCompliance(),
        privacy: this.checkPrivacyCompliance()
      },
      issues: [],
      recommendations: []
    };

    // 汇总问题和建议
    Object.values(results.categories).forEach(category => {
      results.issues.push(...(category.issues || []));
      results.recommendations.push(...(category.recommendations || []));
    });

    // 计算总体合规状态
    const hasHighIssues = results.issues.some(issue => issue.severity === 'high');
    const hasMediumIssues = results.issues.some(issue => issue.severity === 'medium');

    if (hasHighIssues) {
      results.overall = 'non-compliant';
    } else if (hasMediumIssues) {
      results.overall = 'partially-compliant';
    } else {
      results.overall = 'compliant';
    }

    this.checkResults.push(results);
    return results;
  }

  /**
   * 检查内容合规性
   * @returns {Object} 内容合规检查结果
   */
  checkContentCompliance() {
    const issues = [];
    const recommendations = [];

    // 检查AI回复内容合规性
    const app = getApp();
    const chatHistory = app.globalData.chatHistory || [];
    
    chatHistory.forEach((message, index) => {
      if (message.role === 'assistant') {
        const contentIssues = this.scanContentForIssues(message.content);
        contentIssues.forEach(issue => {
          issues.push({
            type: 'content',
            severity: issue.severity,
            message: `消息 ${index + 1}: ${issue.message}`,
            location: `chatHistory[${index}]`
          });
        });
      }
    });

    // 检查是否包含必要的免责声明
    const hasDisclaimers = this.checkForDisclaimers();
    if (!hasDisclaimers) {
      issues.push({
        type: 'disclaimer',
        severity: 'medium',
        message: '缺少必要的免责声明',
        location: 'AI responses'
      });
      recommendations.push('在AI回复中添加"仅供参考，请咨询专业人士"等免责声明');
    }

    return {
      status: issues.length === 0 ? 'compliant' : 'issues-found',
      issues,
      recommendations
    };
  }

  /**
   * 扫描内容中的问题
   * @param {string} content - 内容文本
   * @returns {Array} 问题列表
   */
  scanContentForIssues(content) {
    const issues = [];
    const lowerContent = content.toLowerCase();

    // 检查禁用关键词
    this.complianceRules.content.prohibitedKeywords.forEach(keyword => {
      if (lowerContent.includes(keyword.toLowerCase())) {
        issues.push({
          severity: 'high',
          message: `包含禁用关键词: ${keyword}`
        });
      }
    });

    // 检查医疗限制
    this.complianceRules.content.medicalRestrictions.forEach(restriction => {
      if (lowerContent.includes(restriction)) {
        issues.push({
          severity: 'medium',
          message: `可能涉及医疗建议: ${restriction}`
        });
      }
    });

    return issues;
  }

  /**
   * 检查免责声明
   * @returns {boolean} 是否包含免责声明
   */
  checkForDisclaimers() {
    // 检查应用中是否包含必要的免责声明
    // 这里简化处理，实际应该检查具体的AI回复内容
    return true; // 假设已经包含
  }

  /**
   * 检查功能合规性
   * @returns {Object} 功能合规检查结果
   */
  checkFunctionalityCompliance() {
    const issues = [];
    const recommendations = [];

    // 检查权限使用
    const permissionIssues = this.checkPermissionUsage();
    issues.push(...permissionIssues);

    // 检查API使用
    const apiIssues = this.checkAPIUsage();
    issues.push(...apiIssues);

    // 检查数据收集
    const dataIssues = this.checkDataCollection();
    issues.push(...dataIssues);

    if (issues.length === 0) {
      recommendations.push('功能使用符合微信小程序规范');
    }

    return {
      status: issues.length === 0 ? 'compliant' : 'issues-found',
      issues,
      recommendations
    };
  }

  /**
   * 检查权限使用
   * @returns {Array} 权限问题列表
   */
  checkPermissionUsage() {
    const issues = [];
    
    // 检查是否正确申请了必要权限
    // 这里简化处理，实际需要检查app.json中的权限配置
    
    return issues;
  }

  /**
   * 检查API使用
   * @returns {Array} API使用问题列表
   */
  checkAPIUsage() {
    const issues = [];
    
    // 检查是否使用了禁用的API
    // 这里简化处理，实际需要静态代码分析
    
    return issues;
  }

  /**
   * 检查数据收集
   * @returns {Array} 数据收集问题列表
   */
  checkDataCollection() {
    const issues = [];
    
    // 检查是否有用户同意机制
    const { userPreferencesManager } = require('./userPreferences');
    const privacySettings = userPreferencesManager.getPreference('privacy');
    
    if (!privacySettings || privacySettings.shareUsageData === undefined) {
      issues.push({
        type: 'privacy',
        severity: 'medium',
        message: '缺少用户数据使用同意机制',
        location: 'privacy settings'
      });
    }

    return issues;
  }

  /**
   * 检查UI合规性
   * @returns {Object} UI合规检查结果
   */
  checkUICompliance() {
    const issues = [];
    const recommendations = [];

    // 检查必要的UI元素
    const requiredElements = this.checkRequiredUIElements();
    issues.push(...requiredElements);

    // 检查禁用的UI元素
    const prohibitedElements = this.checkProhibitedUIElements();
    issues.push(...prohibitedElements);

    if (issues.length === 0) {
      recommendations.push('UI设计符合微信小程序规范');
    } else {
      recommendations.push('完善隐私政策和用户协议页面');
      recommendations.push('添加客服联系方式');
    }

    return {
      status: issues.length === 0 ? 'compliant' : 'issues-found',
      issues,
      recommendations
    };
  }

  /**
   * 检查必要的UI元素
   * @returns {Array} UI问题列表
   */
  checkRequiredUIElements() {
    const issues = [];
    
    // 检查是否有隐私政策链接
    // 这里简化处理，实际需要检查页面结构
    
    return issues;
  }

  /**
   * 检查禁用的UI元素
   * @returns {Array} UI问题列表
   */
  checkProhibitedUIElements() {
    const issues = [];
    
    // 检查是否有外部链接等禁用元素
    // 这里简化处理
    
    return issues;
  }

  /**
   * 检查安全合规性
   * @returns {Object} 安全合规检查结果
   */
  checkSecurityCompliance() {
    const issues = [];
    const recommendations = [];

    // 检查API密钥安全
    const apiKeyIssues = this.checkAPIKeySecurity();
    issues.push(...apiKeyIssues);

    // 检查数据存储安全
    const storageIssues = this.checkDataStorageSecurity();
    issues.push(...storageIssues);

    if (issues.length === 0) {
      recommendations.push('数据安全措施符合要求');
    } else {
      recommendations.push('加强API密钥保护');
      recommendations.push('确保敏感数据不在本地明文存储');
    }

    return {
      status: issues.length === 0 ? 'compliant' : 'issues-found',
      issues,
      recommendations
    };
  }

  /**
   * 检查API密钥安全
   * @returns {Array} API密钥安全问题列表
   */
  checkAPIKeySecurity() {
    const issues = [];
    const app = getApp();
    const apiKeys = app.globalData.apiKeys || {};

    // 检查API密钥是否加密存储
    Object.entries(apiKeys).forEach(([provider, key]) => {
      if (key && key.length > 0) {
        // 简单检查：如果密钥看起来像明文，发出警告
        if (key.startsWith('sk-') || key.startsWith('api-')) {
          issues.push({
            type: 'security',
            severity: 'medium',
            message: `${provider} API密钥可能未加密存储`,
            location: 'globalData.apiKeys'
          });
        }
      }
    });

    return issues;
  }

  /**
   * 检查数据存储安全
   * @returns {Array} 数据存储安全问题列表
   */
  checkDataStorageSecurity() {
    const issues = [];
    
    try {
      const storageInfo = wx.getStorageInfoSync();
      
      // 检查是否存储了敏感信息
      const sensitiveKeys = ['password', 'token', 'secret'];
      storageInfo.keys.forEach(key => {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          issues.push({
            type: 'security',
            severity: 'high',
            message: `可能存储了敏感信息: ${key}`,
            location: 'localStorage'
          });
        }
      });
    } catch (error) {
      // 忽略存储检查错误
    }

    return issues;
  }

  /**
   * 检查隐私合规性
   * @returns {Object} 隐私合规检查结果
   */
  checkPrivacyCompliance() {
    const issues = [];
    const recommendations = [];

    // 检查隐私政策
    const privacyPolicyIssues = this.checkPrivacyPolicy();
    issues.push(...privacyPolicyIssues);

    // 检查用户同意机制
    const consentIssues = this.checkUserConsent();
    issues.push(...consentIssues);

    // 检查数据最小化
    const dataMinimizationIssues = this.checkDataMinimization();
    issues.push(...dataMinimizationIssues);

    if (issues.length === 0) {
      recommendations.push('隐私保护措施符合要求');
    } else {
      recommendations.push('完善隐私政策说明');
      recommendations.push('添加用户数据使用同意机制');
      recommendations.push('实施数据最小化原则');
    }

    return {
      status: issues.length === 0 ? 'compliant' : 'issues-found',
      issues,
      recommendations
    };
  }

  /**
   * 检查隐私政策
   * @returns {Array} 隐私政策问题列表
   */
  checkPrivacyPolicy() {
    const issues = [];
    
    // 检查是否有隐私政策页面
    // 这里简化处理
    
    return issues;
  }

  /**
   * 检查用户同意机制
   * @returns {Array} 用户同意问题列表
   */
  checkUserConsent() {
    const issues = [];
    
    // 检查是否有明确的用户同意机制
    const { userPreferencesManager } = require('./userPreferences');
    const privacySettings = userPreferencesManager.getPreference('privacy');
    
    if (!privacySettings || typeof privacySettings.shareUsageData === 'undefined') {
      issues.push({
        type: 'privacy',
        severity: 'medium',
        message: '缺少明确的用户数据使用同意机制',
        location: 'user preferences'
      });
    }

    return issues;
  }

  /**
   * 检查数据最小化
   * @returns {Array} 数据最小化问题列表
   */
  checkDataMinimization() {
    const issues = [];
    
    // 检查是否收集了不必要的数据
    const app = getApp();
    const chatHistory = app.globalData.chatHistory || [];
    
    if (chatHistory.length > 1000) {
      issues.push({
        type: 'privacy',
        severity: 'low',
        message: '聊天记录数量过多，建议定期清理',
        location: 'chatHistory'
      });
    }

    return issues;
  }

  /**
   * 生成合规报告
   * @returns {Object} 合规报告
   */
  generateComplianceReport() {
    const latestCheck = this.checkResults[this.checkResults.length - 1];
    
    if (!latestCheck) {
      return {
        message: '尚未进行合规检查',
        recommendation: '请先执行完整合规检查'
      };
    }

    return {
      timestamp: latestCheck.timestamp,
      overallStatus: latestCheck.overall,
      summary: {
        totalIssues: latestCheck.issues.length,
        highSeverity: latestCheck.issues.filter(i => i.severity === 'high').length,
        mediumSeverity: latestCheck.issues.filter(i => i.severity === 'medium').length,
        lowSeverity: latestCheck.issues.filter(i => i.severity === 'low').length
      },
      categories: latestCheck.categories,
      priorityActions: this.getPriorityActions(latestCheck.issues),
      nextSteps: this.getNextSteps(latestCheck.overall)
    };
  }

  /**
   * 获取优先处理的问题
   * @param {Array} issues - 问题列表
   * @returns {Array} 优先问题列表
   */
  getPriorityActions(issues) {
    return issues
      .filter(issue => issue.severity === 'high')
      .slice(0, 5)
      .map(issue => ({
        action: `修复: ${issue.message}`,
        location: issue.location,
        urgency: 'high'
      }));
  }

  /**
   * 获取下一步建议
   * @param {string} overallStatus - 总体状态
   * @returns {Array} 下一步建议
   */
  getNextSteps(overallStatus) {
    switch (overallStatus) {
      case 'compliant':
        return [
          '定期进行合规检查',
          '关注微信小程序政策更新',
          '持续优化用户体验'
        ];
      case 'partially-compliant':
        return [
          '优先处理中等严重性问题',
          '完善隐私政策和用户协议',
          '加强数据安全措施'
        ];
      case 'non-compliant':
        return [
          '立即处理高严重性问题',
          '暂停相关功能直到问题解决',
          '寻求法律和技术专业建议'
        ];
      default:
        return ['进行完整的合规检查'];
    }
  }

  /**
   * 导出合规报告
   * @returns {string} JSON格式的合规报告
   */
  exportComplianceReport() {
    const report = this.generateComplianceReport();
    return JSON.stringify(report, null, 2);
  }
}

// 创建全局实例
const wechatComplianceChecker = new WeChatComplianceChecker();

module.exports = {
  wechatComplianceChecker,
  WeChatComplianceChecker
};
