<!--pages/cloudStatus/cloudStatus.wxml-->
<view class="page-container">
  <!-- 头部 -->
  <view class="header-section">
    <view class="status-header card gradient-bg">
      <view class="header-content">
        <text class="header-title text-inverse">云函数状态</text>
        <text class="header-subtitle text-inverse">系统功能可用性检查</text>
        <view class="last-check text-inverse" wx:if="{{lastCheckTime}}">
          <text>最后检查: {{lastCheckTime}}</text>
        </view>
      </view>
      <view class="header-icon">☁️</view>
    </view>
  </view>

  <!-- 状态概览 -->
  <view class="content-container">
    <view class="status-summary card">
      <view class="summary-header">
        <text class="summary-title">状态概览</text>
        <button class="btn btn-outline btn-small" bindtap="refreshStatus">
          <text>刷新</text>
        </button>
      </view>
      <view class="summary-grid">
        <view class="summary-item">
          <text class="summary-value">{{statusSummary.total}}</text>
          <text class="summary-label text-secondary">总计</text>
        </view>
        <view class="summary-item">
          <text class="summary-value success">{{statusSummary.available}}</text>
          <text class="summary-label text-secondary">可用</text>
        </view>
        <view class="summary-item">
          <text class="summary-value error">{{statusSummary.unavailable}}</text>
          <text class="summary-label text-secondary">不可用</text>
        </view>
        <view class="summary-item">
          <text class="summary-value warning">{{statusSummary.required_missing}}</text>
          <text class="summary-label text-secondary">必需缺失</text>
        </view>
      </view>
    </view>

    <!-- 云函数列表 -->
    <view class="functions-list">
      <view class="section-title">
        <text>云函数详情</text>
      </view>
      
      <view class="function-item card" wx:for="{{functionList}}" wx:key="name">
        <view class="function-header">
          <view class="function-info">
            <text class="function-name">{{item.name}}</text>
            <text class="function-desc text-secondary">{{item.description}}</text>
          </view>
          <view class="function-status status-{{item.status}}">
            <text class="status-icon">{{item.statusIcon}}</text>
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>
        
        <view class="function-details" wx:if="{{item.available}}">
          <view class="detail-item" wx:if="{{item.responseTime}}">
            <text class="detail-label text-tertiary">响应时间:</text>
            <text class="detail-value">{{item.responseTime}}ms</text>
          </view>
          <view class="detail-item" wx:if="{{item.lastCheck}}">
            <text class="detail-label text-tertiary">最后检查:</text>
            <text class="detail-value">{{item.lastCheck}}</text>
          </view>
        </view>
        
        <view class="function-error" wx:if="{{!item.available && item.error}}">
          <text class="error-text">{{item.error}}</text>
        </view>
        
        <view class="function-badge" wx:if="{{item.required}}">
          <text class="badge-text">必需</text>
        </view>
      </view>
    </view>

    <!-- 建议和操作 -->
    <view class="recommendations card" wx:if="{{recommendations.length > 0}}">
      <view class="recommendations-header">
        <text class="recommendations-title">修复建议</text>
      </view>
      <view class="recommendation-list">
        <view class="recommendation-item" wx:for="{{recommendations}}" wx:key="index">
          <view class="recommendation-priority priority-{{item.priority}}">
            <text>{{item.priorityText}}</text>
          </view>
          <view class="recommendation-content">
            <text class="recommendation-message">{{item.message}}</text>
            <text class="recommendation-action text-secondary">{{item.action}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-primary btn-large" bindtap="refreshStatus" wx:if="{{!isLoading}}">
        <text>重新检查</text>
      </button>
      
      <button class="btn btn-outline btn-large" bindtap="showDeploymentGuide">
        <text>部署指南</text>
      </button>
      
      <button class="btn btn-ghost btn-large" bindtap="goBack">
        <text>返回</text>
      </button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" wx:if="{{isLoading}}">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">检查中...</text>
      </view>
    </view>
  </view>
</view>

<!-- 部署指南弹窗 -->
<view class="modal-overlay" wx:if="{{showGuideModal}}" bindtap="hideGuideModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">云函数部署指南</text>
      <view class="modal-close" bindtap="hideGuideModal">×</view>
    </view>
    <view class="modal-body">
      <view class="guide-step">
        <text class="step-title">1. 检查云开发环境</text>
        <text class="step-content">确保已开通云开发并配置正确的环境ID</text>
      </view>
      <view class="guide-step">
        <text class="step-title">2. 部署必需云函数</text>
        <text class="step-content">右键点击 cloudfunctions/login，选择"上传并部署"</text>
      </view>
      <view class="guide-step">
        <text class="step-title">3. 部署可选云函数</text>
        <text class="step-content">根据需要部署 payment、userdata 等云函数</text>
      </view>
      <view class="guide-step">
        <text class="step-title">4. 验证部署结果</text>
        <text class="step-content">使用此页面检查云函数状态</text>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-primary" bindtap="hideGuideModal">知道了</button>
    </view>
  </view>
</view>
