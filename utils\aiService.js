// utils/aiService.js
// AI服务统一接口

const app = getApp();
const { ivdPromptManager } = require('./ivdPrompts');
const { ivdRegulationManager } = require('./ivdRegulations');

/**
 * AI服务管理器
 */
class AIServiceManager {
  constructor() {
    this.providers = {
      openai: new OpenAIService(),
      anthropic: new AnthropicService(),
      google: new GoogleService(),
      local: new LocalService()
    };
    
    this.defaultProvider = 'openai';
    this.fallbackProviders = ['openai', 'anthropic', 'google', 'local'];
  }

  /**
   * 发送消息到AI服务
   * @param {string} message - 用户消息
   * @param {string} modelId - 模型ID
   * @param {Object} options - 额外选项
   * @returns {Promise<string>} AI回复
   */
  async sendMessage(message, modelId = 'gpt-3.5-turbo', options = {}) {
    const provider = this.getProviderByModel(modelId);
    
    try {
      // 首先尝试指定的提供商
      const response = await this.providers[provider].sendMessage(message, modelId, options);
      return response;
    } catch (error) {
      console.error(`${provider} 服务失败:`, error);
      
      // 如果失败，尝试备用服务
      return await this.tryFallbackProviders(message, options);
    }
  }

  /**
   * 根据模型ID获取提供商
   * @param {string} modelId - 模型ID
   * @returns {string} 提供商名称
   */
  getProviderByModel(modelId) {
    if (modelId.startsWith('gpt')) return 'openai';
    if (modelId.startsWith('claude')) return 'anthropic';
    if (modelId.startsWith('gemini')) return 'google';
    return 'local';
  }

  /**
   * 尝试备用提供商
   * @param {string} message - 用户消息
   * @param {Object} options - 选项
   * @returns {Promise<string>} AI回复
   */
  async tryFallbackProviders(message, options) {
    for (const providerName of this.fallbackProviders) {
      try {
        const provider = this.providers[providerName];
        if (provider.isAvailable()) {
          const response = await provider.sendMessage(message, null, options);
          return response;
        }
      } catch (error) {
        console.error(`备用服务 ${providerName} 也失败:`, error);
        continue;
      }
    }
    
    // 所有服务都失败，返回本地回复
    return this.providers.local.sendMessage(message, null, options);
  }

  /**
   * 检查服务状态
   * @param {string} provider - 提供商名称
   * @returns {Promise<boolean>} 是否可用
   */
  async checkServiceStatus(provider) {
    try {
      return await this.providers[provider].checkStatus();
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取所有服务状态
   * @returns {Promise<Object>} 服务状态对象
   */
  async getAllServiceStatus() {
    const status = {};
    for (const [name, provider] of Object.entries(this.providers)) {
      status[name] = await this.checkServiceStatus(name);
    }
    return status;
  }
}

/**
 * OpenAI服务
 */
class OpenAIService {
  constructor() {
    this.baseURL = 'https://api.openai.com/v1';
    this.models = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'];
  }

  async sendMessage(message, modelId = 'gpt-3.5-turbo', options = {}) {
    const apiKey = app.globalData.apiKeys.openai;
    
    if (!apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    // 增强用户消息
    const enhancedMessage = ivdPromptManager.enhanceUserMessage(message, options.category);

    const requestData = {
      model: modelId,
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt(options.category, options.subCategory)
        },
        {
          role: 'user',
          content: enhancedMessage
        }
      ],
      max_tokens: options.maxTokens || 1000,
      temperature: options.temperature || 0.7
    };

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseURL}/chat/completions`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        data: requestData,
        success: (res) => {
          if (res.statusCode === 200 && res.data.choices && res.data.choices.length > 0) {
            resolve(res.data.choices[0].message.content);
          } else {
            reject(new Error('OpenAI API响应异常'));
          }
        },
        fail: (error) => {
          reject(new Error(`OpenAI API请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  isAvailable() {
    return !!app.globalData.apiKeys.openai;
  }

  async checkStatus() {
    if (!this.isAvailable()) return false;
    
    try {
      // 发送一个简单的测试请求
      await this.sendMessage('Hello', 'gpt-3.5-turbo', { maxTokens: 10 });
      return true;
    } catch (error) {
      return false;
    }
  }

  getSystemPrompt(category, subCategory = null) {
    // 使用增强的IVD专业提示词
    return ivdPromptManager.getSystemPrompt(category, subCategory);
  }
}

/**
 * Anthropic (Claude) 服务
 */
class AnthropicService {
  constructor() {
    this.baseURL = 'https://api.anthropic.com/v1';
    this.models = ['claude-3-sonnet', 'claude-3-opus'];
  }

  async sendMessage(message, modelId = 'claude-3-sonnet', options = {}) {
    const apiKey = app.globalData.apiKeys.anthropic;
    
    if (!apiKey) {
      throw new Error('Anthropic API密钥未配置');
    }

    // Claude API格式
    const requestData = {
      model: modelId,
      max_tokens: options.maxTokens || 1000,
      messages: [
        {
          role: 'user',
          content: `${this.getSystemPrompt(options.category)}\n\n${message}`
        }
      ]
    };

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseURL}/messages`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01'
        },
        data: requestData,
        success: (res) => {
          if (res.statusCode === 200 && res.data.content && res.data.content.length > 0) {
            resolve(res.data.content[0].text);
          } else {
            reject(new Error('Claude API响应异常'));
          }
        },
        fail: (error) => {
          reject(new Error(`Claude API请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  isAvailable() {
    return !!app.globalData.apiKeys.anthropic;
  }

  async checkStatus() {
    return this.isAvailable();
  }

  getSystemPrompt(category) {
    return new OpenAIService().getSystemPrompt(category);
  }
}

/**
 * Google (Gemini) 服务
 */
class GoogleService {
  constructor() {
    this.baseURL = 'https://generativelanguage.googleapis.com/v1beta';
    this.models = ['gemini-pro', 'gemini-pro-vision'];
  }

  async sendMessage(message, modelId = 'gemini-pro', options = {}) {
    const apiKey = app.globalData.apiKeys.google;
    
    if (!apiKey) {
      throw new Error('Google API密钥未配置');
    }

    const requestData = {
      contents: [
        {
          parts: [
            {
              text: `${this.getSystemPrompt(options.category)}\n\n${message}`
            }
          ]
        }
      ],
      generationConfig: {
        maxOutputTokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7
      }
    };

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseURL}/models/${modelId}:generateContent?key=${apiKey}`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: requestData,
        success: (res) => {
          if (res.statusCode === 200 && res.data.candidates && res.data.candidates.length > 0) {
            const content = res.data.candidates[0].content.parts[0].text;
            resolve(content);
          } else {
            reject(new Error('Gemini API响应异常'));
          }
        },
        fail: (error) => {
          reject(new Error(`Gemini API请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  isAvailable() {
    return !!app.globalData.apiKeys.google;
  }

  async checkStatus() {
    return this.isAvailable();
  }

  getSystemPrompt(category) {
    return new OpenAIService().getSystemPrompt(category);
  }
}

/**
 * 本地服务（备用方案）
 */
class LocalService {
  constructor() {
    this.responses = {
      rd: [
        'IVD产品研发是一个复杂的过程，需要从市场需求分析开始，进行技术可行性评估，然后进行产品设计和开发。关键步骤包括：需求分析、技术评估、设计开发、原型制作、验证测试等。',
        '在产品研发过程中，质量管理体系的建立至关重要。建议按照ISO13485标准建立完善的质量管理体系，确保产品开发过程的可控性和可追溯性。'
      ],
      registration: [
        'IVD产品注册申报需要根据产品风险等级确定申报路径。I类产品备案，II、III类产品注册。关键材料包括：产品技术要求、研究资料、临床评价资料等。',
        'NMPA注册流程通常包括：产品分类确定、技术文档准备、临床试验（如需要）、注册申请提交、技术审评、现场检查（如需要）、注册证书颁发等步骤。'
      ],
      sales: [
        'IVD产品市场销售需要制定清晰的市场定位和销售策略。建议从目标客户分析开始，建立合适的销售渠道，制定竞争性的价格策略。',
        '销售渠道建设可以考虑直销、经销商、代理商等多种模式。同时要重视客户关系管理，提供优质的技术支持和售后服务。'
      ]
    };
  }

  async sendMessage(message, modelId, options = {}) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const category = options.category || 'rd';
    const responses = this.responses[category] || this.responses.rd;
    
    // 根据消息内容选择合适的回复
    let response = responses[Math.floor(Math.random() * responses.length)];
    
    // 添加一些个性化内容
    if (message.includes('流程')) {
      response = '关于您询问的流程问题：' + response;
    } else if (message.includes('要求') || message.includes('标准')) {
      response = '根据相关法规要求：' + response;
    } else if (message.includes('策略') || message.includes('方法')) {
      response = '建议采用以下策略：' + response;
    }
    
    return response + '\n\n（注：这是离线回复，建议配置API密钥以获得更准确的专业建议）';
  }

  isAvailable() {
    return true; // 本地服务始终可用
  }

  async checkStatus() {
    return true;
  }
}

// 创建全局AI服务实例
const aiService = new AIServiceManager();

module.exports = {
  aiService,
  AIServiceManager,
  OpenAIService,
  AnthropicService,
  GoogleService,
  LocalService
};
