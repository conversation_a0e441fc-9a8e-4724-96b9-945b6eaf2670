// pages/settings/settings.js
const app = getApp();

Page({
  data: {
    userInfo: {},
    subscriptionName: '免费版',
    currentModel: {
      name: 'GPT-3.5 Turbo'
    },
    chatCount: 0,
    appVersion: '1.0.0',
    cloudSyncEnabled: true,
    chatSettings: {
      autoSave: true,
      showTimestamp: true,
      enableVoice: false
    },
    notificationSettings: {
      enabled: true,
      sound: true,
      vibrate: false
    },
    professionalSettings: {
      field: '医疗器械研发',
      experience: '中级（3-5年）',
      focusAreas: ['产品研发', '注册申报']
    },
    showFieldModal: false,
    showExperienceModal: false,
    selectedField: '',
    selectedExperience: '',
    professionalFields: [
      '医疗器械研发',
      '注册申报',
      '市场销售',
      '质量管理',
      '临床研究',
      '生产制造',
      '其他'
    ],
    experienceLevels: [
      '初级（0-2年）',
      '中级（3-5年）',
      '高级（6-10年）',
      '专家（10年以上）'
    ]
  },

  onLoad(options) {
    console.log('设置页面加载');
    this.loadUserData();
    this.loadSettings();
    this.loadSubscriptionInfo();
  },

  onShow() {
    this.loadCurrentModel();
    this.loadChatCount();
    this.loadSubscriptionInfo();
  },

  // 加载订阅信息
  async loadSubscriptionInfo() {
    if (!app.globalData.userInfo || app.globalData.userInfo.isGuest) {
      this.setData({
        subscriptionName: '游客模式'
      });
      return;
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          action: 'getUserSubscription'
        }
      });

      if (result.result.success) {
        const subscription = result.result.data;
        const tierNames = {
          free: '免费版',
          basic: '基础版',
          standard: '标准版',
          premium: '专业版'
        };

        this.setData({
          subscriptionName: tierNames[subscription.tier] || '免费版'
        });
      }
    } catch (error) {
      console.error('加载订阅信息失败:', error);
    }
  },

  // 跳转到订阅管理页面
  goToSubscription() {
    wx.navigateTo({
      url: '/pages/subscription/subscription'
    });
  },

  // 加载用户数据
  loadUserData() {
    const userInfo = app.globalData.userInfo;
    if (userInfo) {
      this.setData({
        userInfo
      });
    }
  },

  // 加载设置
  loadSettings() {
    try {
      const settings = wx.getStorageSync('appSettings');
      if (settings) {
        this.setData({
          chatSettings: settings.chatSettings || this.data.chatSettings,
          notificationSettings: settings.notificationSettings || this.data.notificationSettings,
          professionalSettings: settings.professionalSettings || this.data.professionalSettings
        });
      }
    } catch (e) {
      console.error('加载设置失败:', e);
    }
  },

  // 保存设置
  saveSettings() {
    try {
      const settings = {
        chatSettings: this.data.chatSettings,
        notificationSettings: this.data.notificationSettings,
        professionalSettings: this.data.professionalSettings
      };
      wx.setStorageSync('appSettings', settings);
    } catch (e) {
      console.error('保存设置失败:', e);
    }
  },

  // 加载当前模型
  loadCurrentModel() {
    const selectedModel = app.globalData.selectedAIModel;
    const modelMap = {
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'gpt-4': 'GPT-4',
      'claude-3': 'Claude-3',
      'gemini-pro': 'Gemini Pro'
    };

    this.setData({
      currentModel: {
        name: modelMap[selectedModel] || 'GPT-3.5 Turbo'
      }
    });
  },

  // 加载聊天记录数量
  loadChatCount() {
    const chatHistory = app.globalData.chatHistory;
    this.setData({
      chatCount: chatHistory.length
    });
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      // 跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    } catch (error) {
      console.error('跳转登录页面失败:', error);
      wx.showToast({
        title: '跳转失败',
        icon: 'error'
      });
    }
  },

  // 跳转到模型管理页面
  goToModels() {
    wx.navigateTo({
      url: '/pages/models/models'
    });
  },

  // 自动保存设置变化
  onAutoSaveChange(e) {
    this.setData({
      'chatSettings.autoSave': e.detail.value
    });
    this.saveSettings();
  },

  // 通知设置变化
  onNotificationChange(e) {
    this.setData({
      'notificationSettings.enabled': e.detail.value
    });
    this.saveSettings();
  },

  // 云端同步设置变化
  onCloudSyncChange(e) {
    const enabled = e.detail.value;
    this.setData({
      cloudSyncEnabled: enabled
    });

    // 保存设置
    wx.setStorageSync('cloudSyncEnabled', enabled);

    if (enabled) {
      wx.showToast({
        title: '云端同步已开启',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '云端同步已关闭',
        icon: 'none'
      });
    }
  },

  // 手动同步
  async manualSync() {
    if (!app.globalData.userInfo || app.globalData.userInfo.isGuest) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      const { cloudSyncManager } = require('../../utils/cloudSync');
      await cloudSyncManager.syncAllData();
    } catch (error) {
      console.error('手动同步失败:', error);
      wx.showToast({
        title: '同步失败',
        icon: 'error'
      });
    }
  },

  // 数据恢复
  async restoreData() {
    if (!app.globalData.userInfo || app.globalData.userInfo.isGuest) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '数据恢复',
      content: '确定要从云端恢复数据吗？这将覆盖本地数据。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const { cloudSyncManager } = require('../../utils/cloudSync');
            await cloudSyncManager.restoreAllData();

            // 刷新页面数据
            this.loadUserData();
            this.loadSettings();
            this.loadChatCount();
          } catch (error) {
            console.error('数据恢复失败:', error);
            wx.showToast({
              title: '恢复失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 显示数据管理
  showDataManagement() {
    const chatCount = this.data.chatCount;
    wx.showActionSheet({
      itemList: [
        '导出聊天记录',
        '清空聊天记录',
        '数据统计'
      ],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.exportChatHistory();
            break;
          case 1:
            this.clearChatHistory();
            break;
          case 2:
            this.showDataStats();
            break;
        }
      }
    });
  },

  // 导出聊天记录
  exportChatHistory() {
    const chatHistory = app.globalData.chatHistory;
    if (chatHistory.length === 0) {
      wx.showToast({
        title: '暂无聊天记录',
        icon: 'none'
      });
      return;
    }

    // 模拟导出功能
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // 清空聊天记录
  clearChatHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          app.globalData.chatHistory = [];
          wx.removeStorageSync('chatHistory');
          this.setData({
            chatCount: 0
          });
          wx.showToast({
            title: '聊天记录已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 显示数据统计
  showDataStats() {
    const chatHistory = app.globalData.chatHistory;
    const totalMessages = chatHistory.length;
    const userMessages = chatHistory.filter(m => m.role === 'user').length;
    const aiMessages = chatHistory.filter(m => m.role === 'assistant').length;

    wx.showModal({
      title: '数据统计',
      content: `总消息数：${totalMessages}\n用户消息：${userMessages}\nAI回复：${aiMessages}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 选择专业领域
  selectProfessionalField() {
    this.setData({
      showFieldModal: true,
      selectedField: this.data.professionalSettings.field
    });
  },

  // 选择经验级别
  selectExperienceLevel() {
    this.setData({
      showExperienceModal: true,
      selectedExperience: this.data.professionalSettings.experience
    });
  },

  // 选择关注重点
  selectFocusAreas() {
    const focusOptions = [
      '产品研发',
      '注册申报',
      '市场销售',
      '质量管理',
      '临床研究',
      '生产制造'
    ];

    wx.showActionSheet({
      itemList: focusOptions,
      success: (res) => {
        const selectedArea = focusOptions[res.tapIndex];
        const currentAreas = this.data.professionalSettings.focusAreas;
        
        if (!currentAreas.includes(selectedArea)) {
          const newAreas = [...currentAreas, selectedArea];
          this.setData({
            'professionalSettings.focusAreas': newAreas
          });
          this.saveSettings();
          wx.showToast({
            title: `已添加${selectedArea}`,
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '该领域已存在',
            icon: 'none'
          });
        }
      }
    });
  },

  // 选择领域
  selectField(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      'professionalSettings.field': field,
      showFieldModal: false
    });
    this.saveSettings();
  },

  // 选择经验
  selectExperience(e) {
    const experience = e.currentTarget.dataset.experience;
    this.setData({
      'professionalSettings.experience': experience,
      showExperienceModal: false
    });
    this.saveSettings();
  },

  // 隐藏弹窗
  hideFieldModal() {
    this.setData({
      showFieldModal: false
    });
  },

  hideExperienceModal() {
    this.setData({
      showExperienceModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 显示帮助
  showHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    });
  },

  // 显示反馈
  showFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的反馈！请通过以下方式联系我们：\n\n邮箱：<EMAIL>\n微信：IVD_AI_Support',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 显示关于
  showAbout() {
    wx.showModal({
      title: '关于IVD智能顾问',
      content: 'IVD智能顾问是专为医疗器械行业打造的AI咨询助手，提供专业的研发、注册、销售指导。\n\n版本：v1.0.0\n开发者：IVD AI Team',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 清空所有数据
  clearAllData() {
    wx.showModal({
      title: '危险操作',
      content: '确定要清空所有数据吗？包括聊天记录、设置等。此操作不可恢复！',
      confirmColor: '#F44336',
      success: (res) => {
        if (res.confirm) {
          // 清空全局数据
          app.globalData.chatHistory = [];
          app.globalData.apiKeys = {};
          
          // 清空本地存储
          wx.clearStorageSync();
          
          // 重置页面数据
          this.setData({
            chatCount: 0,
            chatSettings: {
              autoSave: true,
              showTimestamp: true,
              enableVoice: false
            },
            notificationSettings: {
              enabled: true,
              sound: true,
              vibrate: false
            },
            professionalSettings: {
              field: '医疗器械研发',
              experience: '中级（3-5年）',
              focusAreas: ['产品研发', '注册申报']
            }
          });
          
          wx.showToast({
            title: '所有数据已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 重置设置
  resetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要重置所有设置为默认值吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            chatSettings: {
              autoSave: true,
              showTimestamp: true,
              enableVoice: false
            },
            notificationSettings: {
              enabled: true,
              sound: true,
              vibrate: false
            },
            professionalSettings: {
              field: '医疗器械研发',
              experience: '中级（3-5年）',
              focusAreas: ['产品研发', '注册申报']
            }
          });
          this.saveSettings();
          wx.showToast({
            title: '设置已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 您的专属医疗器械咨询助手',
      path: '/pages/index/index'
    };
  }
});
