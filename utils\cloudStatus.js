// utils/cloudStatus.js
// 云函数状态检查工具

const { logger } = require('./logger')
const { cloudCall } = require('./cloudFunction')

/**
 * 云函数状态管理器
 */
class CloudStatusManager {
  constructor() {
    this.statusCache = new Map()
    this.lastCheckTime = 0
    this.checkInterval = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 检查所有云函数状态
   */
  async checkAllFunctions() {
    const now = Date.now()
    
    // 如果缓存未过期，返回缓存结果
    if (now - this.lastCheckTime < this.checkInterval && this.statusCache.size > 0) {
      return this.getCachedStatus()
    }

    logger.info('开始检查云函数状态')
    
    const functions = [
      { name: 'login', required: true, description: '用户登录' },
      { name: 'payment', required: false, description: '付费系统' },
      { name: 'userdata', required: false, description: '用户数据' },
      { name: 'apiConfig', required: false, description: 'API配置' },
      { name: 'initDB', required: false, description: '数据库初始化' }
    ]

    const results = {}
    
    for (const func of functions) {
      try {
        const status = await this.checkSingleFunction(func.name)
        results[func.name] = {
          ...status,
          required: func.required,
          description: func.description
        }
        this.statusCache.set(func.name, results[func.name])
      } catch (error) {
        results[func.name] = {
          available: false,
          error: error.message,
          required: func.required,
          description: func.description
        }
      }
    }

    this.lastCheckTime = now
    logger.info('云函数状态检查完成', results)
    
    return results
  }

  /**
   * 检查单个云函数
   */
  async checkSingleFunction(name) {
    try {
      // 尝试调用云函数
      const startTime = Date.now()
      
      await wx.cloud.callFunction({
        name,
        data: { _healthCheck: true }
      })
      
      const responseTime = Date.now() - startTime
      
      return {
        available: true,
        responseTime,
        status: 'healthy',
        lastCheck: new Date().toISOString()
      }
    } catch (error) {
      // 分析错误类型
      if (error.errCode === -501000 || error.errCode === 70002) {
        return {
          available: false,
          status: 'not_found',
          error: '云函数不存在',
          lastCheck: new Date().toISOString()
        }
      } else if (error.errCode === -1) {
        return {
          available: false,
          status: 'network_error',
          error: '网络连接失败',
          lastCheck: new Date().toISOString()
        }
      } else {
        // 其他错误可能表示云函数存在但执行失败
        return {
          available: true,
          status: 'error',
          error: error.errMsg || error.message,
          lastCheck: new Date().toISOString()
        }
      }
    }
  }

  /**
   * 获取缓存的状态
   */
  getCachedStatus() {
    const result = {}
    for (const [name, status] of this.statusCache) {
      result[name] = status
    }
    return result
  }

  /**
   * 生成状态报告
   */
  generateReport(status) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: 0,
        available: 0,
        unavailable: 0,
        required_missing: 0
      },
      functions: status,
      recommendations: []
    }

    // 统计信息
    for (const [name, info] of Object.entries(status)) {
      report.summary.total++
      
      if (info.available) {
        report.summary.available++
      } else {
        report.summary.unavailable++
        
        if (info.required) {
          report.summary.required_missing++
        }
      }
    }

    // 生成建议
    report.recommendations = this.generateRecommendations(status)

    return report
  }

  /**
   * 生成修复建议
   */
  generateRecommendations(status) {
    const recommendations = []

    for (const [name, info] of Object.entries(status)) {
      if (!info.available) {
        switch (name) {
          case 'login':
            recommendations.push({
              priority: 'high',
              message: '登录云函数缺失，用户无法登录',
              action: '请部署login云函数'
            })
            break
          case 'payment':
            recommendations.push({
              priority: 'medium',
              message: '付费系统云函数缺失，订阅功能不可用',
              action: '如需付费功能，请部署payment云函数'
            })
            break
          case 'userdata':
            recommendations.push({
              priority: 'medium',
              message: '用户数据云函数缺失，数据同步功能不可用',
              action: '如需数据同步，请部署userdata云函数'
            })
            break
          default:
            recommendations.push({
              priority: 'low',
              message: `${info.description}云函数缺失`,
              action: `如需相关功能，请部署${name}云函数`
            })
        }
      } else if (info.status === 'error') {
        recommendations.push({
          priority: 'medium',
          message: `${info.description}云函数运行异常`,
          action: `请检查${name}云函数代码和配置`
        })
      }
    }

    // 如果没有问题，添加正面反馈
    if (recommendations.length === 0) {
      recommendations.push({
        priority: 'info',
        message: '所有云函数运行正常',
        action: '系统状态良好'
      })
    }

    return recommendations
  }

  /**
   * 显示状态报告
   */
  showReport(report) {
    const { summary, recommendations } = report
    
    let title = '云函数状态检查'
    let content = `总计: ${summary.total}\n`
    content += `可用: ${summary.available}\n`
    content += `不可用: ${summary.unavailable}\n`
    
    if (summary.required_missing > 0) {
      content += `⚠️ 必需功能缺失: ${summary.required_missing}\n`
    }
    
    content += '\n建议:\n'
    
    const highPriority = recommendations.filter(r => r.priority === 'high')
    const mediumPriority = recommendations.filter(r => r.priority === 'medium')
    
    if (highPriority.length > 0) {
      content += '🔴 紧急:\n'
      highPriority.forEach(r => {
        content += `• ${r.action}\n`
      })
    }
    
    if (mediumPriority.length > 0) {
      content += '🟡 建议:\n'
      mediumPriority.forEach(r => {
        content += `• ${r.action}\n`
      })
    }

    wx.showModal({
      title,
      content,
      showCancel: false,
      confirmText: '知道了'
    })
  }

  /**
   * 快速检查关键功能
   */
  async quickCheck() {
    const criticalFunctions = ['login']
    const issues = []

    for (const name of criticalFunctions) {
      try {
        const status = await this.checkSingleFunction(name)
        if (!status.available) {
          issues.push(`${name}云函数不可用`)
        }
      } catch (error) {
        issues.push(`${name}云函数检查失败`)
      }
    }

    return {
      hasIssues: issues.length > 0,
      issues
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.statusCache.clear()
    this.lastCheckTime = 0
  }
}

// 创建全局实例
const cloudStatusManager = new CloudStatusManager()

module.exports = {
  cloudStatusManager,
  CloudStatusManager
}
