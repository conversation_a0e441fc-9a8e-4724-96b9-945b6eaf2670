// utils/exportUtils.js
// 对话导出和数据管理工具

/**
 * 对话导出管理器
 */
class ConversationExporter {
  constructor() {
    this.exportFormats = ['txt', 'json', 'markdown'];
    this.maxExportSize = 10 * 1024 * 1024; // 10MB
  }

  /**
   * 导出对话为文本格式
   * @param {Array} messages - 消息列表
   * @param {Object} options - 导出选项
   * @returns {string} 导出内容
   */
  exportToText(messages, options = {}) {
    const {
      includeTimestamp = true,
      includeMetadata = false,
      separator = '\n---\n'
    } = options;

    let content = '';
    
    // 添加头部信息
    if (includeMetadata) {
      content += `IVD智能顾问对话记录\n`;
      content += `导出时间: ${new Date().toLocaleString()}\n`;
      content += `消息总数: ${messages.length}\n`;
      content += `${separator}\n`;
    }

    // 添加消息内容
    messages.forEach((message, index) => {
      const timestamp = includeTimestamp ? 
        `[${new Date(message.timestamp).toLocaleString()}] ` : '';
      
      const sender = message.role === 'user' ? '用户' : 'AI助手';
      
      content += `${timestamp}${sender}:\n${message.content}\n`;
      
      if (index < messages.length - 1) {
        content += '\n';
      }
    });

    return content;
  }

  /**
   * 导出对话为JSON格式
   * @param {Array} messages - 消息列表
   * @param {Object} metadata - 元数据
   * @returns {string} JSON字符串
   */
  exportToJSON(messages, metadata = {}) {
    const exportData = {
      metadata: {
        exportTime: new Date().toISOString(),
        version: '1.0.0',
        source: 'IVD智能顾问',
        messageCount: messages.length,
        ...metadata
      },
      conversations: messages.map(message => ({
        id: message.id,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp,
        model: message.model || 'unknown'
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导出对话为Markdown格式
   * @param {Array} messages - 消息列表
   * @param {Object} options - 导出选项
   * @returns {string} Markdown内容
   */
  exportToMarkdown(messages, options = {}) {
    const {
      includeTimestamp = true,
      includeMetadata = true,
      title = 'IVD智能顾问对话记录'
    } = options;

    let content = '';
    
    // 添加标题
    content += `# ${title}\n\n`;
    
    // 添加元数据
    if (includeMetadata) {
      content += `**导出时间**: ${new Date().toLocaleString()}\n`;
      content += `**消息总数**: ${messages.length}\n`;
      content += `**来源**: IVD智能顾问\n\n`;
      content += '---\n\n';
    }

    // 添加消息内容
    messages.forEach((message, index) => {
      const timestamp = includeTimestamp ? 
        ` *${new Date(message.timestamp).toLocaleString()}*` : '';
      
      if (message.role === 'user') {
        content += `## 👤 用户${timestamp}\n\n`;
        content += `${message.content}\n\n`;
      } else {
        content += `## 🤖 AI助手${timestamp}\n\n`;
        content += `${message.content}\n\n`;
      }
    });

    return content;
  }

  /**
   * 过滤消息
   * @param {Array} messages - 消息列表
   * @param {Object} filters - 过滤条件
   * @returns {Array} 过滤后的消息
   */
  filterMessages(messages, filters = {}) {
    const {
      startDate,
      endDate,
      role,
      keyword,
      model
    } = filters;

    return messages.filter(message => {
      // 时间过滤
      if (startDate && message.timestamp < startDate) return false;
      if (endDate && message.timestamp > endDate) return false;
      
      // 角色过滤
      if (role && message.role !== role) return false;
      
      // 模型过滤
      if (model && message.model !== model) return false;
      
      // 关键词过滤
      if (keyword && !message.content.toLowerCase().includes(keyword.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * 统计对话数据
   * @param {Array} messages - 消息列表
   * @returns {Object} 统计结果
   */
  generateStatistics(messages) {
    const stats = {
      total: messages.length,
      userMessages: 0,
      aiMessages: 0,
      totalCharacters: 0,
      averageLength: 0,
      timeSpan: null,
      modelUsage: {},
      dailyStats: {}
    };

    if (messages.length === 0) return stats;

    // 基础统计
    messages.forEach(message => {
      if (message.role === 'user') {
        stats.userMessages++;
      } else {
        stats.aiMessages++;
      }
      
      stats.totalCharacters += message.content.length;
      
      // 模型使用统计
      const model = message.model || 'unknown';
      stats.modelUsage[model] = (stats.modelUsage[model] || 0) + 1;
      
      // 按日统计
      const date = new Date(message.timestamp).toDateString();
      if (!stats.dailyStats[date]) {
        stats.dailyStats[date] = { user: 0, ai: 0 };
      }
      stats.dailyStats[date][message.role === 'user' ? 'user' : 'ai']++;
    });

    // 计算平均长度
    stats.averageLength = Math.round(stats.totalCharacters / messages.length);
    
    // 计算时间跨度
    const timestamps = messages.map(m => m.timestamp).sort();
    stats.timeSpan = {
      start: new Date(timestamps[0]),
      end: new Date(timestamps[timestamps.length - 1]),
      duration: timestamps[timestamps.length - 1] - timestamps[0]
    };

    return stats;
  }

  /**
   * 检查导出大小
   * @param {string} content - 导出内容
   * @returns {Object} 大小检查结果
   */
  checkExportSize(content) {
    const size = new Blob([content]).size;
    
    return {
      size,
      sizeFormatted: this.formatFileSize(size),
      exceedsLimit: size > this.maxExportSize,
      maxSize: this.maxExportSize,
      maxSizeFormatted: this.formatFileSize(this.maxExportSize)
    };
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成导出文件名
   * @param {string} format - 文件格式
   * @param {Object} options - 选项
   * @returns {string} 文件名
   */
  generateFileName(format, options = {}) {
    const {
      prefix = 'ivd_conversation',
      includeDate = true,
      includeTime = false
    } = options;

    let fileName = prefix;
    
    if (includeDate) {
      const now = new Date();
      const date = now.toISOString().split('T')[0];
      fileName += `_${date}`;
      
      if (includeTime) {
        const time = now.toTimeString().split(' ')[0].replace(/:/g, '-');
        fileName += `_${time}`;
      }
    }
    
    fileName += `.${format}`;
    
    return fileName;
  }

  /**
   * 压缩导出内容
   * @param {string} content - 原始内容
   * @returns {string} 压缩后的内容
   */
  compressContent(content) {
    // 简单的内容压缩：移除多余空行和空格
    return content
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 移除多余空行
      .replace(/[ \t]+/g, ' ') // 合并多余空格
      .trim();
  }

  /**
   * 验证导出数据
   * @param {Array} messages - 消息列表
   * @returns {Object} 验证结果
   */
  validateExportData(messages) {
    const errors = [];
    const warnings = [];

    if (!Array.isArray(messages)) {
      errors.push('消息数据格式错误');
      return { valid: false, errors, warnings };
    }

    if (messages.length === 0) {
      warnings.push('没有可导出的消息');
    }

    messages.forEach((message, index) => {
      if (!message.content) {
        errors.push(`消息 ${index + 1} 缺少内容`);
      }
      
      if (!message.role) {
        errors.push(`消息 ${index + 1} 缺少角色信息`);
      }
      
      if (!message.timestamp) {
        warnings.push(`消息 ${index + 1} 缺少时间戳`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// 创建全局实例
const conversationExporter = new ConversationExporter();

module.exports = {
  conversationExporter,
  ConversationExporter
};
