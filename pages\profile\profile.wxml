<!--pages/profile/profile.wxml-->
<view class="page-container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-card card gradient-bg">
      <view class="user-info">
        <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name text-inverse">{{userInfo.nickName || '未登录'}}</text>
          <view class="user-tier">
            <view class="tier-badge tier-{{subscriptionInfo.tier}}">
              <text class="tier-icon">{{subscriptionInfo.icon}}</text>
              <text class="tier-name text-inverse">{{subscriptionInfo.name}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 登录/升级按钮 -->
      <view class="user-actions">
        <button class="btn btn-secondary" bindtap="goToLogin" wx:if="{{!userInfo.nickName}}">
          <text>立即登录</text>
        </button>
        <button class="btn btn-secondary" bindtap="goToSubscription" wx:else>
          <text>管理订阅</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 使用统计 -->
  <view class="stats-section" wx:if="{{userInfo.nickName}}">
    <view class="stats-card card">
      <view class="stats-header">
        <text class="title-tertiary">使用统计</text>
        <text class="stats-period">本月</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{usageStats.monthly || 0}}</text>
          <text class="stat-label">对话次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{usageStats.totalChats || 0}}</text>
          <text class="stat-label">总对话数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{usageStats.favoriteModel || 'DeepSeek-V3'}}</text>
          <text class="stat-label">常用模型</text>
        </view>
      </view>
      
      <!-- 使用进度 -->
      <view class="usage-progress">
        <view class="progress-header">
          <text class="progress-label">本月使用进度</text>
          <text class="progress-text">{{usageStats.monthly || 0}}/{{subscriptionInfo.monthlyLimit || 0}}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{usagePercentage}}%;"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToHistory">
        <view class="menu-icon">📝</view>
        <text class="menu-text">对话历史</text>
        <view class="menu-arrow">></view>
      </view>
      
      <view class="menu-item" bindtap="goToModels">
        <view class="menu-icon">🤖</view>
        <text class="menu-text">AI模型</text>
        <view class="menu-arrow">></view>
      </view>
      
      <view class="menu-item" bindtap="goToSubscription">
        <view class="menu-icon">💎</view>
        <text class="menu-text">订阅管理</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="showSettings">
        <view class="menu-icon">⚙️</view>
        <text class="menu-text">设置</text>
        <view class="menu-arrow">></view>
      </view>
      
      <view class="menu-item" bindtap="showHelp">
        <view class="menu-icon">❓</view>
        <text class="menu-text">帮助与反馈</text>
        <view class="menu-arrow">></view>
      </view>
      
      <view class="menu-item" bindtap="showAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="menu-group" wx:if="{{userInfo.nickName}}">
      <view class="menu-item logout-item" bindtap="logout">
        <view class="menu-icon">🚪</view>
        <text class="menu-text">退出登录</text>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-section">
    <text class="version-text">IVD智能顾问 v{{appVersion}}</text>
  </view>
</view>

<!-- 设置弹窗 -->
<view class="modal-overlay" wx:if="{{showSettingsModal}}" bindtap="hideSettingsModal">
  <view class="modal-content card" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="title-secondary">设置</text>
      <button class="close-btn btn btn-ghost btn-small" bindtap="hideSettingsModal">×</button>
    </view>
    <view class="modal-body">
      <view class="setting-item">
        <text class="setting-label">消息通知</text>
        <switch checked="{{settings.notifications}}" bindchange="toggleNotifications"/>
      </view>
      <view class="setting-item">
        <text class="setting-label">自动保存对话</text>
        <switch checked="{{settings.autoSave}}" bindchange="toggleAutoSave"/>
      </view>
      <view class="setting-item">
        <text class="setting-label">云端同步</text>
        <switch checked="{{settings.cloudSync}}" bindchange="toggleCloudSync"/>
      </view>
    </view>
  </view>
</view>

<!-- 帮助弹窗 -->
<view class="modal-overlay" wx:if="{{showHelpModal}}" bindtap="hideHelpModal">
  <view class="modal-content card" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="title-secondary">帮助与反馈</text>
      <button class="close-btn btn btn-ghost btn-small" bindtap="hideHelpModal">×</button>
    </view>
    <view class="modal-body">
      <view class="help-section">
        <text class="help-title">常见问题</text>
        <view class="help-item" bindtap="showFAQ">
          <text>如何使用AI模型？</text>
        </view>
        <view class="help-item" bindtap="showFAQ">
          <text>如何升级订阅？</text>
        </view>
        <view class="help-item" bindtap="showFAQ">
          <text>如何导出对话记录？</text>
        </view>
      </view>
      
      <view class="help-section">
        <text class="help-title">联系我们</text>
        <button class="btn btn-outline" bindtap="contactSupport">
          <text>在线客服</text>
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 关于弹窗 -->
<view class="modal-overlay" wx:if="{{showAboutModal}}" bindtap="hideAboutModal">
  <view class="modal-content card" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="title-secondary">关于IVD智能顾问</text>
      <button class="close-btn btn btn-ghost btn-small" bindtap="hideAboutModal">×</button>
    </view>
    <view class="modal-body">
      <view class="about-content">
        <view class="app-logo">🔬</view>
        <text class="app-name">IVD智能顾问</text>
        <text class="app-desc">专业的体外诊断医疗器械AI咨询助手</text>
        
        <view class="about-info">
          <text class="info-item">版本：{{appVersion}}</text>
          <text class="info-item">开发者：IVD团队</text>
          <text class="info-item">更新时间：{{updateTime}}</text>
        </view>
        
        <view class="about-links">
          <button class="btn btn-outline btn-small" bindtap="showPrivacy">
            <text>隐私政策</text>
          </button>
          <button class="btn btn-outline btn-small" bindtap="showTerms">
            <text>服务条款</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
