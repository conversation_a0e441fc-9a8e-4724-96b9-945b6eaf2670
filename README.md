# IVD智能顾问 - WeChat Mini Program

> 您的专属研发、注册、销售顾问

## 项目简介

IVD智能顾问是一款专为医疗器械（IVD）行业打造的微信小程序，集成多种AI大语言模型，为用户提供专业的产品研发、注册申报、市场销售等方面的智能咨询服务。

## 核心功能

### 🤖 多AI模型集成
- **GPT-3.5 Turbo**: 快速响应，适合日常咨询
- **GPT-4**: 深度推理，复杂问题分析
- **Claude-3**: 长文本处理，深度分析
- **Gemini Pro**: 多模态AI助手

### 🎯 专业领域覆盖
- **产品研发流程**: 需求分析、技术评估、产品设计、临床试验
- **注册申报要点**: NMPA、CE、FDA注册流程指导
- **市场销售策略**: 市场定位、渠道建设、客户管理
- **质量管理体系**: ISO13485体系建立和维护

### 💬 智能对话体验
- 实时AI对话
- 上下文理解
- 专业术语识别
- 智能建议生成
- 对话历史管理

## 技术架构

### 前端技术
- **框架**: 微信小程序原生开发
- **样式**: WXSS + Flexbox布局
- **状态管理**: 全局数据管理
- **组件化**: 模块化页面设计

### 后端服务
- **AI服务**: 多LLM API集成
- **知识库**: IVD专业知识体系
- **错误处理**: 统一错误管理
- **缓存机制**: 响应缓存优化

## 项目结构

```
ivdminip/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── project.config.json   # 项目配置文件
├── pages/                # 页面目录
│   ├── index/           # 首页
│   │   ├── index.wxml
│   │   ├── index.js
│   │   ├── index.wxss
│   │   └── index.json
│   ├── chat/            # 聊天页面
│   │   ├── chat.wxml
│   │   ├── chat.js
│   │   ├── chat.wxss
│   │   └── chat.json
│   ├── models/          # AI模型选择页面
│   │   ├── models.wxml
│   │   ├── models.js
│   │   ├── models.wxss
│   │   └── models.json
│   └── settings/        # 设置页面
│       ├── settings.wxml
│       ├── settings.js
│       ├── settings.wxss
│       └── settings.json
├── utils/               # 工具类目录
│   ├── aiService.js     # AI服务管理
│   ├── ivdKnowledge.js  # IVD知识库
│   ├── apiUtils.js      # API工具类
│   └── constants.js     # 常量定义
└── images/              # 图片资源目录
```

## 开发环境搭建

### 1. 环境要求
- **微信开发者工具**: 最新稳定版
- **Node.js**: 14.0+ (可选，用于开发工具)
- **微信小程序账号**: 已注册并获得AppID

### 2. 项目配置
1. 下载并安装微信开发者工具
2. 克隆或下载项目代码
3. 在微信开发者工具中导入项目
4. 修改 `project.config.json` 中的 `appid` 为您的小程序AppID

### 3. API密钥配置
在小程序中配置以下API密钥（可选）：
- **OpenAI API Key**: 用于GPT模型
- **Anthropic API Key**: 用于Claude模型  
- **Google API Key**: 用于Gemini模型

## 部署说明

### 1. 开发版本
1. 在微信开发者工具中点击"预览"
2. 使用微信扫描二维码进行真机测试

### 2. 体验版本
1. 在微信开发者工具中点击"上传"
2. 在微信公众平台设置体验版本
3. 邀请用户体验测试

### 3. 正式版本
1. 在微信公众平台提交审核
2. 审核通过后发布上线
3. 用户可在微信中搜索小程序名称使用

## 使用指南

### 1. 首次使用
- 打开小程序后，可以直接开始对话
- 建议先配置API密钥以获得更好的服务体验
- 选择适合的AI模型进行咨询

### 2. 专业咨询
- 点击首页的快速咨询入口
- 选择相关的专业领域
- 输入具体问题获得专业建议

### 3. 设置管理
- 在设置页面配置个人信息
- 管理API密钥和模型选择
- 调整应用偏好设置

## 功能特色

### 🔬 专业知识库
- 涵盖IVD行业完整知识体系
- 实时更新的法规要求
- 最佳实践案例分享

### ⚡ 智能响应
- 毫秒级响应速度
- 上下文理解能力
- 多轮对话支持

### 🛡️ 数据安全
- 本地数据存储
- API密钥加密保护
- 隐私信息不上传

### 📱 用户体验
- 简洁直观的界面设计
- 流畅的交互动画
- 响应式布局适配

## 开发计划

### 近期计划
- [ ] 语音输入功能
- [ ] 图片识别分析
- [ ] 文档导出功能
- [ ] 离线知识库

### 长期规划
- [ ] 多语言支持
- [ ] 企业版功能
- [ ] API开放平台
- [ ] 移动端APP

## 技术支持

### 问题反馈
- 邮箱: <EMAIL>
- 微信: IVD_AI_Support

### 开发文档
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [OpenAI API文档](https://platform.openai.com/docs)
- [Anthropic API文档](https://docs.anthropic.com/)

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

---

**IVD智能顾问** - 让AI成为您的专业伙伴
