# IVD智能顾问 🔬

> 专业的体外诊断医疗器械AI咨询助手

[![微信小程序](https://img.shields.io/badge/微信小程序-IVD智能顾问-brightgreen)](https://github.com/your-repo/ivd-ai-consultant)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-1.0.0-orange.svg)](package.json)

## 📖 项目简介

IVD智能顾问是一款专为体外诊断（In Vitro Diagnostics）医疗器械行业打造的AI咨询助手。通过集成多种先进的AI模型，为用户提供专业的产品研发、注册申报、市场销售和质量管理咨询服务。

### 🎯 核心功能

- **🤖 多AI模型支持** - 集成DeepSeek V3、通义千问等先进AI模型
- **🔐 微信一键登录** - 支持微信授权登录，数据云端同步
- **💎 订阅服务** - 灵活的付费订阅模式，满足不同用户需求
- **📝 对话历史** - 自动保存咨询记录，支持搜索和分类
- **🎨 协调UI设计** - 统一的设计系统，优秀的用户体验
- **☁️ 云端同步** - 用户数据安全存储，多设备同步

### 🏥 专业领域

- **产品研发流程** - 需求分析、技术评估、产品设计指导
- **注册申报要点** - NMPA、CE、FDA注册申报专业建议
- **市场销售策略** - 市场定位、渠道建设、客户管理
- **质量管理体系** - ISO13485、风险管理、审核指导

## 🚀 快速开始

### 环境要求

- **微信开发者工具** 1.06.0+
- **Node.js** 14.0.0+
- **微信小程序基础库** 2.19.4+

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/your-repo/ivd-ai-consultant.git
cd ivd-ai-consultant
```

2. **安装依赖**
```bash
npm install
```

3. **配置云开发**
```bash
# 在微信开发者工具中
1. 开通云开发服务
2. 创建云开发环境
3. 配置环境变量
```

4. **环境变量配置**
```bash
# 在云函数中配置以下环境变量
DEEPSEEK_API_KEY=your_deepseek_api_key
QWEN_API_KEY=your_qwen_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
```

5. **部署云函数**
```bash
# 在微信开发者工具中右键每个云函数
# 选择"上传并部署：云端安装依赖"
```

6. **初始化数据库**
```bash
# 调用initDB云函数初始化数据库结构
```

## 🏗️ 项目结构

```
ivd-ai-consultant/
├── pages/                 # 页面文件
│   ├── index/             # 首页
│   ├── chat/              # 聊天页面
│   ├── models/            # AI模型选择
│   ├── login/             # 登录页面
│   ├── subscription/      # 订阅管理
│   ├── history/           # 对话历史
│   └── profile/           # 个人中心
├── components/            # 自定义组件
│   └── premiumGate/       # 付费功能门控
├── cloudfunctions/        # 云函数
│   ├── login/             # 用户登录
│   ├── payment/           # 支付系统
│   ├── userdata/          # 用户数据
│   ├── syncUserData/      # 数据同步
│   ├── getApiConfig/      # API配置
│   └── initDB/            # 数据库初始化
├── utils/                 # 工具函数
│   ├── aiService.js       # AI服务
│   ├── accessControl.js   # 访问控制
│   ├── cloudFunction.js   # 云函数调用
│   └── logger.js          # 日志管理
├── styles/                # 样式文件
│   └── design-system.wxss # 设计系统
└── scripts/               # 脚本文件
    └── remove-debug-features.js # 生产环境清理
```

## 🤖 AI模型配置

### 支持的AI模型

| 模型 | 提供商 | 订阅要求 | 特点 |
|------|--------|----------|------|
| DeepSeek-V3-0324 | DeepSeek | 免费版+ | 推理能力强，性价比高 |
| DeepSeek-R1-0528 | DeepSeek | 基础版+ | 推理增强，复杂问题 |
| Qwen3 | 阿里云 | 免费版+ | 中文优化，快速响应 |
| Qwen-Max | 阿里云 | 标准版+ | 顶级性能，专业知识 |

### API配置

所有API密钥通过云函数环境变量管理，确保安全性：

```javascript
// 通过云函数获取API配置
const config = await wx.cloud.callFunction({
  name: 'getApiConfig',
  data: { provider: 'deepseek' }
});
```

## 💳 订阅计划

| 计划 | 价格 | 日限制 | 月限制 | 模型访问 |
|------|------|--------|--------|----------|
| 免费版 | ¥0/月 | 10次 | 100次 | DeepSeek-V3, Qwen3 |
| 基础版 | ¥19.9/月 | 100次 | 2000次 | + DeepSeek-R1 |
| 标准版 | ¥39.9/月 | 300次 | 6000次 | + Qwen-Max |
| 专业版 | ¥99.9/月 | 1000次 | 20000次 | 全部模型 |

## 🔧 开发指南

### 本地开发

1. **启动开发服务器**
```bash
# 在微信开发者工具中打开项目
# 选择"本地设置" -> "不校验合法域名"
```

2. **调试云函数**
```bash
# 右键云函数 -> "本地调试"
# 设置断点进行调试
```

3. **查看日志**
```bash
# 在控制台查看实时日志
# 使用logger工具记录关键信息
```

### 代码规范

- **命名规范** - 使用驼峰命名法
- **注释规范** - 关键函数必须添加注释
- **错误处理** - 统一使用try-catch处理异常
- **日志记录** - 使用logger工具记录日志

### 测试

```bash
# 运行单元测试
npm test

# 运行集成测试
npm run test:integration

# 代码覆盖率
npm run coverage
```

## 📱 功能特性

### 用户体验
- ✅ 微信一键登录
- ✅ 响应式设计
- ✅ 流畅的动画效果
- ✅ 智能错误提示
- ✅ 离线缓存支持

### 安全特性
- ✅ API密钥安全存储
- ✅ 用户数据加密
- ✅ 访问权限控制
- ✅ 支付安全保障
- ✅ 数据备份恢复

### 性能优化
- ✅ 代码分包加载
- ✅ 图片懒加载
- ✅ 请求缓存机制
- ✅ 数据库索引优化
- ✅ CDN加速

## 🔒 隐私与安全

我们高度重视用户隐私和数据安全：

- **数据加密** - 所有敏感数据采用AES加密存储
- **权限控制** - 严格的用户权限验证机制
- **隐私保护** - 遵循最小化数据收集原则
- **安全审计** - 定期进行安全漏洞扫描
- **合规认证** - 符合相关法律法规要求

## 📊 监控与分析

### 性能监控
- 页面加载时间
- API响应时间
- 错误率统计
- 用户行为分析

### 业务指标
- 用户活跃度
- 功能使用率
- 付费转化率
- 用户满意度

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📝 文档改进
- 🎨 UI/UX优化
- ⚡ 性能优化

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/your-repo/ivd-ai-consultant)
- **问题反馈**: [Issues](https://github.com/your-repo/ivd-ai-consultant/issues)
- **邮箱**: <EMAIL>
- **微信群**: 扫描二维码加入技术交流群

## 🙏 致谢

感谢以下开源项目和服务提供商：

- [微信小程序](https://developers.weixin.qq.com/miniprogram/dev/framework/) - 小程序开发框架
- [DeepSeek](https://www.deepseek.com/) - AI模型服务
- [阿里云](https://www.aliyun.com/) - 通义千问API
- [微信云开发](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html) - 云服务支持

---

<div align="center">
  <p>如果这个项目对您有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by IVD Team</p>
</div>
