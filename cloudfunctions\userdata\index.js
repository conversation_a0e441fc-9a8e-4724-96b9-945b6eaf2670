// cloudfunctions/userdata/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 用户数据管理云函数
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @returns {Object} 操作结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  const { action, data } = event
  
  try {
    switch (action) {
      case 'getUserInfo':
        return await getUserInfo(OPENID)
      case 'updateUserInfo':
        return await updateUserInfo(OPENID, data)
      case 'savePreferences':
        return await savePreferences(OPENID, data)
      case 'getPreferences':
        return await getPreferences(OPENID)
      case 'saveChatHistory':
        return await saveChatHistory(OPENID, data)
      case 'getChatHistory':
        return await getChatHistory(OPENID, data)
      case 'deleteChatHistory':
        return await deleteChatHistory(OPENID, data)
      case 'saveAPIKeys':
        return await saveAPIKeys(OPENID, data)
      case 'getAPIKeys':
        return await getAPIKeys(OPENID)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('用户数据操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      error: error.message
    }
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo(openid) {
  const userCollection = db.collection('users')
  const result = await userCollection.where({ openid }).get()
  
  if (result.data.length > 0) {
    return {
      success: true,
      data: result.data[0]
    }
  } else {
    return {
      success: false,
      message: '用户不存在'
    }
  }
}

/**
 * 更新用户信息
 */
async function updateUserInfo(openid, userData) {
  const userCollection = db.collection('users')
  
  const updateData = {
    ...userData,
    updatedAt: new Date()
  }
  
  const result = await userCollection.where({ openid }).update({
    data: updateData
  })
  
  return {
    success: true,
    message: '用户信息更新成功',
    data: result
  }
}

/**
 * 保存用户偏好设置
 */
async function savePreferences(openid, preferences) {
  const userCollection = db.collection('users')
  
  const result = await userCollection.where({ openid }).update({
    data: {
      preferences,
      updatedAt: new Date()
    }
  })
  
  return {
    success: true,
    message: '偏好设置保存成功',
    data: result
  }
}

/**
 * 获取用户偏好设置
 */
async function getPreferences(openid) {
  const userCollection = db.collection('users')
  const result = await userCollection.where({ openid }).field({
    preferences: true
  }).get()
  
  if (result.data.length > 0) {
    return {
      success: true,
      data: result.data[0].preferences || {}
    }
  } else {
    return {
      success: false,
      message: '用户偏好设置不存在'
    }
  }
}

/**
 * 保存聊天记录
 */
async function saveChatHistory(openid, chatData) {
  const chatCollection = db.collection('chat_history')
  
  const chatRecord = {
    openid,
    sessionId: chatData.sessionId || generateSessionId(),
    messages: chatData.messages,
    model: chatData.model,
    category: chatData.category,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  // 检查是否已存在该会话
  if (chatData.sessionId) {
    const existingChat = await chatCollection.where({
      openid,
      sessionId: chatData.sessionId
    }).get()
    
    if (existingChat.data.length > 0) {
      // 更新现有会话
      const result = await chatCollection.doc(existingChat.data[0]._id).update({
        data: {
          messages: chatData.messages,
          updatedAt: new Date()
        }
      })
      
      return {
        success: true,
        message: '聊天记录更新成功',
        data: { sessionId: chatData.sessionId }
      }
    }
  }
  
  // 创建新会话
  const result = await chatCollection.add({
    data: chatRecord
  })
  
  return {
    success: true,
    message: '聊天记录保存成功',
    data: { 
      sessionId: chatRecord.sessionId,
      recordId: result._id
    }
  }
}

/**
 * 获取聊天记录
 */
async function getChatHistory(openid, params = {}) {
  const chatCollection = db.collection('chat_history')
  const { sessionId, limit = 20, skip = 0 } = params
  
  let query = chatCollection.where({ openid })
  
  if (sessionId) {
    query = query.where({ sessionId })
  }
  
  const result = await query
    .orderBy('updatedAt', 'desc')
    .limit(limit)
    .skip(skip)
    .get()
  
  return {
    success: true,
    data: result.data
  }
}

/**
 * 删除聊天记录
 */
async function deleteChatHistory(openid, params) {
  const chatCollection = db.collection('chat_history')
  const { sessionId, recordId } = params
  
  let query = chatCollection.where({ openid })
  
  if (recordId) {
    query = query.where({ _id: recordId })
  } else if (sessionId) {
    query = query.where({ sessionId })
  }
  
  const result = await query.remove()
  
  return {
    success: true,
    message: '聊天记录删除成功',
    data: result
  }
}

/**
 * 保存API密钥（加密存储）
 */
async function saveAPIKeys(openid, apiKeys) {
  const userCollection = db.collection('users')
  
  // 简单的加密处理（实际应用中应使用更安全的加密方法）
  const encryptedKeys = {}
  Object.keys(apiKeys).forEach(provider => {
    if (apiKeys[provider]) {
      encryptedKeys[provider] = Buffer.from(apiKeys[provider]).toString('base64')
    }
  })
  
  const result = await userCollection.where({ openid }).update({
    data: {
      apiKeys: encryptedKeys,
      updatedAt: new Date()
    }
  })
  
  return {
    success: true,
    message: 'API密钥保存成功',
    data: result
  }
}

/**
 * 获取API密钥（解密）
 */
async function getAPIKeys(openid) {
  const userCollection = db.collection('users')
  const result = await userCollection.where({ openid }).field({
    apiKeys: true
  }).get()
  
  if (result.data.length > 0 && result.data[0].apiKeys) {
    const encryptedKeys = result.data[0].apiKeys
    const decryptedKeys = {}
    
    Object.keys(encryptedKeys).forEach(provider => {
      if (encryptedKeys[provider]) {
        decryptedKeys[provider] = Buffer.from(encryptedKeys[provider], 'base64').toString()
      }
    })
    
    return {
      success: true,
      data: decryptedKeys
    }
  } else {
    return {
      success: true,
      data: {}
    }
  }
}

/**
 * 生成会话ID
 */
function generateSessionId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
