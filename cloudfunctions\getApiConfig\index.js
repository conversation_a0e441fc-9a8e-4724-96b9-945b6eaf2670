// cloudfunctions/getApiConfig/index.js
// 获取API配置的云函数

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取API配置
 */
exports.main = async (event, context) => {
  const { key, provider } = event

  try {
    // 验证请求权限（可以添加更严格的权限控制）
    const wxContext = cloud.getWXContext()

    // 支持两种调用方式：按key获取或按provider获取
    if (provider) {
      return getProviderConfig(provider)
    }

    // 从环境变量中获取API密钥
    const apiKeys = {
      'DEEPSEEK_API_KEY': process.env.DEEPSEEK_API_KEY,
      'QWEN_API_KEY': process.env.QWEN_API_KEY
    }

    if (!apiKeys[key]) {
      return {
        success: false,
        message: `API密钥 ${key} 未配置`
      }
    }

    return {
      success: true,
      value: apiKeys[key]
    }

  } catch (error) {
    console.error('获取API配置失败:', error)
    return {
      success: false,
      message: '获取API配置失败',
      error: error.message
    }
  }
}

/**
 * 获取提供商配置
 * @param {string} provider - 提供商名称
 * @returns {Object} 配置对象
 */
function getProviderConfig(provider) {
  const configs = {
    deepseek: {
      apiKey: process.env.DEEPSEEK_API_KEY,
      baseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',
      models: ['deepseek-v3-0324', 'deepseek-r1-0528']
    },
    qwen: {
      apiKey: process.env.QWEN_API_KEY,
      baseUrl: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
      models: ['qwen3', 'qwen-max']
    },
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo']
    }
  }

  const config = configs[provider]

  if (!config) {
    return {
      success: false,
      message: '不支持的API提供商'
    }
  }

  return {
    success: true,
    data: config
  }
}
