// utils/consultationTemplates.js
// 快速咨询模板管理

/**
 * 咨询模板管理器
 */
class ConsultationTemplateManager {
  constructor() {
    this.templates = {
      // 产品研发模板
      rd: {
        category: '产品研发',
        icon: '🔬',
        color: '#1976D2',
        templates: [
          {
            id: 'rd_001',
            title: '产品需求分析',
            description: '如何进行IVD产品的市场需求分析',
            content: '我想开发一款新的IVD产品，请详细介绍如何进行市场需求分析，包括目标用户调研、竞争对手分析、技术可行性评估等关键步骤。',
            tags: ['需求分析', '市场调研', '可行性'],
            difficulty: 'beginner'
          },
          {
            id: 'rd_002',
            title: '设计开发流程',
            description: 'IVD产品设计开发的完整流程',
            content: '请详细介绍IVD产品从概念设计到产品定型的完整开发流程，包括设计输入、设计输出、设计验证、设计确认等各个阶段的要点和注意事项。',
            tags: ['设计开发', '流程管理', '质量控制'],
            difficulty: 'intermediate'
          },
          {
            id: 'rd_003',
            title: '临床试验设计',
            description: '如何设计和实施临床试验',
            content: '我需要为新开发的IVD产品设计临床试验方案，请介绍临床试验的设计原则、样本量计算、统计分析方法以及试验实施过程中的关键控制点。',
            tags: ['临床试验', '统计分析', '方案设计'],
            difficulty: 'advanced'
          },
          {
            id: 'rd_004',
            title: '性能验证',
            description: '产品分析性能验证要点',
            content: '请详细说明IVD产品分析性能验证的主要内容，包括准确度、精密度、线性范围、检出限、参考区间等指标的验证方法和判定标准。',
            tags: ['性能验证', '分析指标', '质量控制'],
            difficulty: 'intermediate'
          }
        ]
      },

      // 注册申报模板
      registration: {
        category: '注册申报',
        icon: '📋',
        color: '#4CAF50',
        templates: [
          {
            id: 'reg_001',
            title: 'NMPA注册流程',
            description: '中国NMPA注册申报完整流程',
            content: '请详细介绍IVD产品在中国NMPA的注册申报流程，包括产品分类确定、技术文档准备、申报材料提交、技术审评、现场检查等各个环节的要求和时间节点。',
            tags: ['NMPA', '注册流程', '申报材料'],
            difficulty: 'intermediate'
          },
          {
            id: 'reg_002',
            title: 'CE认证要求',
            description: '欧盟CE认证的要求和流程',
            content: '我的产品需要进入欧盟市场，请介绍IVDR法规下的CE认证要求，包括产品分类、技术文档、符合性评估程序、公告机构选择等关键要素。',
            tags: ['CE认证', 'IVDR', '欧盟市场'],
            difficulty: 'advanced'
          },
          {
            id: 'reg_003',
            title: 'FDA 510(k)申报',
            description: '美国FDA 510(k)申报指南',
            content: '请详细说明FDA 510(k)申报的要求和流程，包括实质等效性论证、性能数据要求、标签要求以及申报策略制定。',
            tags: ['FDA', '510(k)', '美国市场'],
            difficulty: 'advanced'
          },
          {
            id: 'reg_004',
            title: '临床评价资料',
            description: '如何准备临床评价资料',
            content: '请介绍IVD产品临床评价资料的准备要点，包括临床文献检索、临床数据分析、临床评价报告撰写等内容。',
            tags: ['临床评价', '文献检索', '数据分析'],
            difficulty: 'intermediate'
          }
        ]
      },

      // 市场销售模板
      sales: {
        category: '市场销售',
        icon: '📈',
        color: '#FF9800',
        templates: [
          {
            id: 'sales_001',
            title: '市场定位策略',
            description: '如何制定产品市场定位策略',
            content: '请帮我分析IVD产品的市场定位策略，包括目标客户群体识别、价值主张设计、竞争优势分析以及差异化策略制定。',
            tags: ['市场定位', '目标客户', '竞争分析'],
            difficulty: 'intermediate'
          },
          {
            id: 'sales_002',
            title: '渠道建设',
            description: '销售渠道建设和管理',
            content: '我想建立IVD产品的销售渠道，请介绍直销、经销商、代理商等不同渠道模式的特点、选择标准以及管理要点。',
            tags: ['渠道建设', '经销商', '渠道管理'],
            difficulty: 'beginner'
          },
          {
            id: 'sales_003',
            title: '定价策略',
            description: '产品定价策略制定',
            content: '请详细介绍IVD产品的定价策略，包括成本分析、竞争定价、价值定价等方法，以及不同市场阶段的定价调整策略。',
            tags: ['定价策略', '成本分析', '竞争定价'],
            difficulty: 'intermediate'
          },
          {
            id: 'sales_004',
            title: '客户关系管理',
            description: '医院客户关系管理要点',
            content: '请介绍IVD产品在医院市场的客户关系管理策略，包括客户分级、需求挖掘、关系维护以及客户满意度提升方法。',
            tags: ['客户关系', '医院市场', '需求挖掘'],
            difficulty: 'intermediate'
          }
        ]
      },

      // 质量管理模板
      quality: {
        category: '质量管理',
        icon: '✅',
        color: '#9C27B0',
        templates: [
          {
            id: 'qms_001',
            title: 'ISO13485体系建立',
            description: '如何建立ISO13485质量管理体系',
            content: '我们公司需要建立ISO13485质量管理体系，请详细介绍体系建立的步骤、关键要素、文件体系设计以及实施要点。',
            tags: ['ISO13485', '质量体系', '文件管理'],
            difficulty: 'intermediate'
          },
          {
            id: 'qms_002',
            title: '风险管理',
            description: 'ISO14971风险管理实施',
            content: '请介绍IVD产品风险管理的实施方法，包括风险识别、风险分析、风险评价、风险控制以及风险管理文档的编制。',
            tags: ['风险管理', 'ISO14971', '风险控制'],
            difficulty: 'advanced'
          },
          {
            id: 'qms_003',
            title: '供应商管理',
            description: '供应商评估和管理体系',
            content: '请详细说明IVD企业的供应商管理要求，包括供应商评估标准、审核流程、绩效监控以及供应商关系维护。',
            tags: ['供应商管理', '评估审核', '绩效监控'],
            difficulty: 'intermediate'
          },
          {
            id: 'qms_004',
            title: '内部审核',
            description: '质量管理体系内部审核',
            content: '请介绍质量管理体系内部审核的计划制定、审核实施、不符合项处理以及持续改进的方法和要点。',
            tags: ['内部审核', '不符合项', '持续改进'],
            difficulty: 'intermediate'
          }
        ]
      }
    };

    this.customTemplates = this.loadCustomTemplates();
  }

  /**
   * 获取分类模板
   * @param {string} category - 分类
   * @returns {Object} 分类模板
   */
  getCategoryTemplates(category) {
    return this.templates[category] || null;
  }

  /**
   * 获取所有模板
   * @returns {Object} 所有模板
   */
  getAllTemplates() {
    return this.templates;
  }

  /**
   * 根据ID获取模板
   * @param {string} templateId - 模板ID
   * @returns {Object} 模板对象
   */
  getTemplateById(templateId) {
    for (const category of Object.values(this.templates)) {
      const template = category.templates.find(t => t.id === templateId);
      if (template) {
        return template;
      }
    }
    
    // 查找自定义模板
    return this.customTemplates.find(t => t.id === templateId) || null;
  }

  /**
   * 搜索模板
   * @param {string} keyword - 搜索关键词
   * @param {Object} filters - 过滤条件
   * @returns {Array} 搜索结果
   */
  searchTemplates(keyword, filters = {}) {
    const results = [];
    const searchTerm = keyword.toLowerCase();
    
    // 搜索系统模板
    Object.entries(this.templates).forEach(([categoryKey, category]) => {
      category.templates.forEach(template => {
        if (this.matchesSearch(template, searchTerm, filters)) {
          results.push({
            ...template,
            category: categoryKey,
            categoryName: category.category,
            isCustom: false
          });
        }
      });
    });
    
    // 搜索自定义模板
    this.customTemplates.forEach(template => {
      if (this.matchesSearch(template, searchTerm, filters)) {
        results.push({
          ...template,
          isCustom: true
        });
      }
    });
    
    return results;
  }

  /**
   * 检查模板是否匹配搜索条件
   * @param {Object} template - 模板对象
   * @param {string} searchTerm - 搜索词
   * @param {Object} filters - 过滤条件
   * @returns {boolean} 是否匹配
   */
  matchesSearch(template, searchTerm, filters) {
    // 关键词匹配
    const keywordMatch = !searchTerm || 
      template.title.toLowerCase().includes(searchTerm) ||
      template.description.toLowerCase().includes(searchTerm) ||
      template.content.toLowerCase().includes(searchTerm) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm));
    
    if (!keywordMatch) return false;
    
    // 难度过滤
    if (filters.difficulty && template.difficulty !== filters.difficulty) {
      return false;
    }
    
    // 标签过滤
    if (filters.tags && filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => 
        template.tags.includes(tag)
      );
      if (!hasMatchingTag) return false;
    }
    
    return true;
  }

  /**
   * 获取推荐模板
   * @param {Object} userProfile - 用户画像
   * @returns {Array} 推荐模板列表
   */
  getRecommendedTemplates(userProfile = {}) {
    const {
      experience = 'intermediate',
      focusAreas = [],
      recentCategories = []
    } = userProfile;
    
    const recommendations = [];
    
    // 基于经验级别推荐
    Object.values(this.templates).forEach(category => {
      const suitableTemplates = category.templates.filter(template => {
        if (experience === 'beginner') {
          return template.difficulty === 'beginner' || template.difficulty === 'intermediate';
        } else if (experience === 'advanced') {
          return template.difficulty === 'intermediate' || template.difficulty === 'advanced';
        }
        return true;
      });
      
      recommendations.push(...suitableTemplates.slice(0, 2));
    });
    
    // 基于关注领域推荐
    focusAreas.forEach(area => {
      const categoryTemplates = this.getCategoryTemplates(area);
      if (categoryTemplates) {
        recommendations.push(...categoryTemplates.templates.slice(0, 1));
      }
    });
    
    // 去重并限制数量
    const uniqueRecommendations = recommendations.filter((template, index, self) => 
      index === self.findIndex(t => t.id === template.id)
    );
    
    return uniqueRecommendations.slice(0, 8);
  }

  /**
   * 添加自定义模板
   * @param {Object} template - 模板对象
   * @returns {boolean} 添加是否成功
   */
  addCustomTemplate(template) {
    const newTemplate = {
      id: this.generateTemplateId(),
      title: template.title,
      description: template.description,
      content: template.content,
      tags: template.tags || [],
      difficulty: template.difficulty || 'intermediate',
      category: template.category || 'custom',
      createdAt: Date.now(),
      isCustom: true
    };
    
    this.customTemplates.push(newTemplate);
    return this.saveCustomTemplates();
  }

  /**
   * 更新自定义模板
   * @param {string} templateId - 模板ID
   * @param {Object} updates - 更新内容
   * @returns {boolean} 更新是否成功
   */
  updateCustomTemplate(templateId, updates) {
    const index = this.customTemplates.findIndex(t => t.id === templateId);
    if (index === -1) return false;
    
    this.customTemplates[index] = {
      ...this.customTemplates[index],
      ...updates,
      updatedAt: Date.now()
    };
    
    return this.saveCustomTemplates();
  }

  /**
   * 删除自定义模板
   * @param {string} templateId - 模板ID
   * @returns {boolean} 删除是否成功
   */
  deleteCustomTemplate(templateId) {
    const index = this.customTemplates.findIndex(t => t.id === templateId);
    if (index === -1) return false;
    
    this.customTemplates.splice(index, 1);
    return this.saveCustomTemplates();
  }

  /**
   * 获取自定义模板
   * @returns {Array} 自定义模板列表
   */
  getCustomTemplates() {
    return this.customTemplates;
  }

  /**
   * 加载自定义模板
   * @returns {Array} 自定义模板列表
   */
  loadCustomTemplates() {
    try {
      const stored = wx.getStorageSync('customTemplates');
      return stored || [];
    } catch (error) {
      console.error('加载自定义模板失败:', error);
      return [];
    }
  }

  /**
   * 保存自定义模板
   * @returns {boolean} 保存是否成功
   */
  saveCustomTemplates() {
    try {
      wx.setStorageSync('customTemplates', this.customTemplates);
      return true;
    } catch (error) {
      console.error('保存自定义模板失败:', error);
      return false;
    }
  }

  /**
   * 生成模板ID
   * @returns {string} 模板ID
   */
  generateTemplateId() {
    return 'custom_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 获取模板统计
   * @returns {Object} 统计信息
   */
  getTemplateStats() {
    const systemTemplateCount = Object.values(this.templates)
      .reduce((total, category) => total + category.templates.length, 0);
    
    return {
      systemTemplates: systemTemplateCount,
      customTemplates: this.customTemplates.length,
      totalTemplates: systemTemplateCount + this.customTemplates.length,
      categoriesCount: Object.keys(this.templates).length
    };
  }

  /**
   * 导出模板
   * @param {Array} templateIds - 模板ID列表
   * @returns {string} 导出的JSON字符串
   */
  exportTemplates(templateIds) {
    const templates = templateIds.map(id => this.getTemplateById(id)).filter(Boolean);
    
    return JSON.stringify({
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      templates
    }, null, 2);
  }

  /**
   * 导入模板
   * @param {string} jsonString - JSON字符串
   * @returns {Object} 导入结果
   */
  importTemplates(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      const imported = [];
      const failed = [];
      
      if (data.templates && Array.isArray(data.templates)) {
        data.templates.forEach(template => {
          try {
            if (this.addCustomTemplate(template)) {
              imported.push(template.title);
            } else {
              failed.push(template.title);
            }
          } catch (error) {
            failed.push(template.title);
          }
        });
      }
      
      return {
        success: true,
        imported: imported.length,
        failed: failed.length,
        importedList: imported,
        failedList: failed
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 创建全局实例
const consultationTemplateManager = new ConsultationTemplateManager();

module.exports = {
  consultationTemplateManager,
  ConsultationTemplateManager
};
