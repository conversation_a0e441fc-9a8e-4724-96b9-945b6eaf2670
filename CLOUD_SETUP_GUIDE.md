# IVD智能顾问 - 云开发配置指南

## 概述

本指南将帮助您配置微信小程序云开发环境，实现用户登录和数据云端存储功能。

## 功能特性

### 🔐 微信一键登录
- 获取用户基本信息（昵称、头像等）
- 自动创建用户档案
- 支持游客模式

### ☁️ 云端数据存储
- 用户偏好设置同步
- 聊天记录云端备份
- API密钥安全存储
- 自动/手动数据同步

### 📊 数据管理
- 实时数据同步
- 数据恢复功能
- 离线数据缓存
- 数据导出功能

## 配置步骤

### 第一步：开通云开发

1. **登录微信公众平台**
   - 访问 https://mp.weixin.qq.com
   - 使用小程序管理员账号登录

2. **开通云开发**
   - 进入小程序后台
   - 点击"云开发" -> "开通"
   - 选择合适的套餐（建议先选择免费版）
   - 创建云开发环境

3. **获取环境ID**
   - 记录云开发环境ID（格式如：cloud1-xxx）
   - 后续配置需要使用此ID

### 第二步：配置云开发环境

1. **更新环境ID**
   ```javascript
   // 在以下文件中替换 'your-env-id' 为实际的环境ID：
   // - pages/login/login.js
   // - utils/cloudSync.js
   // - cloudbaserc.json
   ```

2. **配置数据库权限**
   - 在云开发控制台进入"数据库"
   - 点击"设置" -> "权限设置"
   - 设置以下集合权限：
     ```
     users: 仅创建者可读写
     chat_history: 仅创建者可读写
     user_preferences: 仅创建者可读写
     api_keys: 仅创建者可读写
     user_feedback: 仅创建者可读写
     system_logs: 仅管理员可读写
     ```

### 第三步：部署云函数

1. **在微信开发者工具中**
   - 打开项目
   - 右键点击 `cloudfunctions` 文件夹
   - 选择"当前环境" -> 选择你的云开发环境

2. **部署各个云函数**
   ```bash
   # 依次右键点击以下文件夹并选择"上传并部署：云端安装依赖"
   cloudfunctions/login/
   cloudfunctions/userdata/
   cloudfunctions/initDB/
   ```

3. **初始化数据库**
   - 在云开发控制台的"云函数"页面
   - 找到 `initDB` 函数
   - 点击"测试"按钮执行初始化

### 第四步：配置小程序

1. **更新 app.json**
   ```json
   {
     "cloud": true,
     "permission": {
       "scope.userInfo": {
         "desc": "用于完善用户资料"
       }
     }
   }
   ```

2. **更新 project.config.json**
   ```json
   {
     "cloudfunctionRoot": "cloudfunctions/",
     "cloudfunctionTemplateRoot": "cloudfunctionTemplate"
   }
   ```

### 第五步：测试功能

1. **测试登录功能**
   - 在真机上测试微信登录
   - 检查用户信息是否正确获取
   - 验证数据库中是否创建用户记录

2. **测试数据同步**
   - 修改用户偏好设置
   - 发送聊天消息
   - 检查云端数据是否同步

3. **测试数据恢复**
   - 清除本地数据
   - 使用数据恢复功能
   - 验证数据是否正确恢复

## 数据库结构

### users 集合
```javascript
{
  _id: "用户ID",
  openid: "微信openid",
  unionid: "微信unionid",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  gender: 1, // 性别
  country: "国家",
  province: "省份",
  city: "城市",
  language: "语言",
  preferences: {
    selectedAIModel: "gpt-3.5-turbo",
    professionalField: "医疗器械研发",
    experienceLevel: "中级（3-5年）",
    focusAreas: ["产品研发", "注册申报"]
  },
  apiKeys: {
    openai: "加密后的API密钥",
    anthropic: "加密后的API密钥",
    google: "加密后的API密钥"
  },
  createdAt: "创建时间",
  updatedAt: "更新时间",
  lastLoginTime: "最后登录时间",
  loginCount: 1,
  status: "active"
}
```

### chat_history 集合
```javascript
{
  _id: "记录ID",
  openid: "用户openid",
  sessionId: "会话ID",
  messages: [
    {
      id: "消息ID",
      role: "user|assistant",
      content: "消息内容",
      timestamp: 1640995200000,
      model: "gpt-3.5-turbo"
    }
  ],
  model: "使用的AI模型",
  category: "咨询分类",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

## 安全注意事项

### 1. API密钥安全
- API密钥在云端使用Base64编码存储
- 建议实施更强的加密算法
- 定期轮换API密钥
- 监控API使用情况

### 2. 数据权限
- 确保数据库权限设置正确
- 用户只能访问自己的数据
- 敏感操作需要额外验证

### 3. 云函数安全
- 验证用户身份
- 限制请求频率
- 记录操作日志
- 异常监控

## 常见问题

### Q1: 云函数调用失败
**A:** 检查以下项目：
- 云开发环境ID是否正确
- 云函数是否正确部署
- 网络连接是否正常
- 用户是否有权限

### Q2: 数据同步失败
**A:** 可能原因：
- 用户未登录
- 网络连接问题
- 云函数异常
- 数据格式错误

### Q3: 登录失败
**A:** 检查项目：
- 小程序是否已发布
- 用户是否授权
- 云函数是否正常
- 数据库权限设置

## 监控和维护

### 1. 云函数监控
- 在云开发控制台查看调用统计
- 监控错误率和响应时间
- 设置告警规则

### 2. 数据库监控
- 监控存储使用量
- 查看读写请求统计
- 定期备份重要数据

### 3. 用户反馈
- 收集用户使用反馈
- 分析常见问题
- 持续优化体验

## 成本优化

### 1. 免费额度
- 云函数：每月100万次调用
- 数据库：每月2GB存储
- CDN：每月5GB流量

### 2. 优化建议
- 合理设置同步频率
- 压缩存储数据
- 缓存常用数据
- 定期清理过期数据

## 升级计划

### 短期优化
- [ ] 实施更强的数据加密
- [ ] 添加数据备份机制
- [ ] 优化同步性能
- [ ] 增加错误重试机制

### 长期规划
- [ ] 支持多端数据同步
- [ ] 实现数据分析功能
- [ ] 添加用户行为统计
- [ ] 构建推荐系统

---

**配置完成后，请及时测试所有功能并更新本文档。**
