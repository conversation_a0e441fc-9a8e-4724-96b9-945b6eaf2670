// utils/logger.js
// 安全的日志管理工具

/**
 * 日志管理器
 * 解决微信小程序日志文件访问问题
 */
class Logger {
  constructor() {
    this.isProduction = false // 生产环境标志
    this.logLevel = 'info' // 日志级别
    this.maxLogSize = 100 // 最大日志条数
    this.logs = [] // 内存日志缓存
    
    // 检测环境
    this.detectEnvironment()
  }

  /**
   * 检测运行环境
   */
  detectEnvironment() {
    try {
      // 通过账号信息检测是否为生产环境
      const accountInfo = wx.getAccountInfoSync()
      this.isProduction = accountInfo.miniProgram.envVersion === 'release'
    } catch (error) {
      // 如果获取失败，默认为开发环境
      this.isProduction = false
    }
  }

  /**
   * 安全的console.log包装
   */
  log(...args) {
    this.writeLog('log', args)
  }

  /**
   * 安全的console.info包装
   */
  info(...args) {
    this.writeLog('info', args)
  }

  /**
   * 安全的console.warn包装
   */
  warn(...args) {
    this.writeLog('warn', args)
  }

  /**
   * 安全的console.error包装
   */
  error(...args) {
    this.writeLog('error', args)
  }

  /**
   * 写入日志
   */
  writeLog(level, args) {
    try {
      const timestamp = new Date().toISOString()
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')

      // 创建日志条目
      const logEntry = {
        timestamp,
        level,
        message,
        page: this.getCurrentPage()
      }

      // 添加到内存缓存
      this.addToCache(logEntry)

      // 在开发环境中输出到控制台
      if (!this.isProduction) {
        this.outputToConsole(level, timestamp, message)
      }

      // 在生产环境中可以发送到远程日志服务
      if (this.isProduction && level === 'error') {
        this.sendToRemoteLog(logEntry)
      }

    } catch (error) {
      // 如果日志记录失败，使用原生console（但要防止递归）
      if (typeof console !== 'undefined' && console.error) {
        console.error('Logger error:', error)
      }
    }
  }

  /**
   * 输出到控制台
   */
  outputToConsole(level, timestamp, message) {
    const prefix = `[${timestamp}]`
    
    switch (level) {
      case 'error':
        console.error && console.error(prefix, message)
        break
      case 'warn':
        console.warn && console.warn(prefix, message)
        break
      case 'info':
        console.info && console.info(prefix, message)
        break
      default:
        console.log && console.log(prefix, message)
    }
  }

  /**
   * 添加到内存缓存
   */
  addToCache(logEntry) {
    this.logs.push(logEntry)
    
    // 限制缓存大小
    if (this.logs.length > this.maxLogSize) {
      this.logs.shift()
    }
  }

  /**
   * 获取当前页面路径
   */
  getCurrentPage() {
    try {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        return pages[pages.length - 1].route
      }
    } catch (error) {
      // 忽略错误
    }
    return 'unknown'
  }

  /**
   * 发送到远程日志服务
   */
  async sendToRemoteLog(logEntry) {
    try {
      // 这里可以集成远程日志服务
      // 比如发送到云函数或第三方日志服务
      await wx.cloud.callFunction({
        name: 'logger',
        data: {
          action: 'log',
          data: logEntry
        }
      })
    } catch (error) {
      // 静默处理远程日志错误
    }
  }

  /**
   * 获取日志历史
   */
  getLogs(level = null) {
    if (level) {
      return this.logs.filter(log => log.level === level)
    }
    return [...this.logs]
  }

  /**
   * 清空日志缓存
   */
  clearLogs() {
    this.logs = []
  }

  /**
   * 导出日志
   */
  exportLogs() {
    try {
      const logsText = this.logs.map(log => 
        `[${log.timestamp}] [${log.level.toUpperCase()}] [${log.page}] ${log.message}`
      ).join('\n')
      
      return logsText
    } catch (error) {
      return 'Export failed: ' + error.message
    }
  }

  /**
   * 设置日志级别
   */
  setLogLevel(level) {
    this.logLevel = level
  }
}

// 创建全局日志实例
const logger = new Logger()

/**
 * 安全的存储操作包装
 */
class SafeStorage {
  /**
   * 安全的getStorageSync
   */
  static getSync(key, defaultValue = null) {
    try {
      const value = wx.getStorageSync(key)
      return value || defaultValue
    } catch (error) {
      logger.error('Storage getSync error:', error, 'key:', key)
      return defaultValue
    }
  }

  /**
   * 安全的setStorageSync
   */
  static setSync(key, value) {
    try {
      wx.setStorageSync(key, value)
      return true
    } catch (error) {
      logger.error('Storage setSync error:', error, 'key:', key)
      return false
    }
  }

  /**
   * 安全的removeStorageSync
   */
  static removeSync(key) {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      logger.error('Storage removeSync error:', error, 'key:', key)
      return false
    }
  }

  /**
   * 安全的clearStorageSync
   */
  static clearSync() {
    try {
      wx.clearStorageSync()
      return true
    } catch (error) {
      logger.error('Storage clearSync error:', error)
      return false
    }
  }

  /**
   * 获取存储信息
   */
  static getInfo() {
    try {
      return wx.getStorageInfoSync()
    } catch (error) {
      logger.error('Storage getInfo error:', error)
      return {
        keys: [],
        currentSize: 0,
        limitSize: 10240
      }
    }
  }
}

/**
 * 错误边界处理
 */
class ErrorBoundary {
  /**
   * 全局错误处理
   */
  static setup() {
    // 监听小程序错误
    wx.onError && wx.onError((error) => {
      logger.error('Global error:', error)
    })

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection && wx.onUnhandledRejection((res) => {
      logger.error('Unhandled promise rejection:', res.reason)
    })
  }

  /**
   * 安全执行函数
   */
  static safeExecute(fn, context = null, ...args) {
    try {
      if (typeof fn === 'function') {
        return fn.apply(context, args)
      }
    } catch (error) {
      logger.error('Safe execute error:', error)
      return null
    }
  }

  /**
   * 安全的异步执行
   */
  static async safeExecuteAsync(fn, context = null, ...args) {
    try {
      if (typeof fn === 'function') {
        return await fn.apply(context, args)
      }
    } catch (error) {
      logger.error('Safe execute async error:', error)
      return null
    }
  }
}

// 设置全局错误处理
ErrorBoundary.setup()

module.exports = {
  logger,
  SafeStorage,
  ErrorBoundary
}
