// pages/models/models.js
const app = getApp()
const { checkAccess } = require('../../utils/accessControl');

Page({
  data: {
    selectedModelId: 'deepseek-v3-0324',
    currentModel: {},
    availableModels: [
      {
        id: 'deepseek-v3-0324',
        name: 'DeepSeek-V3-0324',
        description: 'DeepSeek最新V3模型，推理能力强，性价比极高',
        icon: '🚀',
        status: 'online',
        statusText: '在线',
        features: ['强推理能力', '高性价比', '中文优化'],
        metrics: {
          speed: 95,
          accuracy: 95,
          expertise: 92
        },
        useCases: ['专业咨询', '复杂推理', '技术分析'],
        provider: 'deepseek',
        color: '#1890FF'
      },
      {
        id: 'deepseek-r1-0528',
        name: 'DeepSeek-R1-0528',
        description: 'DeepSeek推理专用模型，逻辑分析能力突出',
        icon: '🧠',
        status: 'online',
        statusText: '在线',
        features: ['逻辑推理', '数学计算', '科学分析'],
        metrics: {
          speed: 85,
          accuracy: 98,
          expertise: 95
        },
        useCases: ['逻辑推理', '数学分析', '科学计算'],
        provider: 'deepseek',
        color: '#1890FF'
      },
      {
        id: 'qwen3',
        name: 'Qwen3',
        description: '通义千问3.0，全面升级的智能助手',
        icon: '🌟',
        status: 'online',
        statusText: '在线',
        features: ['全面升级', '多模态', '高效推理'],
        metrics: {
          speed: 90,
          accuracy: 92,
          expertise: 88
        },
        useCases: ['多模态分析', '综合咨询', '智能问答'],
        provider: 'alibaba',
        color: '#FF6A00'
      },
      {
        id: 'qwen-max',
        name: 'Qwen Max',
        description: '通义千问旗舰版，顶级性能',
        icon: '👑',
        status: 'online',
        statusText: '在线',
        features: ['顶级性能', '复杂推理', '专业级'],
        metrics: {
          speed: 75,
          accuracy: 95,
          expertise: 92
        },
        useCases: ['复杂分析', '专业建议', '高端咨询'],
        provider: 'alibaba',
        color: '#FF6A00'
      }
    ],
    showApiConfig: false // 用户端不显示API配置
  },

  async onLoad(options) {
    console.log('模型选择页面加载');
    await this.initializeAccessControl();
    this.loadCurrentSettings();
  },

  /**
   * 初始化访问控制
   */
  async initializeAccessControl() {
    try {
      await checkAccess.init(app.globalData.userInfo);
      await this.updateModelAccess();
    } catch (error) {
      console.error('初始化访问控制失败:', error);
    }
  },

  /**
   * 更新模型访问权限
   */
  async updateModelAccess() {
    const models = this.data.models.map(async (model) => {
      const accessResult = await checkAccess.model(model.id);
      return {
        ...model,
        hasAccess: accessResult.allowed,
        accessReason: accessResult.reason,
        requiredPlan: accessResult.requiredPlan
      };
    });

    const updatedModels = await Promise.all(models);
    this.setData({
      models: updatedModels
    });
  },

  // 加载当前设置
  loadCurrentSettings() {
    const selectedModelId = app.globalData.selectedAIModel || 'deepseek-v3-0324';

    // 设置当前选中的模型
    const currentModel = this.data.availableModels.find(m => m.id === selectedModelId);

    this.setData({
      selectedModelId,
      currentModel: currentModel || this.data.availableModels[0]
    });
  },

  // 选择模型
  async selectModel(e) {
    const model = e.currentTarget.dataset.model;

    // 检查访问权限
    const accessResult = await checkAccess.model(model.id);
    if (!accessResult.allowed) {
      this.showAccessDenied(accessResult, model);
      return;
    }

    this.setData({
      selectedModelId: model.id,
      currentModel: model
    });

    // 更新全局数据
    app.globalData.selectedAIModel = model.id;

    wx.showToast({
      title: `已选择${model.name}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 显示访问被拒绝的提示
   */
  showAccessDenied(accessResult, model) {
    let title = '模型访问受限';
    let content = `${model.name} 需要 ${accessResult.requiredPlan || '付费'} 订阅`;

    if (accessResult.reason) {
      content = accessResult.reason;
    }

    wx.showModal({
      title,
      content,
      showCancel: true,
      cancelText: '稍后再说',
      confirmText: '立即升级',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/subscription/subscription'
          });
        }
      }
    });
  },



  // 保存设置
  saveSettings() {
    // 保存到本地存储
    app.saveUserSettings();

    wx.showToast({
      title: '设置已保存',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 获取模型使用统计
  getModelStats() {
    // 这里可以添加模型使用统计的逻辑
    return {
      totalChats: 0,
      favoriteModel: this.data.selectedModelId
    };
  },

  // 重置所有设置
  resetSettings() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置模型选择吗？将恢复为默认模型。',
      success: (res) => {
        if (res.confirm) {
          // 重置为默认模型
          const defaultModel = this.data.availableModels[0];

          this.setData({
            selectedModelId: defaultModel.id,
            currentModel: defaultModel
          });

          // 更新全局数据
          app.globalData.selectedAIModel = defaultModel.id;

          wx.showToast({
            title: '设置已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  // 查看模型详情
  viewModelDetails(e) {
    const model = e.currentTarget.dataset.model;
    
    wx.showModal({
      title: model.name,
      content: `${model.description}\n\n特性：${model.features.join('、')}\n\n适用场景：${model.useCases.join('、')}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 多AI模型选择',
      path: '/pages/index/index'
    };
  }
});
