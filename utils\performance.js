// utils/performance.js
// 性能优化工具

/**
 * 性能监控和优化管理器
 */
class PerformanceManager {
  constructor() {
    this.metrics = [];
    this.optimizations = [];
    this.isMonitoring = false;
    this.monitoringInterval = null;
  }

  /**
   * 开始性能监控
   * @param {number} interval - 监控间隔（毫秒）
   */
  startMonitoring(interval = 5000) {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, interval);
    
    console.log('性能监控已启动');
  }

  /**
   * 停止性能监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('性能监控已停止');
  }

  /**
   * 收集性能指标
   */
  collectMetrics() {
    const metrics = {
      timestamp: Date.now(),
      memory: this.getMemoryMetrics(),
      storage: this.getStorageMetrics(),
      network: this.getNetworkMetrics(),
      render: this.getRenderMetrics(),
      user: this.getUserMetrics()
    };

    this.metrics.push(metrics);
    
    // 限制指标数量
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-50);
    }

    // 检查是否需要优化
    this.checkOptimizationNeeds(metrics);
  }

  /**
   * 获取内存指标
   */
  getMemoryMetrics() {
    // 微信小程序无法直接获取内存使用情况
    // 通过间接方式估算
    const app = getApp();
    const chatHistorySize = app.globalData.chatHistory.length;
    const estimatedMemory = chatHistorySize * 0.5; // 估算每条消息0.5KB
    
    return {
      estimatedUsage: estimatedMemory,
      chatHistoryCount: chatHistorySize,
      unit: 'KB'
    };
  }

  /**
   * 获取存储指标
   */
  getStorageMetrics() {
    try {
      const info = wx.getStorageInfoSync();
      return {
        currentSize: info.currentSize,
        limitSize: info.limitSize,
        keys: info.keys,
        usageRatio: info.currentSize / info.limitSize,
        unit: 'KB'
      };
    } catch (error) {
      return {
        error: error.message
      };
    }
  }

  /**
   * 获取网络指标
   */
  getNetworkMetrics() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            networkType: res.networkType,
            isConnected: res.networkType !== 'none'
          });
        },
        fail: () => {
          resolve({
            networkType: 'unknown',
            isConnected: false
          });
        }
      });
    });
  }

  /**
   * 获取渲染指标
   */
  getRenderMetrics() {
    // 模拟渲染性能指标
    return {
      fps: 60 - Math.random() * 10, // 模拟FPS
      renderTime: 16 + Math.random() * 10, // 模拟渲染时间
      unit: 'ms'
    };
  }

  /**
   * 获取用户行为指标
   */
  getUserMetrics() {
    const app = getApp();
    return {
      sessionDuration: Date.now() - (app.globalData.sessionStart || Date.now()),
      messagesSent: app.globalData.chatHistory.filter(m => m.role === 'user').length,
      modelsUsed: new Set(app.globalData.chatHistory.map(m => m.model)).size
    };
  }

  /**
   * 检查优化需求
   * @param {Object} metrics - 性能指标
   */
  checkOptimizationNeeds(metrics) {
    const optimizations = [];

    // 存储优化检查
    if (metrics.storage.usageRatio > 0.8) {
      optimizations.push({
        type: 'storage',
        priority: 'high',
        action: 'cleanup',
        message: '存储空间不足，需要清理数据'
      });
    }

    // 内存优化检查
    if (metrics.memory.chatHistoryCount > 500) {
      optimizations.push({
        type: 'memory',
        priority: 'medium',
        action: 'compress',
        message: '聊天记录过多，建议压缩或清理'
      });
    }

    // 网络优化检查
    if (!metrics.network.isConnected) {
      optimizations.push({
        type: 'network',
        priority: 'high',
        action: 'offline_mode',
        message: '网络连接异常，启用离线模式'
      });
    }

    // 渲染优化检查
    if (metrics.render.fps < 30) {
      optimizations.push({
        type: 'render',
        priority: 'medium',
        action: 'reduce_animation',
        message: '渲染性能不佳，建议减少动画效果'
      });
    }

    // 执行优化
    optimizations.forEach(opt => this.executeOptimization(opt));
  }

  /**
   * 执行优化操作
   * @param {Object} optimization - 优化配置
   */
  executeOptimization(optimization) {
    switch (optimization.action) {
      case 'cleanup':
        this.cleanupStorage();
        break;
      case 'compress':
        this.compressChatHistory();
        break;
      case 'offline_mode':
        this.enableOfflineMode();
        break;
      case 'reduce_animation':
        this.reduceAnimations();
        break;
    }

    this.optimizations.push({
      ...optimization,
      executedAt: Date.now()
    });
  }

  /**
   * 清理存储空间
   */
  cleanupStorage() {
    try {
      // 清理过期的缓存数据
      const { responseCache } = require('./apiUtils');
      responseCache.clear();
      
      // 清理旧的错误日志
      const { errorHandler } = require('./apiUtils');
      errorHandler.clearErrorLog();
      
      console.log('存储空间清理完成');
    } catch (error) {
      console.error('存储清理失败:', error);
    }
  }

  /**
   * 压缩聊天记录
   */
  compressChatHistory() {
    try {
      const app = getApp();
      const chatHistory = app.globalData.chatHistory;
      
      // 只保留最近的200条记录
      if (chatHistory.length > 200) {
        app.globalData.chatHistory = chatHistory.slice(-200);
        app.saveUserSettings();
        console.log('聊天记录已压缩');
      }
    } catch (error) {
      console.error('聊天记录压缩失败:', error);
    }
  }

  /**
   * 启用离线模式
   */
  enableOfflineMode() {
    try {
      const app = getApp();
      app.globalData.offlineMode = true;
      console.log('离线模式已启用');
    } catch (error) {
      console.error('启用离线模式失败:', error);
    }
  }

  /**
   * 减少动画效果
   */
  reduceAnimations() {
    try {
      const { userPreferencesManager } = require('./userPreferences');
      userPreferencesManager.setPreference('display', 'messageAnimation', false);
      console.log('动画效果已减少');
    } catch (error) {
      console.error('减少动画失败:', error);
    }
  }

  /**
   * 优化图片加载
   */
  optimizeImageLoading() {
    // 实现图片懒加载和压缩
    return {
      lazyLoading: true,
      compression: 0.8,
      maxWidth: 750,
      maxHeight: 750
    };
  }

  /**
   * 优化API请求
   */
  optimizeAPIRequests() {
    const { responseCache } = require('./apiUtils');
    
    return {
      enableCache: true,
      cacheTTL: 5 * 60 * 1000, // 5分钟
      requestTimeout: 30000,
      maxRetries: 3
    };
  }

  /**
   * 优化数据存储
   */
  optimizeDataStorage() {
    return {
      compression: true,
      maxHistoryLength: 1000,
      autoCleanup: true,
      cleanupInterval: 24 * 60 * 60 * 1000 // 24小时
    };
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    if (this.metrics.length === 0) {
      return {
        message: '暂无性能数据',
        recommendations: ['启动性能监控以收集数据']
      };
    }

    const latest = this.metrics[this.metrics.length - 1];
    const recommendations = this.generateRecommendations();

    return {
      timestamp: new Date().toISOString(),
      currentMetrics: latest,
      trends: this.analyzeTrends(),
      optimizations: this.optimizations.slice(-10),
      recommendations,
      score: this.calculatePerformanceScore()
    };
  }

  /**
   * 分析性能趋势
   * @returns {Object} 趋势分析
   */
  analyzeTrends() {
    if (this.metrics.length < 2) {
      return { message: '数据不足，无法分析趋势' };
    }

    const recent = this.metrics.slice(-10);
    const storageUsage = recent.map(m => m.storage.usageRatio);
    const memoryUsage = recent.map(m => m.memory.estimatedUsage);

    return {
      storage: {
        trend: this.calculateTrend(storageUsage),
        current: storageUsage[storageUsage.length - 1]
      },
      memory: {
        trend: this.calculateTrend(memoryUsage),
        current: memoryUsage[memoryUsage.length - 1]
      }
    };
  }

  /**
   * 计算趋势
   * @param {Array} values - 数值数组
   * @returns {string} 趋势描述
   */
  calculateTrend(values) {
    if (values.length < 2) return 'stable';
    
    const first = values[0];
    const last = values[values.length - 1];
    const change = (last - first) / first;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * 生成性能建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.metrics.length === 0) {
      return ['启动性能监控以获得个性化建议'];
    }

    const latest = this.metrics[this.metrics.length - 1];

    // 存储建议
    if (latest.storage.usageRatio > 0.7) {
      recommendations.push('定期清理聊天记录和缓存数据');
    }

    // 内存建议
    if (latest.memory.chatHistoryCount > 300) {
      recommendations.push('考虑导出并清理旧的聊天记录');
    }

    // 网络建议
    if (!latest.network.isConnected) {
      recommendations.push('检查网络连接，考虑使用离线功能');
    }

    // 通用建议
    recommendations.push('定期重启小程序以释放内存');
    recommendations.push('在网络较差时使用较小的AI模型');

    return recommendations;
  }

  /**
   * 计算性能评分
   * @returns {number} 性能评分 (0-100)
   */
  calculatePerformanceScore() {
    if (this.metrics.length === 0) return 0;

    const latest = this.metrics[this.metrics.length - 1];
    let score = 100;

    // 存储评分
    score -= latest.storage.usageRatio * 30;

    // 内存评分
    if (latest.memory.chatHistoryCount > 500) {
      score -= 20;
    } else if (latest.memory.chatHistoryCount > 300) {
      score -= 10;
    }

    // 网络评分
    if (!latest.network.isConnected) {
      score -= 25;
    }

    // 渲染评分
    if (latest.render.fps < 30) {
      score -= 15;
    } else if (latest.render.fps < 45) {
      score -= 5;
    }

    return Math.max(0, Math.round(score));
  }

  /**
   * 重置性能数据
   */
  resetMetrics() {
    this.metrics = [];
    this.optimizations = [];
    console.log('性能数据已重置');
  }

  /**
   * 导出性能数据
   * @returns {string} JSON格式的性能数据
   */
  exportMetrics() {
    return JSON.stringify({
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      metrics: this.metrics,
      optimizations: this.optimizations,
      report: this.getPerformanceReport()
    }, null, 2);
  }
}

// 创建全局实例
const performanceManager = new PerformanceManager();

module.exports = {
  performanceManager,
  PerformanceManager
};
