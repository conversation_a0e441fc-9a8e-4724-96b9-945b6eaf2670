/* pages/models/models.wxss */

.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 页面头部 - 小程序风格 */
.header-section {
  background: var(--primary-gradient);
  padding: var(--spacing-xxl) var(--spacing-page);
  padding-top: calc(var(--spacing-xxl) + env(safe-area-inset-top));
  color: var(--text-inverse);
}

.header-content {
  text-align: center;
}

.page-title {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.page-desc {
  display: block;
  font-size: var(--font-size-base);
  opacity: 0.9;
  line-height: 1.5;
}

/* 当前模型区域 - 小程序卡片风格 */
.current-model-section {
  padding: var(--spacing-page);
  background: var(--bg-secondary);
}

.section-title {
  font-size: var(--font-size-title);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-desc {
  font-size: var(--font-size-sm) !important;
  color: var(--text-secondary) !important;
  font-weight: normal !important;
}

.current-model {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  background: var(--primary-color);
  color: var(--text-inverse);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
}

.current-model .model-info {
  flex: 1;
}

.current-model .model-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.current-model .model-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.current-model .model-status {
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  font-size: 22rpx;
}

.current-model .model-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.current-model .model-features {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.current-model .feature-item {
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  font-size: 22rpx;
}

.current-model .model-icon {
  font-size: 60rpx;
  margin-left: 20rpx;
}

/* 模型列表区域 - 小程序卡片列表 */
.models-section {
  padding: 0 var(--spacing-page) var(--spacing-page);
}

.models-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.model-item {
  position: relative;
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-card);
  border: 1rpx solid var(--border-light);
  transition: all var(--transition-base);
}

.model-item:active {
  transform: scale(0.98);
  background: var(--bg-tertiary);
}

.model-item.selected {
  border-color: var(--primary-color);
  background: rgba(7, 193, 96, 0.05);
}

.model-content {
  width: 100%;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.model-basic-info {
  flex: 1;
}

.model-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.model-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  display: inline-block;
}

.model-status.online {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.model-status.offline {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.model-icon {
  font-size: 50rpx;
  margin-left: 20rpx;
}

.model-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.model-features {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.feature-item {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666;
}

/* 性能指标 */
.model-metrics {
  margin-bottom: 20rpx;
}

.metric-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.metric-label {
  width: 120rpx;
  font-size: 24rpx;
  color: #666;
}

.metric-bar {
  flex: 1;
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.metric-value {
  width: 60rpx;
  font-size: 22rpx;
  color: #666;
  text-align: right;
}

/* 适用场景 */
.use-cases {
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.use-cases-title {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.use-cases-content {
  font-size: 24rpx;
  color: #333;
}

/* 选中标识 */
.selection-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  background: #1976D2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* API配置区域 */
.api-config-section {
  padding: 30rpx;
  background: #f5f5f5;
}

.api-config-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.config-item {
  padding: 30rpx;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.provider-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.config-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.config-status.configured {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.config-status.not-configured {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.api-key-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  background: #fff;
  box-sizing: border-box;
}

.api-key-input:focus {
  border-color: #1976D2;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.toggle-visibility,
.test-connection {
  padding: 12rpx 24rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  flex: 1;
}

.toggle-visibility:active,
.test-connection:active {
  background: #e0e0e0;
}

.test-connection {
  background: #1976D2;
  color: #fff;
}

.test-connection:active {
  background: #1565C0;
}

/* 保存按钮 */
.save-section {
  padding: 30rpx;
}

.save-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 50rpx;
}

/* 帮助信息 */
.help-section {
  padding: 30rpx;
  background: #f5f5f5;
}

.help-card {
  padding: 30rpx;
}

.help-header {
  margin-bottom: 20rpx;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.help-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
