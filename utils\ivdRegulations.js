// utils/ivdRegulations.js
// IVD法规数据库

/**
 * IVD法规管理器
 */
class IVDRegulationManager {
  constructor() {
    this.regulations = {
      // 中国NMPA法规
      china: {
        name: '中国NMPA',
        authority: '国家药品监督管理局',
        website: 'https://www.nmpa.gov.cn',
        classification: {
          class1: {
            name: 'I类产品',
            riskLevel: '低风险',
            procedure: '备案',
            timeline: '20个工作日',
            requirements: [
              '产品技术要求',
              '检验报告',
              '临床评价资料',
              '产品说明书和标签样稿',
              '质量管理体系文件'
            ]
          },
          class2: {
            name: 'II类产品',
            riskLevel: '中等风险',
            procedure: '注册',
            timeline: '60个工作日',
            requirements: [
              '产品技术要求',
              '研究资料',
              '临床评价资料',
              '产品说明书和标签样稿',
              '质量管理体系文件',
              '风险管理资料'
            ]
          },
          class3: {
            name: 'III类产品',
            riskLevel: '高风险',
            procedure: '注册',
            timeline: '90个工作日',
            requirements: [
              '产品技术要求',
              '研究资料',
              '临床试验资料',
              '产品说明书和标签样稿',
              '质量管理体系文件',
              '风险管理资料',
              '临床试验方案'
            ]
          }
        },
        keyRegulations: [
          '医疗器械监督管理条例',
          '体外诊断试剂注册管理办法',
          '医疗器械分类目录',
          '医疗器械临床试验质量管理规范'
        ]
      },

      // 欧盟CE认证
      eu: {
        name: '欧盟CE认证',
        authority: '欧盟委员会',
        website: 'https://ec.europa.eu',
        regulation: 'IVDR 2017/746',
        classification: {
          classA: {
            name: 'A类产品',
            riskLevel: '低风险',
            procedure: '自我声明',
            timeline: '3-6个月',
            requirements: [
              '技术文档',
              '符合性声明',
              'CE标识',
              '使用说明书'
            ]
          },
          classB: {
            name: 'B类产品',
            riskLevel: '中低风险',
            procedure: '公告机构认证',
            timeline: '6-12个月',
            requirements: [
              '技术文档',
              '质量管理体系认证',
              '公告机构评估',
              '符合性声明'
            ]
          },
          classC: {
            name: 'C类产品',
            riskLevel: '中高风险',
            procedure: '公告机构认证',
            timeline: '12-18个月',
            requirements: [
              '技术文档',
              '质量管理体系认证',
              '产品认证',
              '临床证据'
            ]
          },
          classD: {
            name: 'D类产品',
            riskLevel: '高风险',
            procedure: '公告机构认证',
            timeline: '18-24个月',
            requirements: [
              '技术文档',
              '质量管理体系认证',
              '产品认证',
              '临床试验数据',
              '专家小组评估'
            ]
          }
        },
        keyRegulations: [
          'IVDR 2017/746',
          'MDR 2017/745',
          'ISO 13485',
          'ISO 14971'
        ]
      },

      // 美国FDA
      usa: {
        name: '美国FDA',
        authority: '美国食品药品监督管理局',
        website: 'https://www.fda.gov',
        classification: {
          class1: {
            name: 'I类产品',
            riskLevel: '低风险',
            procedure: '510(k)豁免或510(k)',
            timeline: '90天',
            requirements: [
              '510(k)申请（如需要）',
              '产品标签',
              '使用说明',
              '质量体系法规'
            ]
          },
          class2: {
            name: 'II类产品',
            riskLevel: '中等风险',
            procedure: '510(k)申请',
            timeline: '90天',
            requirements: [
              '510(k)申请',
              '实质等效性证明',
              '性能数据',
              '标签和说明书',
              '质量体系法规'
            ]
          },
          class3: {
            name: 'III类产品',
            riskLevel: '高风险',
            procedure: 'PMA申请',
            timeline: '180天',
            requirements: [
              'PMA申请',
              '临床试验数据',
              '制造信息',
              '风险效益分析',
              '质量体系法规'
            ]
          }
        },
        keyRegulations: [
          '21 CFR Part 820 (QSR)',
          '21 CFR Part 807 (510k)',
          '21 CFR Part 814 (PMA)',
          'FDA Guidance Documents'
        ]
      }
    };

    this.standards = {
      quality: {
        'ISO 13485': {
          title: '医疗器械质量管理体系',
          scope: '医疗器械设计、开发、生产、安装和服务',
          keyRequirements: [
            '管理职责',
            '资源管理',
            '产品实现',
            '测量、分析和改进'
          ]
        },
        'ISO 14971': {
          title: '医疗器械风险管理',
          scope: '医疗器械风险管理过程',
          keyRequirements: [
            '风险分析',
            '风险评价',
            '风险控制',
            '残余风险评价'
          ]
        }
      },
      testing: {
        'ISO 15189': {
          title: '医学实验室质量和能力要求',
          scope: '临床实验室质量管理',
          keyRequirements: [
            '管理要求',
            '技术要求',
            '检验前过程',
            '检验过程'
          ]
        },
        'CLSI': {
          title: '临床和实验室标准协会标准',
          scope: '临床实验室标准化',
          keyRequirements: [
            '方法学验证',
            '质量控制',
            '参考区间',
            '不确定度评估'
          ]
        }
      }
    };

    this.commonRequirements = {
      technicalDocumentation: {
        title: '技术文档要求',
        documents: [
          '产品描述和预期用途',
          '设计和制造信息',
          '风险管理文件',
          '验证和确认数据',
          '临床评价或临床数据',
          '标签和使用说明',
          '上市后监督计划'
        ]
      },
      clinicalEvidence: {
        title: '临床证据要求',
        types: [
          '临床试验数据',
          '临床文献数据',
          '临床经验数据',
          '等效性数据'
        ]
      },
      qualitySystem: {
        title: '质量体系要求',
        elements: [
          '文件控制',
          '管理职责',
          '资源管理',
          '产品实现',
          '监视和测量'
        ]
      }
    };
  }

  /**
   * 获取特定地区的法规信息
   * @param {string} region - 地区代码
   * @returns {Object} 法规信息
   */
  getRegulation(region) {
    return this.regulations[region] || null;
  }

  /**
   * 获取产品分类信息
   * @param {string} region - 地区代码
   * @param {string} classType - 产品类别
   * @returns {Object} 分类信息
   */
  getClassification(region, classType) {
    const regulation = this.getRegulation(region);
    if (!regulation || !regulation.classification) return null;
    
    return regulation.classification[classType] || null;
  }

  /**
   * 获取申报时间线
   * @param {string} region - 地区代码
   * @param {string} classType - 产品类别
   * @returns {string} 时间线
   */
  getTimeline(region, classType) {
    const classification = this.getClassification(region, classType);
    return classification ? classification.timeline : null;
  }

  /**
   * 获取申报要求
   * @param {string} region - 地区代码
   * @param {string} classType - 产品类别
   * @returns {Array} 要求列表
   */
  getRequirements(region, classType) {
    const classification = this.getClassification(region, classType);
    return classification ? classification.requirements : [];
  }

  /**
   * 比较不同地区的要求
   * @param {Array} regions - 地区列表
   * @param {string} classType - 产品类别
   * @returns {Object} 比较结果
   */
  compareRegulations(regions, classType) {
    const comparison = {};
    
    regions.forEach(region => {
      const classification = this.getClassification(region, classType);
      if (classification) {
        comparison[region] = {
          timeline: classification.timeline,
          procedure: classification.procedure,
          requirements: classification.requirements
        };
      }
    });
    
    return comparison;
  }

  /**
   * 获取相关标准
   * @param {string} category - 标准类别
   * @returns {Object} 标准信息
   */
  getStandards(category) {
    return this.standards[category] || {};
  }

  /**
   * 搜索法规信息
   * @param {string} keyword - 搜索关键词
   * @returns {Array} 搜索结果
   */
  searchRegulations(keyword) {
    const results = [];
    const searchTerm = keyword.toLowerCase();
    
    // 搜索法规
    Object.entries(this.regulations).forEach(([region, regulation]) => {
      if (regulation.name.toLowerCase().includes(searchTerm)) {
        results.push({
          type: 'regulation',
          region,
          title: regulation.name,
          authority: regulation.authority
        });
      }
      
      // 搜索分类
      Object.entries(regulation.classification || {}).forEach(([classType, classification]) => {
        if (classification.name.toLowerCase().includes(searchTerm) ||
            classification.procedure.toLowerCase().includes(searchTerm)) {
          results.push({
            type: 'classification',
            region,
            classType,
            title: `${regulation.name} - ${classification.name}`,
            procedure: classification.procedure
          });
        }
      });
    });
    
    // 搜索标准
    Object.entries(this.standards).forEach(([category, standards]) => {
      Object.entries(standards).forEach(([standardId, standard]) => {
        if (standardId.toLowerCase().includes(searchTerm) ||
            standard.title.toLowerCase().includes(searchTerm)) {
          results.push({
            type: 'standard',
            category,
            id: standardId,
            title: standard.title,
            scope: standard.scope
          });
        }
      });
    });
    
    return results;
  }

  /**
   * 获取法规更新信息
   * @param {string} region - 地区代码
   * @returns {Array} 更新信息
   */
  getRegulationUpdates(region) {
    // 这里可以集成实时法规更新API
    const updates = {
      china: [
        {
          date: '2024-01-01',
          title: '医疗器械分类目录更新',
          description: '新增部分IVD产品分类'
        }
      ],
      eu: [
        {
          date: '2024-01-01',
          title: 'IVDR实施指南更新',
          description: '更新临床证据要求'
        }
      ],
      usa: [
        {
          date: '2024-01-01',
          title: 'FDA指导原则更新',
          description: '510(k)申报要求调整'
        }
      ]
    };
    
    return updates[region] || [];
  }

  /**
   * 生成法规合规检查清单
   * @param {string} region - 地区代码
   * @param {string} classType - 产品类别
   * @returns {Array} 检查清单
   */
  generateComplianceChecklist(region, classType) {
    const requirements = this.getRequirements(region, classType);
    const checklist = [];
    
    requirements.forEach((requirement, index) => {
      checklist.push({
        id: index + 1,
        item: requirement,
        completed: false,
        notes: '',
        priority: this.getRequirementPriority(requirement)
      });
    });
    
    return checklist;
  }

  /**
   * 获取要求的优先级
   * @param {string} requirement - 要求描述
   * @returns {string} 优先级
   */
  getRequirementPriority(requirement) {
    const highPriority = ['临床试验', '风险管理', '质量管理体系'];
    const mediumPriority = ['技术要求', '研究资料', '检验报告'];
    
    if (highPriority.some(item => requirement.includes(item))) {
      return 'high';
    } else if (mediumPriority.some(item => requirement.includes(item))) {
      return 'medium';
    }
    return 'low';
  }
}

// 创建全局实例
const ivdRegulationManager = new IVDRegulationManager();

module.exports = {
  ivdRegulationManager,
  IVDRegulationManager
};
