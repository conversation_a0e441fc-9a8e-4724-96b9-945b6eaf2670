// cloudfunctions/apiConfig/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * API配置管理云函数
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @returns {Object} 操作结果
 */
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'getAPIConfig':
        return await getAPIConfig(data)
      case 'updateAPIConfig':
        return await updateAPIConfig(data)
      case 'testAPIConnection':
        return await testAPIConnection(data)
      case 'getModelStatus':
        return await getModelStatus()
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('API配置操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      error: error.message
    }
  }
}

/**
 * 获取API配置
 */
async function getAPIConfig(params) {
  const { provider, modelId } = params
  
  const configCollection = db.collection('api_configs')
  let query = configCollection
  
  if (provider) {
    query = query.where({ provider })
  }
  
  if (modelId) {
    query = query.where({ modelId })
  }
  
  const result = await query.get()
  
  return {
    success: true,
    data: result.data
  }
}

/**
 * 更新API配置
 */
async function updateAPIConfig(configData) {
  const { provider, modelId, apiKey, baseURL, enabled = true } = configData
  
  if (!provider || !modelId || !apiKey) {
    return {
      success: false,
      message: '缺少必要参数'
    }
  }
  
  const configCollection = db.collection('api_configs')
  
  // 检查配置是否已存在
  const existingConfig = await configCollection.where({
    provider,
    modelId
  }).get()
  
  const configRecord = {
    provider,
    modelId,
    apiKey: encryptAPIKey(apiKey), // 加密存储
    baseURL,
    enabled,
    updatedAt: new Date()
  }
  
  if (existingConfig.data.length > 0) {
    // 更新现有配置
    await configCollection.doc(existingConfig.data[0]._id).update({
      data: configRecord
    })
  } else {
    // 创建新配置
    configRecord.createdAt = new Date()
    await configCollection.add({
      data: configRecord
    })
  }
  
  return {
    success: true,
    message: 'API配置更新成功'
  }
}

/**
 * 测试API连接
 */
async function testAPIConnection(params) {
  const { provider, modelId, apiKey, baseURL } = params
  
  try {
    // 这里应该实际调用对应的API进行测试
    // 为了演示，我们模拟测试结果
    
    const testResults = {
      openai: await testOpenAI(apiKey, baseURL),
      anthropic: await testAnthropic(apiKey, baseURL),
      google: await testGoogle(apiKey, baseURL),
      deepseek: await testDeepSeek(apiKey, baseURL),
      alibaba: await testAlibaba(apiKey, baseURL),
      tencent: await testTencent(apiKey, baseURL)
    }
    
    const result = testResults[provider]
    
    if (result) {
      return {
        success: true,
        data: result
      }
    } else {
      return {
        success: false,
        message: '不支持的API提供商'
      }
    }
  } catch (error) {
    return {
      success: false,
      message: 'API连接测试失败',
      error: error.message
    }
  }
}

/**
 * 获取模型状态
 */
async function getModelStatus() {
  const configCollection = db.collection('api_configs')
  const configs = await configCollection.where({ enabled: true }).get()
  
  const modelStatus = {}
  
  configs.data.forEach(config => {
    modelStatus[config.modelId] = {
      provider: config.provider,
      enabled: config.enabled,
      lastUpdated: config.updatedAt,
      status: 'available' // 实际应用中应该检查API状态
    }
  })
  
  return {
    success: true,
    data: modelStatus
  }
}

/**
 * 测试OpenAI API
 */
async function testOpenAI(apiKey, baseURL) {
  // 模拟测试
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        latency: 150 + Math.random() * 100,
        message: 'OpenAI API连接正常'
      })
    }, 1000)
  })
}

/**
 * 测试Anthropic API
 */
async function testAnthropic(apiKey, baseURL) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        latency: 200 + Math.random() * 150,
        message: 'Anthropic API连接正常'
      })
    }, 1200)
  })
}

/**
 * 测试Google API
 */
async function testGoogle(apiKey, baseURL) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        latency: 180 + Math.random() * 120,
        message: 'Google API连接正常'
      })
    }, 1100)
  })
}

/**
 * 测试DeepSeek API
 */
async function testDeepSeek(apiKey, baseURL) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        latency: 120 + Math.random() * 80,
        message: 'DeepSeek API连接正常'
      })
    }, 800)
  })
}

/**
 * 测试阿里云API
 */
async function testAlibaba(apiKey, baseURL) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        latency: 100 + Math.random() * 60,
        message: '通义千问API连接正常'
      })
    }, 700)
  })
}

/**
 * 测试腾讯云API
 */
async function testTencent(apiKey, baseURL) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 'success',
        latency: 110 + Math.random() * 70,
        message: '腾讯混元API连接正常'
      })
    }, 750)
  })
}

/**
 * 加密API密钥
 */
function encryptAPIKey(apiKey) {
  // 简单的Base64编码，实际应用中应使用更强的加密
  return Buffer.from(apiKey).toString('base64')
}

/**
 * 解密API密钥
 */
function decryptAPIKey(encryptedKey) {
  return Buffer.from(encryptedKey, 'base64').toString()
}
