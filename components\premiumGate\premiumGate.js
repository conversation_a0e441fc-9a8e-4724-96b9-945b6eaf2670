// components/premiumGate/premiumGate.js
// 付费功能门控组件

const { checkAccess } = require('../../utils/accessControl')

Component({
  properties: {
    // 功能名称
    feature: {
      type: String,
      value: ''
    },
    // 模型ID（用于模型访问控制）
    modelId: {
      type: String,
      value: ''
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 描述
    description: {
      type: String,
      value: ''
    },
    // 是否可关闭
    dismissible: {
      type: Boolean,
      value: true
    },
    // 是否显示升级按钮
    showUpgrade: {
      type: Boolean,
      value: true
    },
    // 是否显示查看计划按钮
    showViewPlans: {
      type: Boolean,
      value: true
    },
    // 是否显示使用量
    showUsage: {
      type: Boolean,
      value: false
    },
    // 使用量类型
    usageType: {
      type: String,
      value: 'daily' // daily | monthly
    }
  },

  data: {
    hasAccess: false,
    requiredPlan: '',
    limitType: '',
    resetTime: '',
    resetTimeText: '',
    usagePercentage: 0,
    usedCount: 0,
    totalLimit: 0,
    planNames: {
      'free': '免费版',
      'basic': '基础版',
      'standard': '标准版',
      'premium': '专业版'
    }
  },

  lifetimes: {
    attached() {
      this.checkAccess()
    }
  },

  observers: {
    'feature, modelId': function(feature, modelId) {
      if (feature || modelId) {
        this.checkAccess()
      }
    }
  },

  methods: {
    /**
     * 检查访问权限
     */
    async checkAccess() {
      try {
        let accessResult

        if (this.data.modelId) {
          // 检查模型访问权限
          accessResult = await checkAccess.model(this.data.modelId)
        } else if (this.data.feature) {
          // 检查功能访问权限
          accessResult = await checkAccess.feature(this.data.feature)
        } else {
          // 默认允许访问
          accessResult = { allowed: true }
        }

        this.setData({
          hasAccess: accessResult.allowed,
          requiredPlan: accessResult.requiredPlan || '',
          limitType: this.getLimitType(accessResult.reason),
          resetTime: accessResult.resetTime || '',
          resetTimeText: this.formatResetTime(accessResult.resetTime)
        })

        // 更新使用量信息
        if (accessResult.allowed && this.data.showUsage) {
          this.updateUsageInfo(accessResult.remaining)
        }

        // 触发访问检查完成事件
        this.triggerEvent('accesscheck', {
          hasAccess: accessResult.allowed,
          result: accessResult
        })

      } catch (error) {
        console.error('检查访问权限失败:', error)
        this.setData({
          hasAccess: false
        })
      }
    },

    /**
     * 更新使用量信息
     */
    updateUsageInfo(remaining) {
      if (!remaining) return

      const status = checkAccess.getStatus()
      const limits = status.limits || {}
      const usage = status.usage || {}

      let usedCount, totalLimit
      if (this.data.usageType === 'daily') {
        usedCount = usage.daily || 0
        totalLimit = limits.daily || 0
      } else {
        usedCount = usage.monthly || 0
        totalLimit = limits.monthly || 0
      }

      const percentage = totalLimit > 0 ? (usedCount / totalLimit) * 100 : 0

      this.setData({
        usagePercentage: Math.min(percentage, 100),
        usedCount,
        totalLimit
      })
    },

    /**
     * 获取限制类型
     */
    getLimitType(reason) {
      if (!reason) return ''
      
      if (reason.includes('今日')) return 'daily'
      if (reason.includes('本月')) return 'monthly'
      return ''
    },

    /**
     * 格式化重置时间
     */
    formatResetTime(resetTime) {
      if (!resetTime) return ''

      try {
        const date = new Date(resetTime)
        const now = new Date()
        const diff = date.getTime() - now.getTime()

        if (diff <= 0) return '即将重置'

        const hours = Math.floor(diff / (1000 * 60 * 60))
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

        if (hours > 24) {
          const days = Math.floor(hours / 24)
          return `${days}天后`
        } else if (hours > 0) {
          return `${hours}小时${minutes}分钟后`
        } else {
          return `${minutes}分钟后`
        }
      } catch (error) {
        return ''
      }
    },

    /**
     * 升级订阅
     */
    onUpgrade() {
      this.triggerEvent('upgrade', {
        requiredPlan: this.data.requiredPlan,
        feature: this.data.feature,
        modelId: this.data.modelId
      })

      // 默认跳转到订阅页面
      wx.navigateTo({
        url: '/pages/subscription/subscription'
      })
    },

    /**
     * 查看订阅计划
     */
    onViewPlans() {
      this.triggerEvent('viewplans', {
        feature: this.data.feature,
        modelId: this.data.modelId
      })

      // 默认跳转到订阅页面
      wx.navigateTo({
        url: '/pages/subscription/subscription'
      })
    },

    /**
     * 关闭提示
     */
    onDismiss() {
      this.triggerEvent('dismiss', {
        feature: this.data.feature,
        modelId: this.data.modelId
      })

      // 可以选择隐藏组件或其他操作
      // 这里暂时不做处理，由父组件决定
    },

    /**
     * 刷新访问权限
     */
    refresh() {
      this.checkAccess()
    },

    /**
     * 手动设置访问权限
     */
    setAccess(hasAccess) {
      this.setData({
        hasAccess
      })
    }
  }
})
