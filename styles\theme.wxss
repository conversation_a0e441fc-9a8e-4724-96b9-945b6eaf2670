/* styles/theme.wxss */
/* 全局主题样式 */

/* 主题色彩变量 */
page {
  /* 主色调 - 医疗蓝 */
  --primary-color: #2E7CE8;
  --primary-light: #5A9EF4;
  --primary-dark: #1F5FBF;
  --primary-gradient: linear-gradient(135deg, #2E7CE8 0%, #5A9EF4 100%);
  
  /* 辅助色 */
  --secondary-color: #00C896;
  --secondary-light: #33D4B0;
  --secondary-dark: #00A67D;
  
  /* 功能色 */
  --success-color: #52C41A;
  --warning-color: #FAAD14;
  --error-color: #FF4D4F;
  --info-color: #1890FF;
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8C8C8C;
  --text-disabled: #BFBFBF;
  --text-inverse: #FFFFFF;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-tertiary: #F5F5F5;
  --bg-quaternary: #F0F0F0;
  --bg-mask: rgba(0, 0, 0, 0.45);
  
  /* 边框色 */
  --border-light: #F0F0F0;
  --border-base: #D9D9D9;
  --border-dark: #BFBFBF;
  
  /* 阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-dark: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  
  /* 圆角 */
  --radius-small: 8rpx;
  --radius-base: 12rpx;
  --radius-large: 16rpx;
  --radius-round: 50rpx;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;
  --font-size-title: 48rpx;
  
  /* 行高 */
  --line-height-base: 1.5;
  --line-height-tight: 1.3;
  --line-height-loose: 1.7;
  
  /* 动画 */
  --transition-fast: 0.2s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 通用组件样式 */

/* 卡片样式 */
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.card-hover {
  transition: all var(--transition-base);
}

.card-hover:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-round);
  font-size: var(--font-size-base);
  font-weight: 500;
  transition: all var(--transition-base);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.btn-primary:active {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:active {
  background: var(--bg-quaternary);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:active {
  background: var(--primary-color);
  color: var(--text-inverse);
}

.btn-ghost {
  background: rgba(46, 124, 232, 0.1);
  color: var(--primary-color);
}

.btn-ghost:active {
  background: rgba(46, 124, 232, 0.2);
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 输入框样式 */
.input {
  background: var(--bg-primary);
  border: 2rpx solid var(--border-base);
  border-radius: var(--radius-base);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  transition: all var(--transition-base);
}

.input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(46, 124, 232, 0.1);
}

.input-error {
  border-color: var(--error-color);
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.tag-primary {
  background: rgba(46, 124, 232, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.tag-error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

/* 分割线 */
.divider {
  height: 2rpx;
  background: var(--border-light);
  margin: var(--spacing-md) 0;
}

.divider-vertical {
  width: 2rpx;
  height: 100%;
  background: var(--border-light);
  margin: 0 var(--spacing-md);
}

/* 加载动画 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 渐变背景 */
.gradient-bg {
  background: var(--primary-gradient);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-inverse {
  color: var(--text-inverse);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 响应式工具类 */
@media (max-width: 375px) {
  .hide-on-small {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .hide-on-large {
    display: none !important;
  }
}
