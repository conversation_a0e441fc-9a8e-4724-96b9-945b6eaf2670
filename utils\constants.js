// utils/constants.js
// 应用常量定义

// 应用信息
export const APP_INFO = {
  name: 'IVD智能顾问',
  version: '1.0.0',
  appId: 'wx40de5ae4b1c122b6',
  description: '您的专属研发、注册、销售顾问'
};

// API配置
export const API_CONFIG = {
  timeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
  
  // OpenAI配置
  openai: {
    baseURL: 'https://api.openai.com/v1',
    models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
    maxTokens: 2000
  },
  
  // Anthropic配置
  anthropic: {
    baseURL: 'https://api.anthropic.com/v1',
    models: ['claude-3-sonnet', 'claude-3-opus'],
    maxTokens: 2000
  },
  
  // Google配置
  google: {
    baseURL: 'https://generativelanguage.googleapis.com/v1beta',
    models: ['gemini-pro', 'gemini-pro-vision'],
    maxTokens: 2000
  }
};

// 存储键名
export const STORAGE_KEYS = {
  userSettings: 'userSettings',
  chatHistory: 'chatHistory',
  appSettings: 'appSettings',
  apiKeys: 'apiKeys'
};

// 页面路径
export const PAGES = {
  index: '/pages/index/index',
  chat: '/pages/chat/chat',
  models: '/pages/models/models',
  settings: '/pages/settings/settings'
};

// 消息类型
export const MESSAGE_TYPES = {
  user: 'user',
  assistant: 'assistant',
  system: 'system'
};

// AI模型配置
export const AI_MODELS = {
  'gpt-3.5-turbo': {
    name: 'GPT-3.5 Turbo',
    provider: 'openai',
    description: '快速响应，适合日常咨询',
    icon: '⚡',
    features: ['快速响应', '通用对话', '成本较低'],
    metrics: { speed: 95, accuracy: 85, expertise: 80 }
  },
  'gpt-4': {
    name: 'GPT-4',
    provider: 'openai',
    description: '最先进的AI模型，具有强大的推理能力',
    icon: '🧠',
    features: ['深度推理', '复杂分析', '高准确性'],
    metrics: { speed: 75, accuracy: 95, expertise: 90 }
  },
  'claude-3': {
    name: 'Claude-3',
    provider: 'anthropic',
    description: '擅长长文本处理和深度分析',
    icon: '📚',
    features: ['长文本处理', '深度分析', '安全可靠'],
    metrics: { speed: 80, accuracy: 90, expertise: 85 }
  },
  'gemini-pro': {
    name: 'Gemini Pro',
    provider: 'google',
    description: 'Google的多模态AI助手',
    icon: '🌟',
    features: ['多模态', '图像理解', '创新能力'],
    metrics: { speed: 85, accuracy: 88, expertise: 82 }
  }
};

// IVD领域分类
export const IVD_CATEGORIES = {
  rd: {
    title: '产品研发流程',
    icon: '🔬',
    color: '#1976D2',
    description: '从概念到上市的完整研发流程'
  },
  registration: {
    title: '注册申报要点',
    icon: '📋',
    color: '#4CAF50',
    description: '法规要求和申报流程指导'
  },
  sales: {
    title: '市场销售策略',
    icon: '📈',
    color: '#FF9800',
    description: '市场推广和销售策略制定'
  },
  quality: {
    title: '质量管理体系',
    icon: '✅',
    color: '#9C27B0',
    description: 'ISO13485质量管理体系建立'
  }
};

// 错误代码
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

// 事件类型
export const EVENT_TYPES = {
  MESSAGE_SENT: 'MESSAGE_SENT',
  MESSAGE_RECEIVED: 'MESSAGE_RECEIVED',
  MODEL_CHANGED: 'MODEL_CHANGED',
  ERROR_OCCURRED: 'ERROR_OCCURRED'
};

// 默认设置
export const DEFAULT_SETTINGS = {
  chat: {
    autoSave: true,
    showTimestamp: true,
    enableVoice: false,
    maxHistoryLength: 1000
  },
  notification: {
    enabled: true,
    sound: true,
    vibrate: false
  },
  professional: {
    field: '医疗器械研发',
    experience: '中级（3-5年）',
    focusAreas: ['产品研发', '注册申报']
  }
};

// 正则表达式
export const REGEX_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^1[3-9]\d{9}$/,
  apiKey: /^[a-zA-Z0-9\-_]{20,}$/
};

// 动画配置
export const ANIMATION_CONFIG = {
  duration: 300,
  timingFunction: 'ease-in-out'
};

// 主题颜色
export const THEME_COLORS = {
  primary: '#1976D2',
  secondary: '#1565C0',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  background: '#f5f5f5',
  surface: '#ffffff',
  text: '#333333',
  textSecondary: '#666666'
};
