/* pages/history/history.wxss */

.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 头部区域 */
.header-section {
  margin-bottom: var(--spacing-md);
}

.history-header {
  padding: var(--spacing-xl);
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

/* 搜索和筛选区域 */
.filter-section {
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.search-bar {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.search-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2rpx solid var(--border-base);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
}

.search-btn {
  min-width: 120rpx;
}

.filter-tabs {
  display: flex;
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  padding: 8rpx;
  box-shadow: var(--shadow-light);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-sm) var(--spacing-xs);
  border-radius: var(--radius-small);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: all var(--transition-base);
}

.tab-item.active {
  background: var(--primary-color);
  color: var(--text-inverse);
}

/* 历史记录列表 */
.history-list {
  padding: 0 var(--spacing-md);
}

.history-item {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-lg);
  transition: all var(--transition-base);
}

.history-item:hover {
  box-shadow: var(--shadow-base);
  transform: translateY(-2rpx);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.chat-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.title-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.category-tag {
  display: inline-block;
  padding: 4rpx var(--spacing-xs);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.tag-rd {
  background: rgba(46, 124, 232, 0.1);
  color: var(--primary-color);
}

.tag-registration {
  background: rgba(0, 168, 112, 0.1);
  color: var(--secondary-color);
}

.tag-sales {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.tag-quality {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-general {
  background: rgba(140, 140, 140, 0.1);
  color: var(--text-tertiary);
}

.chat-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  min-width: 80rpx;
  padding: var(--spacing-xs) var(--spacing-sm);
}

.item-content {
  margin-bottom: var(--spacing-sm);
}

.chat-preview {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-loose);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-footer {
  border-top: 1rpx solid var(--border-light);
  padding-top: var(--spacing-sm);
}

.chat-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.model-name {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: var(--spacing-xl);
}

.empty-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.empty-icon {
  font-size: 120rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.empty-desc {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.start-chat-btn {
  min-width: 200rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 加载更多 */
.load-more {
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: center;
}

.load-more-btn {
  min-width: 200rpx;
}

/* 删除确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  overflow: hidden;
}

.modal-header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  border-bottom: 1rpx solid var(--border-light);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

.modal-footer .btn {
  min-width: 120rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .filter-tabs {
    padding: 4rpx;
  }
  
  .tab-item {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }
  
  .history-item {
    padding: var(--spacing-md);
  }
  
  .chat-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .action-btn {
    min-width: 60rpx;
    font-size: var(--font-size-xs);
  }
}
