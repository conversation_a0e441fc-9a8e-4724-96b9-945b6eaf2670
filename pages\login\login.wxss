/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 1;
  padding: 80rpx 40rpx 40rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 功能介绍 */
.features-section {
  margin-bottom: 80rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.feature-icon {
  font-size: 50rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
}

.feature-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 登录区域 */
.login-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 40rpx;
}

.login-tips {
  text-align: center;
  margin-bottom: 40rpx;
}

.login-tips text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  padding: 0;
  margin-bottom: 30rpx;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
}

.login-btn::after {
  border: none;
}

.wechat-login {
  background: #fff;
}



.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 30rpx 20rpx 30rpx;
  position: relative;
  z-index: 2;
}

.btn-subtitle {
  text-align: center;
  padding: 0 30rpx 20rpx 30rpx;
  font-size: 24rpx;
  color: rgba(51, 51, 51, 0.6);
}

.wechat-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
}

.wechat-login .btn-text {
  color: #333;
}



.btn-hover {
  transform: scale(0.98);
  opacity: 0.8;
}

.login-btn:active {
  transform: scale(0.98);
}

/* 确保按钮可点击 */
.login-btn {
  pointer-events: auto;
  user-select: none;
  -webkit-user-select: none;
}

/* 已登录状态 */
.logged-in-section {
  text-align: center;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  text-align: left;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
}

.user-status {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.action-btn {
  width: 100%;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: #fff;
  color: #333;
}

.action-btn.secondary {
  background: transparent;
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.action-btn:active {
  transform: scale(0.98);
}

/* 游客模式 */
.guest-section {
  text-align: center;
  margin-top: 40rpx;
}

.guest-tips {
  margin-bottom: 30rpx;
}

.guest-tips text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.guest-btn {
  width: 100%;
  padding: 25rpx;
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  font-size: 28rpx;
}

.guest-btn::after {
  border: none;
}

.guest-btn:active {
  background: rgba(255, 255, 255, 0.1);
}



/* 服务条款 */
.terms-section {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
}

.terms-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.5;
}

.terms-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #fff;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #fff;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  width: 85%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  border-radius: 50%;
  background: #f0f0f0;
}

.modal-close:active {
  background: #e0e0e0;
}

.modal-body {
  max-height: 500rpx;
  padding: 30rpx;
}

.agreement-text,
.privacy-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-line;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx;
  text-align: center;
}

.modal-btn {
  width: 200rpx;
  padding: 20rpx;
  background: #1976D2;
  color: #fff;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn::after {
  border: none;
}

.modal-btn:active {
  background: #1565C0;
}
