// pages/index/index.js
const app = getApp();

Page({
  data: {
    userInfo: {},
    subscriptionTier: 'free',
    subscriptionName: '免费版',
    currentModel: {
      name: 'GPT-3.5 Turbo',
      description: '快速响应，适合日常咨询',
      provider: 'OpenAI',
      tier: '基础',
      icon: '⚡',
      color: '#10A37F',
      features: ['快速响应', '通用对话', '成本较低'],
      metrics: { speed: 95, accuracy: 85, expertise: 80 }
    },
    recentChats: []
  },

  onLoad(options) {
    console.log('首页加载');
    this.loadUserInfo();
    this.loadCurrentModel();
    this.loadRecentChats();
    this.loadSubscriptionInfo();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadUserInfo();
    this.loadRecentChats();
    this.loadCurrentModel();
    this.loadSubscriptionInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo;
    if (userInfo) {
      this.setData({
        userInfo
      });
    }
  },

  // 加载订阅信息
  async loadSubscriptionInfo() {
    if (!app.globalData.userInfo || app.globalData.userInfo.isGuest) {
      this.setData({
        subscriptionTier: 'guest',
        subscriptionName: '游客模式'
      });
      return;
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          action: 'getUserSubscription'
        }
      });

      if (result.result.success) {
        const subscription = result.result.data;
        const tierNames = {
          free: '免费版',
          basic: '基础版',
          standard: '标准版',
          premium: '专业版'
        };

        this.setData({
          subscriptionTier: subscription.tier,
          subscriptionName: tierNames[subscription.tier] || '免费版'
        });
      }
    } catch (error) {
      console.error('加载订阅信息失败:', error);
    }
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 加载当前AI模型信息
  loadCurrentModel() {
    const { AI_MODELS } = require('../../utils/constants');
    const selectedModel = app.globalData.selectedAIModel || 'gpt-3.5-turbo';
    const modelConfig = AI_MODELS[selectedModel];

    if (modelConfig) {
      this.setData({
        currentModel: {
          ...modelConfig,
          id: selectedModel
        }
      });
    } else {
      // 默认模型
      this.setData({
        currentModel: {
          ...AI_MODELS['gpt-3.5-turbo'],
          id: 'gpt-3.5-turbo'
        }
      });
    }
  },

  // 加载最近对话
  loadRecentChats() {
    const chatHistory = app.globalData.chatHistory;
    
    // 按时间排序并取最近5条
    const recentChats = chatHistory
      .slice(-10) // 取最近10条
      .reverse() // 最新的在前
      .slice(0, 5) // 只显示5条
      .map(chat => ({
        id: chat.id,
        title: this.generateChatTitle(chat.content),
        lastMessage: chat.content.substring(0, 50) + (chat.content.length > 50 ? '...' : ''),
        timeAgo: this.formatTimeAgo(chat.timestamp),
        model: chat.model || 'GPT-3.5'
      }));

    this.setData({
      recentChats
    });
  },

  // 生成对话标题
  generateChatTitle(content) {
    if (content.includes('研发') || content.includes('开发')) {
      return '产品研发咨询';
    } else if (content.includes('注册') || content.includes('申报')) {
      return '注册申报咨询';
    } else if (content.includes('销售') || content.includes('市场')) {
      return '销售策略咨询';
    } else {
      return '智能咨询';
    }
  },

  // 格式化时间
  formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return new Date(timestamp).toLocaleDateString();
  },

  // 开始特定类别的聊天
  startChat(e) {
    const category = e.currentTarget.dataset.category;
    const knowledge = app.getIVDKnowledge(category);
    
    if (knowledge) {
      // 跳转到聊天页面并传递类别信息
      wx.navigateTo({
        url: `/pages/chat/chat?category=${category}&title=${knowledge.title}`
      });
    } else {
      this.startNewChat();
    }
  },

  // 开始新的聊天
  startNewChat() {
    wx.switchTab({
      url: '/pages/chat/chat'
    });
  },

  // 切换AI模型
  changeModel() {
    wx.navigateTo({
      url: '/pages/models/models'
    });
  },

  // 打开特定对话
  openChat(e) {
    const chatId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/chat/chat?chatId=${chatId}`
    });
  },

  // 查看所有对话
  viewAllChats() {
    wx.navigateTo({
      url: '/pages/chat/chat?showHistory=true'
    });
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      const userInfo = await app.getUserInfo();
      this.setData({
        userInfo
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 您的专属研发、注册、销售顾问',
      path: '/pages/index/index',
      imageUrl: '/images/share-image.png'
    };
  },

  onShareTimeline() {
    return {
      title: 'IVD智能顾问 - 专业的医疗器械咨询助手',
      imageUrl: '/images/share-image.png'
    };
  }
});
