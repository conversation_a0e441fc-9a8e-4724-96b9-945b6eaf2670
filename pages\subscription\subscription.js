// pages/subscription/subscription.js
const app = getApp()

Page({
  data: {
    currentSubscription: {
      tier: 'free',
      name: '免费版',
      icon: '🆓',
      dailyLimit: 10,
      monthlyLimit: 100,
      expiresAt: null
    },
    usageStats: {
      daily: 0,
      monthly: 0,
      total: 0
    },
    dailyProgress: 0,
    monthlyProgress: 0,
    availableModels: [],
    showUpgradeModal: false,
    selectedPlan: null,
    selectedDuration: 1,
    totalPrice: 0,
    plans: {
      free: {
        name: '免费版',
        price: 0,
        dailyLimit: 10,
        monthlyLimit: 100,
        features: ['每日10次对话', '基础AI模型', '基础功能']
      },
      basic: {
        name: '基础版',
        price: 19.9,
        dailyLimit: 100,
        monthlyLimit: 2000,
        features: ['每日100次对话', '更多AI模型', '云端同步', '优先支持']
      },
      standard: {
        name: '标准版',
        price: 39.9,
        dailyLimit: 300,
        monthlyLimit: 6000,
        features: ['每日300次对话', '高级AI模型', '专业分析', '数据导出']
      },
      premium: {
        name: '专业版',
        price: 99.9,
        dailyLimit: 1000,
        monthlyLimit: 20000,
        features: ['每日1000次对话', '所有AI模型', '企业级功能', '专属客服']
      }
    }
  },

  onLoad(options) {
    console.log('订阅页面加载')
    this.loadSubscriptionInfo()
    this.loadUsageStats()
    this.loadAvailableModels()
  },

  onShow() {
    this.loadSubscriptionInfo()
    this.loadUsageStats()
  },

  // 加载订阅信息
  async loadSubscriptionInfo() {
    if (!app.globalData.userInfo || app.globalData.userInfo.isGuest) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          action: 'getUserSubscription'
        }
      })

      if (result.result.success) {
        const subscription = result.result.data
        const tierIcons = {
          free: '🆓',
          basic: '📦',
          standard: '⭐',
          premium: '👑'
        }

        this.setData({
          currentSubscription: {
            ...subscription,
            name: this.data.plans[subscription.tier].name,
            icon: tierIcons[subscription.tier],
            dailyLimit: subscription.limits.dailyLimit,
            monthlyLimit: subscription.limits.monthlyLimit,
            expiresAt: subscription.expiresAt ? this.formatDate(subscription.expiresAt) : null
          }
        })
      }
    } catch (error) {
      console.error('加载订阅信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 加载使用统计
  async loadUsageStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          action: 'getUsageStats'
        }
      })

      if (result.result.success) {
        const stats = result.result.data
        const { dailyLimit, monthlyLimit } = this.data.currentSubscription

        const dailyProgress = Math.min((stats.daily / dailyLimit) * 100, 100)
        const monthlyProgress = Math.min((stats.monthly / monthlyLimit) * 100, 100)

        this.setData({
          usageStats: stats,
          dailyProgress: Math.round(dailyProgress),
          monthlyProgress: Math.round(monthlyProgress)
        })
      }
    } catch (error) {
      console.error('加载使用统计失败:', error)
    }
  },

  // 加载可用模型
  loadAvailableModels() {
    const { AI_MODELS } = require('../../utils/constants')
    const { currentSubscription } = this.data
    
    const tierModels = {
      free: ['gpt-3.5-turbo', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo'],
      basic: [
        'gpt-3.5-turbo', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo',
        'claude-3-sonnet', 'gemini-pro', 'deepseek-coder', 'qwen-plus', 'hunyuan-pro'
      ],
      standard: [
        'gpt-3.5-turbo', 'gpt-4', 'deepseek-chat', 'qwen-turbo', 'hunyuan-turbo',
        'claude-3-sonnet', 'gemini-pro', 'deepseek-coder', 'qwen-plus', 'hunyuan-pro'
      ],
      premium: Object.keys(AI_MODELS)
    }

    const availableModelIds = tierModels[currentSubscription.tier] || tierModels.free
    const availableModels = availableModelIds.map(modelId => {
      const model = AI_MODELS[modelId]
      return {
        id: modelId,
        ...model,
        tierName: this.getTierName(model.tier)
      }
    })

    this.setData({
      availableModels
    })
  },

  // 获取等级名称
  getTierName(tier) {
    const tierNames = {
      basic: '基础',
      standard: '标准',
      premium: '专业'
    }
    return tierNames[tier] || '基础'
  },

  // 选择计划
  selectPlan(e) {
    const planType = e.currentTarget.dataset.plan
    
    if (planType === 'free') {
      wx.showToast({
        title: '当前已是免费版',
        icon: 'none'
      })
      return
    }

    if (planType === this.data.currentSubscription.tier) {
      wx.showToast({
        title: '当前已是此计划',
        icon: 'none'
      })
      return
    }

    this.setData({
      selectedPlan: this.data.plans[planType],
      showUpgradeModal: true,
      selectedDuration: 1
    })
    
    this.calculateTotalPrice()
  },

  // 显示升级弹窗
  showUpgradeModal() {
    this.setData({
      selectedPlan: this.data.plans.standard,
      showUpgradeModal: true,
      selectedDuration: 1
    })
    this.calculateTotalPrice()
  },

  // 隐藏升级弹窗
  hideUpgradeModal() {
    this.setData({
      showUpgradeModal: false,
      selectedPlan: null
    })
  },

  // 选择订阅时长
  selectDuration(e) {
    const duration = parseInt(e.currentTarget.dataset.duration)
    this.setData({
      selectedDuration: duration
    })
    this.calculateTotalPrice()
  },

  // 计算总价
  calculateTotalPrice() {
    const { selectedPlan, selectedDuration } = this.data
    if (!selectedPlan) return

    let totalPrice = selectedPlan.price * selectedDuration
    
    // 应用折扣
    if (selectedDuration === 3) {
      totalPrice *= 0.9 // 9折
    } else if (selectedDuration === 12) {
      totalPrice *= 0.8 // 8折
    }

    this.setData({
      totalPrice: totalPrice.toFixed(1)
    })
  },

  // 确认升级
  async confirmUpgrade() {
    const { selectedPlan, selectedDuration, totalPrice } = this.data
    
    if (!selectedPlan) {
      wx.showToast({
        title: '请选择计划',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '创建订单中...'
    })

    try {
      // 创建订单
      const orderResult = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          action: 'createOrder',
          data: {
            planType: this.getPlanType(selectedPlan.name),
            duration: selectedDuration
          }
        }
      })

      wx.hideLoading()

      if (orderResult.result.success) {
        const orderData = orderResult.result.data
        
        // 调用微信支付
        wx.requestPayment({
          ...orderData.paymentParams,
          success: async (res) => {
            // 支付成功，验证支付结果
            await this.verifyPayment(orderData.orderId)
          },
          fail: (err) => {
            console.error('支付失败:', err)
            wx.showToast({
              title: '支付取消',
              icon: 'none'
            })
          }
        })
      } else {
        wx.showToast({
          title: orderResult.result.message || '创建订单失败',
          icon: 'error'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('创建订单失败:', error)
      wx.showToast({
        title: '创建订单失败',
        icon: 'error'
      })
    }
  },

  // 验证支付结果
  async verifyPayment(orderId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          action: 'verifyPayment',
          data: { orderId }
        }
      })

      if (result.result.success) {
        wx.showToast({
          title: '订阅成功',
          icon: 'success'
        })
        
        this.hideUpgradeModal()
        
        // 刷新页面数据
        setTimeout(() => {
          this.loadSubscriptionInfo()
          this.loadAvailableModels()
        }, 1500)
      } else {
        wx.showToast({
          title: '支付验证失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('支付验证失败:', error)
      wx.showToast({
        title: '支付验证失败',
        icon: 'error'
      })
    }
  },

  // 获取计划类型
  getPlanType(planName) {
    const planMap = {
      '基础版': 'basic',
      '标准版': 'standard',
      '专业版': 'premium'
    }
    return planMap[planName] || 'basic'
  },

  // 显示取消订阅弹窗
  showCancelModal() {
    wx.showModal({
      title: '取消订阅',
      content: '确定要取消当前订阅吗？取消后将在到期时自动降级为免费版。',
      success: (res) => {
        if (res.confirm) {
          this.cancelSubscription()
        }
      }
    })
  },

  // 取消订阅
  async cancelSubscription() {
    wx.showLoading({
      title: '处理中...'
    })

    try {
      // 这里应该调用取消订阅的云函数
      // 暂时模拟成功
      setTimeout(() => {
        wx.hideLoading()
        wx.showToast({
          title: '取消成功',
          icon: 'success'
        })
      }, 1000)
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '取消失败',
        icon: 'error'
      })
    }
  },

  // 格式化日期
  formatDate(dateString) {
    const date = new Date(dateString)
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业的医疗器械咨询助手',
      path: '/pages/index/index'
    }
  }
})
