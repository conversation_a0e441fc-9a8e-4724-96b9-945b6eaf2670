<!--pages/chat/chat.wxml-->
<view class="page-container">
  <!-- 聊天头部 -->
  <view class="chat-header gradient-bg">
    <view class="header-content flex flex-between">
      <view class="model-info">
        <text class="text-body text-inverse">{{currentModel.name}}</text>
        <view class="status-indicator status-{{currentModel.status}}">{{currentModel.statusText}}</view>
      </view>
      <view class="header-actions flex">
        <button class="btn btn-text" bindtap="switchModel">
          <text class="text-inverse">切换模型</text>
        </button>
        <button class="btn btn-text" bindtap="clearChat">
          <text class="text-inverse">清空对话</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 快速咨询模板 -->
  <view class="quick-templates" wx:if="{{showTemplates && messages.length === 0}}">
    <view class="templates-title mb-md">
      <text class="title-tertiary">快速咨询模板</text>
    </view>
    <scroll-view class="templates-scroll" scroll-x="true">
      <view class="template-item card" wx:for="{{consultationTemplates}}" wx:key="id" bindtap="useTemplate" data-template="{{item}}">
        <view class="template-icon">{{item.icon}}</view>
        <text class="text-body">{{item.title}}</text>
        <text class="text-caption text-secondary">{{item.description}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 聊天消息区域 -->
  <scroll-view class="messages-container" scroll-y="true" scroll-top="{{scrollTop}}" scroll-into-view="{{scrollIntoView}}">
    <!-- 欢迎消息 -->
    <view class="welcome-message card gradient-bg-secondary" wx:if="{{messages.length === 0}}">
      <view class="welcome-content text-center">
        <view class="welcome-icon">🤖</view>
        <text class="title-secondary text-inverse">您好！我是您的IVD专业顾问</text>
        <text class="text-caption text-inverse">我可以为您提供产品研发、注册申报、市场销售等方面的专业咨询服务</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-item {{item.role}} slide-up" wx:for="{{messages}}" wx:key="id" id="msg-{{item.id}}">
      <view class="message-avatar">
        <image wx:if="{{item.role === 'user'}}" src="/images/user-avatar.png" mode="aspectFit"></image>
        <view wx:else class="ai-avatar">🤖</view>
      </view>
      <view class="message-content card">
        <view class="message-header flex flex-between">
          <text class="text-body">{{item.role === 'user' ? '您' : currentModel.name}}</text>
          <text class="text-small text-tertiary">{{item.timeStr}}</text>
        </view>
        <view class="message-text mt-sm">
          <text wx:if="{{item.role === 'assistant' && item.typing}}" class="typing-text">{{item.content}}</text>
          <text wx:else class="text-body">{{item.content}}</text>
        </view>
        <!-- 消息操作 -->
        <view class="message-actions flex mt-sm" wx:if="{{item.role === 'assistant' && !item.typing}}">
          <button class="btn btn-text btn-small" bindtap="copyMessage" data-content="{{item.content}}">
            <text>复制</text>
          </button>
          <button class="btn btn-text btn-small" bindtap="regenerateResponse" data-id="{{item.id}}">
            <text>重新生成</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 加载指示器 -->
    <view class="loading-message" wx:if="{{isLoading}}">
      <view class="message-avatar">
        <view class="ai-avatar">🤖</view>
      </view>
      <view class="message-content">
        <view class="loading-dots">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
        <text class="loading-text">AI正在思考中...</text>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-container">
    <!-- 建议问题 -->
    <view class="suggestions" wx:if="{{suggestions.length > 0 && !inputValue}}">
      <scroll-view class="suggestions-scroll" scroll-x="true">
        <view class="suggestion-item" wx:for="{{suggestions}}" wx:key="index" bindtap="useSuggestion" data-text="{{item}}">
          <text>{{item}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 输入框区域 -->
    <view class="input-area">
      <view class="input-wrapper card">
        <textarea
          class="message-input input-field"
          placeholder="请输入您的问题..."
          value="{{inputValue}}"
          bindinput="onInputChange"
          bindconfirm="sendMessage"
          auto-height
          maxlength="2000"
          show-confirm-bar="{{false}}"
          cursor-spacing="20"
        ></textarea>
        <view class="input-actions flex flex-between mt-sm">
          <view class="text-small text-tertiary">{{inputValue.length}}/2000</view>
          <button class="btn btn-ghost btn-small" bindtap="startVoiceInput" wx:if="{{!inputValue}}">
            <text>🎤</text>
          </button>
        </view>
      </view>
      <button
        class="btn {{inputValue.trim() ? 'btn-primary' : 'btn-ghost'}} btn-large"
        bindtap="sendMessage"
        disabled="{{isLoading || !inputValue.trim()}}"
      >
        <text wx:if="{{isLoading}}">发送中</text>
        <text wx:else>发送</text>
      </button>
    </view>
  </view>
</view>

<!-- 模型选择弹窗 -->
<view class="modal-overlay" wx:if="{{showModelModal}}" bindtap="hideModelModal">
  <view class="modal-content card elevated scale-in" catchtap="stopPropagation">
    <view class="modal-header flex flex-between">
      <text class="title-secondary">选择AI模型</text>
      <button class="btn btn-ghost btn-small" bindtap="hideModelModal">×</button>
    </view>
    <view class="model-list mt-lg">
      <view class="model-option card {{item.id === selectedModelId ? 'selected' : ''}}"
            wx:for="{{availableModels}}"
            wx:key="id"
            bindtap="selectModel"
            data-model="{{item}}">
        <view class="model-info">
          <text class="text-body">{{item.name}}</text>
          <text class="text-caption text-secondary">{{item.description}}</text>
        </view>
        <view class="status-indicator status-{{item.status}}">
          <text>{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
