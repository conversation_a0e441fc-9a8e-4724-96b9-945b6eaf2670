// utils/ivdPrompts.js
// IVD领域专业提示词管理

/**
 * IVD专业提示词管理器
 */
class IVDPromptManager {
  constructor() {
    this.systemPrompts = {
      base: `你是一位资深的IVD（体外诊断）行业专家，拥有超过15年的医疗器械研发、注册申报和市场销售经验。你熟悉中国NMPA、欧盟CE、美国FDA等主要法规体系，深度了解ISO13485质量管理体系，并具有丰富的产品商业化经验。

请以专业、准确、实用的方式回答用户问题，提供具体可操作的建议。回答时请：
1. 使用专业术语，但确保表达清晰易懂
2. 提供具体的步骤和要点
3. 引用相关法规和标准
4. 结合实际案例说明
5. 给出风险提示和注意事项`,

      rd: `你是IVD产品研发领域的技术专家，专注于：
- 产品需求分析和市场调研
- 技术路线选择和可行性评估  
- 产品设计开发和验证确认
- 临床试验设计和实施
- 质量管理体系建立
- 风险管理和控制

请重点关注技术实现、开发流程、质量控制等方面，提供详细的技术指导。`,

      registration: `你是IVD产品注册申报的法规专家，精通：
- 中国NMPA注册申报流程和要求
- 欧盟CE认证（IVDR法规）
- 美国FDA 510(k)和PMA申报
- 产品分类和风险评估
- 技术文档编制和审评要点
- 临床评价和临床试验要求

请重点关注法规要求、申报策略、文档准备等方面，确保合规性。`,

      sales: `你是IVD产品市场销售的商业专家，擅长：
- 市场分析和竞争策略
- 客户需求挖掘和价值主张
- 销售渠道建设和管理
- 定价策略和商务谈判
- 客户关系管理和维护
- 市场推广和品牌建设

请重点关注商业策略、市场拓展、客户管理等方面，提供实用的商业建议。`,

      quality: `你是IVD行业质量管理专家，专精于：
- ISO13485质量管理体系建立和维护
- 质量风险管理（ISO14971）
- 设计控制和变更管理
- 供应商管理和采购控制
- 生产质量控制和放行
- 不良事件监测和处理

请重点关注质量体系、风险控制、合规管理等方面，确保产品质量和安全。`
    };

    this.contextPrompts = {
      // 产品研发相关提示
      rd_planning: "在产品规划阶段，重点关注市场需求分析、技术可行性评估、竞争对手分析等关键要素。",
      rd_design: "在设计开发阶段，强调设计输入、设计输出、设计验证、设计确认的完整性和可追溯性。",
      rd_testing: "在测试验证阶段，注重分析性能验证、稳定性研究、参考区间建立等关键验证活动。",
      
      // 注册申报相关提示
      reg_classification: "产品分类是注册申报的基础，需要准确理解分类规则和风险等级评估方法。",
      reg_documentation: "技术文档是申报的核心，包括产品技术要求、研究资料、临床评价等关键文件。",
      reg_process: "申报流程需要严格按照法规要求执行，注意时间节点和沟通策略。",
      
      // 市场销售相关提示
      sales_market: "市场分析是销售成功的基础，需要深入了解目标客户、竞争格局和市场趋势。",
      sales_channels: "渠道建设要考虑直销、经销、代理等多种模式，选择最适合的渠道策略。",
      sales_support: "销售支持体系包括技术支持、培训服务、售后服务等多个维度。",
      
      // 质量管理相关提示
      quality_system: "质量管理体系建立要基于ISO13485标准，结合企业实际情况进行定制化设计。",
      quality_risk: "风险管理贯穿产品全生命周期，需要建立系统性的风险识别、评估和控制机制。",
      quality_improvement: "持续改进是质量管理的核心，通过数据分析和过程优化不断提升质量水平。"
    };

    this.responseTemplates = {
      // 结构化回答模板
      structured: {
        overview: "## 概述\n{overview}\n\n",
        keyPoints: "## 关键要点\n{keyPoints}\n\n",
        steps: "## 具体步骤\n{steps}\n\n",
        considerations: "## 注意事项\n{considerations}\n\n",
        references: "## 相关法规\n{references}"
      },
      
      // 问题解答模板
      qa: {
        answer: "**回答：**\n{answer}\n\n",
        explanation: "**详细说明：**\n{explanation}\n\n",
        examples: "**实例参考：**\n{examples}\n\n",
        tips: "**专业建议：**\n{tips}"
      }
    };
  }

  /**
   * 获取系统提示词
   * @param {string} category - 分类
   * @param {string} subCategory - 子分类
   * @returns {string} 系统提示词
   */
  getSystemPrompt(category, subCategory = null) {
    let prompt = this.systemPrompts.base;
    
    if (category && this.systemPrompts[category]) {
      prompt += "\n\n" + this.systemPrompts[category];
    }
    
    if (subCategory) {
      const contextKey = `${category}_${subCategory}`;
      if (this.contextPrompts[contextKey]) {
        prompt += "\n\n" + this.contextPrompts[contextKey];
      }
    }
    
    return prompt;
  }

  /**
   * 生成专业化的用户提示
   * @param {string} userMessage - 用户消息
   * @param {string} category - 分类
   * @returns {string} 增强后的用户消息
   */
  enhanceUserMessage(userMessage, category) {
    const categoryContext = {
      rd: "作为IVD产品研发相关的问题：",
      registration: "作为IVD产品注册申报相关的问题：",
      sales: "作为IVD产品市场销售相关的问题：",
      quality: "作为IVD质量管理相关的问题："
    };

    const context = categoryContext[category] || "作为IVD行业相关的问题：";
    return `${context}\n\n${userMessage}\n\n请提供专业、详细、可操作的建议。`;
  }

  /**
   * 格式化AI回复
   * @param {string} response - AI原始回复
   * @param {string} template - 模板类型
   * @returns {string} 格式化后的回复
   */
  formatResponse(response, template = 'structured') {
    // 这里可以根据需要对AI回复进行格式化处理
    // 例如添加结构化标记、专业术语解释等
    
    // 添加专业免责声明
    const disclaimer = "\n\n---\n*以上建议仅供参考，具体实施请结合实际情况并咨询相关专业人士。*";
    
    return response + disclaimer;
  }

  /**
   * 生成后续问题建议
   * @param {string} userMessage - 用户消息
   * @param {string} category - 分类
   * @returns {Array} 建议问题列表
   */
  generateFollowUpQuestions(userMessage, category) {
    const followUpTemplates = {
      rd: [
        "这个产品的技术难点在哪里？",
        "需要进行哪些关键验证？",
        "如何制定开发时间计划？",
        "有哪些风险需要重点关注？"
      ],
      registration: [
        "需要准备哪些核心文件？",
        "预计审评时间是多久？",
        "有哪些常见的审评问题？",
        "如何提高申报成功率？"
      ],
      sales: [
        "目标客户群体是什么？",
        "如何制定定价策略？",
        "竞争对手分析怎么做？",
        "如何建立销售团队？"
      ],
      quality: [
        "如何建立质量体系？",
        "需要哪些质量文件？",
        "如何进行风险评估？",
        "怎样实施持续改进？"
      ]
    };

    return followUpTemplates[category] || [];
  }

  /**
   * 检测消息的专业程度
   * @param {string} message - 消息内容
   * @returns {Object} 专业程度分析结果
   */
  analyzeProfessionalLevel(message) {
    const professionalTerms = {
      high: ['ISO13485', 'NMPA', 'FDA', 'CE', 'IVDR', 'GMP', '510k', 'PMA'],
      medium: ['注册', '申报', '验证', '确认', '风险管理', '质量控制'],
      basic: ['产品', '开发', '销售', '市场', '客户', '价格']
    };

    let level = 'basic';
    let score = 0;

    for (const [levelName, terms] of Object.entries(professionalTerms)) {
      const foundTerms = terms.filter(term => 
        message.toLowerCase().includes(term.toLowerCase())
      );
      
      if (foundTerms.length > 0) {
        if (levelName === 'high') {
          level = 'high';
          score += foundTerms.length * 3;
        } else if (levelName === 'medium' && level !== 'high') {
          level = 'medium';
          score += foundTerms.length * 2;
        } else if (levelName === 'basic' && level === 'basic') {
          score += foundTerms.length;
        }
      }
    }

    return {
      level,
      score,
      isHighlyProfessional: level === 'high',
      needsSimplification: level === 'basic' && score < 2
    };
  }
}

// 创建全局实例
const ivdPromptManager = new IVDPromptManager();

module.exports = {
  ivdPromptManager,
  IVDPromptManager
};
