// pages/login/login.js
const app = getApp()
const { cloudCall } = require('../../utils/cloudFunction')

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    isLoading: false,
    loadingText: '登录中...',
    showAgreementModal: false,
    showPrivacyModal: false
  },

  onLoad(options) {
    console.log('登录页面加载')
    
    // 检查是否已经登录
    this.checkLoginStatus()
    
    // 初始化云开发
    this.initCloud()
  },

  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus()
  },

  /**
   * 初始化云开发
   */
  initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      return
    }
    
    wx.cloud.init({
      env: 'cloudbase-7g8nxwah43c62b19', // 替换为你的云开发环境ID
      traceUser: true
    })
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo
    const isLoggedIn = !!userInfo && !!userInfo.openid
    
    this.setData({
      isLoggedIn,
      userInfo: userInfo || {}
    })
  },

  /**
   * 微信一键登录
   */
  onWechatLogin() {
    console.log('点击微信一键登录按钮')

    // 显示加载状态
    this.setData({
      isLoading: true,
      loadingText: '正在登录...'
    })

    // 检查云开发是否初始化
    if (!wx.cloud) {
      console.error('云开发未初始化')
      this.setData({ isLoading: false })
      wx.showModal({
        title: '登录失败',
        content: '云开发环境未初始化，请检查配置',
        showCancel: false
      })
      return
    }

    // 使用wx.login获取code进行一键登录
    wx.login({
      success: (res) => {
        console.log('wx.login成功:', res)
        if (res.code) {
          // 调用云函数进行一键登录，自动获取用户信息
          this.performOneClickLogin(res.code)
        } else {
          console.error('wx.login未返回code')
          this.setData({
            isLoading: false
          })
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'error'
          })
        }
      },
      fail: (err) => {
        console.error('wx.login失败:', err)
        this.setData({
          isLoading: false
        })
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        })
      }
    })
  },



  /**
   * 获取用户信息并登录（兼容旧版本）
   */
  onGetUserProfile(e) {
    if (e.detail.errMsg === 'getUserProfile:ok') {
      this.setData({
        isLoading: true,
        loadingText: '登录中...'
      })

      this.performLoginWithUserInfo(e.detail.userInfo)
    } else {
      wx.showToast({
        title: '登录取消',
        icon: 'none'
      })
    }
  },

  /**
   * 执行一键登录（使用code）
   */
  async performOneClickLogin(code) {
    try {
      console.log('开始调用一键登录云函数，code:', code)

      // 调用云函数进行一键登录，云端自动获取用户信息
      const result = await cloudCall.login({
        code,
        loginType: 'wechat_oneclick'
      })

      console.log('一键登录云函数调用结果:', result)

      if (result.result.success) {
        const userData = result.result.data

        // 保存用户信息到全局数据
        app.globalData.userInfo = {
          openid: userData.openid,
          unionid: userData.unionid,
          nickName: userData.nickName || '微信用户',
          avatarUrl: userData.avatarUrl || '/images/default-avatar.png',
          userId: userData.userId,
          isNewUser: userData.isNewUser,
          loginType: userData.loginType,
          cloudStorageEnabled: userData.cloudStorageEnabled
        }

        // 保存到本地存储
        wx.setStorageSync('userInfo', app.globalData.userInfo)

        // 如果启用了云存储，同步本地数据
        if (userData.cloudStorageEnabled) {
          await this.syncWithCloudStorage()
        }

        // 如果是新用户，加载默认偏好设置
        if (userData.isNewUser) {
          await this.loadUserPreferences()
        } else {
          // 加载用户的偏好设置和数据
          await this.loadUserData()
        }

        this.setData({
          isLoggedIn: true,
          userInfo: app.globalData.userInfo,
          isLoading: false
        })

        const loginMessage = userData.isNewUser ? '欢迎使用IVD智能顾问' : '欢迎回来'
        wx.showToast({
          title: loginMessage,
          icon: 'success'
        })

        // 延迟跳转到首页
        setTimeout(() => {
          this.goToHome()
        }, 1500)

      } else {
        throw new Error(result.result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)

      this.setData({
        isLoading: false
      })

      wx.showModal({
        title: '登录失败',
        content: error.message || '网络异常，请稍后重试',
        showCancel: false
      })
    }
  },

  /**
   * 执行登录（使用用户信息，兼容旧版本）
   */
  async performLoginWithUserInfo(userInfo) {
    try {
      // 先获取code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (!loginRes.code) {
        throw new Error('获取登录凭证失败')
      }

      // 调用云函数进行登录
      const result = await cloudCall.login({
        code: loginRes.code,
        userInfo,
        loginType: 'wechat'
      })

      if (result.result.success) {
        const userData = result.result.data

        // 保存用户信息到全局数据
        app.globalData.userInfo = {
          ...userInfo,
          openid: userData.openid,
          userId: userData.userId,
          isNewUser: userData.isNewUser
        }

        // 保存到本地存储
        wx.setStorageSync('userInfo', app.globalData.userInfo)

        // 如果是新用户，加载默认偏好设置
        if (userData.isNewUser) {
          await this.loadUserPreferences()
        } else {
          // 加载用户的偏好设置和数据
          await this.loadUserData()
        }

        this.setData({
          isLoggedIn: true,
          userInfo: app.globalData.userInfo,
          isLoading: false
        })

        wx.showToast({
          title: userData.isNewUser ? '注册成功' : '登录成功',
          icon: 'success'
        })

        // 延迟跳转到首页
        setTimeout(() => {
          this.goToHome()
        }, 1500)

      } else {
        throw new Error(result.result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)

      this.setData({
        isLoading: false
      })

      wx.showModal({
        title: '登录失败',
        content: error.message || '网络异常，请稍后重试',
        showCancel: false
      })
    }
  },

  /**
   * 加载用户偏好设置
   */
  async loadUserPreferences() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getPreferences'
        }
      })

      if (result.result.success) {
        const preferences = result.result.data
        
        // 合并到全局数据
        app.globalData.userPreferences = preferences
        
        // 应用偏好设置
        if (preferences.selectedAIModel) {
          app.globalData.selectedAIModel = preferences.selectedAIModel
        }
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error)
    }
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    try {
      // 加载偏好设置
      await this.loadUserPreferences()
      
      // 加载API密钥
      const apiResult = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getAPIKeys'
        }
      })

      if (apiResult.result.success) {
        app.globalData.apiKeys = {
          ...app.globalData.apiKeys,
          ...apiResult.result.data
        }
      }
      
      // 加载最近的聊天记录
      const chatResult = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getChatHistory',
          data: { limit: 10 }
        }
      })

      if (chatResult.result.success) {
        // 处理聊天记录数据
        const chatHistory = chatResult.result.data
        if (chatHistory.length > 0) {
          // 合并最近的聊天记录
          app.globalData.recentChatSessions = chatHistory
        }
      }
      
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  },

  /**
   * 游客登录
   */
  guestLogin() {
    // 设置游客模式
    app.globalData.userInfo = {
      isGuest: true,
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.png'
    }
    
    wx.setStorageSync('userInfo', app.globalData.userInfo)
    
    wx.showToast({
      title: '进入游客模式',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          this.goToHome()
        }, 1000)
      }
    })
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '退出登录后将清除本地数据，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          app.globalData.userInfo = null
          app.globalData.userPreferences = {}
          app.globalData.apiKeys = {
            openai: '',
            anthropic: '',
            google: ''
          }
          app.globalData.chatHistory = []
          
          // 清除本地存储
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('userPreferences')
          wx.removeStorageSync('apiKeys')
          wx.removeStorageSync('chatHistory')
          
          this.setData({
            isLoggedIn: false,
            userInfo: null
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 跳转到首页
   */
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    this.setData({
      showAgreementModal: true
    })
  },

  /**
   * 隐藏用户协议
   */
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    })
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    this.setData({
      showPrivacyModal: true
    })
  },

  /**
   * 隐藏隐私政策
   */
  hidePrivacyModal() {
    this.setData({
      showPrivacyModal: false
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },



  /**
   * 同步云存储数据
   */
  async syncWithCloudStorage() {
    try {
      console.log('开始同步云存储数据')

      // 调用云函数同步用户数据
      const result = await wx.cloud.callFunction({
        name: 'syncUserData',
        data: {
          action: 'sync',
          userInfo: app.globalData.userInfo
        }
      })

      if (result.result && result.result.success) {
        console.log('云存储数据同步成功')

        // 更新本地数据
        if (result.result.data) {
          const syncedData = result.result.data

          // 合并同步的数据到本地
          Object.assign(app.globalData, syncedData)

          // 保存到本地存储
          wx.setStorageSync('userSettings', syncedData.userSettings || {})
          wx.setStorageSync('chatHistory', syncedData.chatHistory || [])
        }
      }
    } catch (error) {
      console.error('云存储数据同步失败:', error)
      // 不影响登录流程，只记录错误
    }
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 您的专属医疗器械咨询助手',
      path: '/pages/login/login'
    }
  }
})
