// pages/login/login.js
const app = getApp()

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    isLoading: false,
    loadingText: '登录中...',
    showAgreementModal: false,
    showPrivacyModal: false
  },

  onLoad(options) {
    console.log('登录页面加载')
    
    // 检查是否已经登录
    this.checkLoginStatus()
    
    // 初始化云开发
    this.initCloud()
  },

  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus()
  },

  /**
   * 初始化云开发
   */
  initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      return
    }
    
    wx.cloud.init({
      env: 'your-env-id', // 替换为你的云开发环境ID
      traceUser: true
    })
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo
    const isLoggedIn = !!userInfo && !!userInfo.openid
    
    this.setData({
      isLoggedIn,
      userInfo: userInfo || {}
    })
  },

  /**
   * 获取用户信息并登录
   */
  onGetUserProfile(e) {
    if (e.detail.errMsg === 'getUserProfile:ok') {
      this.setData({
        isLoading: true,
        loadingText: '登录中...'
      })
      
      this.performLogin(e.detail.userInfo)
    } else {
      wx.showToast({
        title: '登录取消',
        icon: 'none'
      })
    }
  },

  /**
   * 执行登录
   */
  async performLogin(userInfo) {
    try {
      // 调用云函数进行登录
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          userInfo,
          loginType: 'wechat'
        }
      })

      if (result.result.success) {
        const userData = result.result.data
        
        // 保存用户信息到全局数据
        app.globalData.userInfo = {
          ...userData.userInfo,
          userId: userData.userId,
          openid: userData.openid,
          isNewUser: userData.isNewUser
        }
        
        // 保存到本地存储
        wx.setStorageSync('userInfo', app.globalData.userInfo)
        
        // 如果是新用户，加载默认偏好设置
        if (userData.isNewUser) {
          await this.loadUserPreferences()
        } else {
          // 加载用户的偏好设置和数据
          await this.loadUserData()
        }
        
        this.setData({
          isLoggedIn: true,
          userInfo: app.globalData.userInfo,
          isLoading: false
        })
        
        wx.showToast({
          title: userData.isNewUser ? '注册成功' : '登录成功',
          icon: 'success'
        })
        
        // 延迟跳转到首页
        setTimeout(() => {
          this.goToHome()
        }, 1500)
        
      } else {
        throw new Error(result.result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      
      this.setData({
        isLoading: false
      })
      
      wx.showModal({
        title: '登录失败',
        content: error.message || '网络异常，请稍后重试',
        showCancel: false
      })
    }
  },

  /**
   * 加载用户偏好设置
   */
  async loadUserPreferences() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getPreferences'
        }
      })

      if (result.result.success) {
        const preferences = result.result.data
        
        // 合并到全局数据
        app.globalData.userPreferences = preferences
        
        // 应用偏好设置
        if (preferences.selectedAIModel) {
          app.globalData.selectedAIModel = preferences.selectedAIModel
        }
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error)
    }
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    try {
      // 加载偏好设置
      await this.loadUserPreferences()
      
      // 加载API密钥
      const apiResult = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getAPIKeys'
        }
      })

      if (apiResult.result.success) {
        app.globalData.apiKeys = {
          ...app.globalData.apiKeys,
          ...apiResult.result.data
        }
      }
      
      // 加载最近的聊天记录
      const chatResult = await wx.cloud.callFunction({
        name: 'userdata',
        data: {
          action: 'getChatHistory',
          data: { limit: 10 }
        }
      })

      if (chatResult.result.success) {
        // 处理聊天记录数据
        const chatHistory = chatResult.result.data
        if (chatHistory.length > 0) {
          // 合并最近的聊天记录
          app.globalData.recentChatSessions = chatHistory
        }
      }
      
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  },

  /**
   * 游客登录
   */
  guestLogin() {
    // 设置游客模式
    app.globalData.userInfo = {
      isGuest: true,
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.png'
    }
    
    wx.setStorageSync('userInfo', app.globalData.userInfo)
    
    wx.showToast({
      title: '进入游客模式',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          this.goToHome()
        }, 1000)
      }
    })
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '退出登录后将清除本地数据，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          app.globalData.userInfo = null
          app.globalData.userPreferences = {}
          app.globalData.apiKeys = {
            openai: '',
            anthropic: '',
            google: ''
          }
          app.globalData.chatHistory = []
          
          // 清除本地存储
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('userPreferences')
          wx.removeStorageSync('apiKeys')
          wx.removeStorageSync('chatHistory')
          
          this.setData({
            isLoggedIn: false,
            userInfo: null
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 跳转到首页
   */
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    this.setData({
      showAgreementModal: true
    })
  },

  /**
   * 隐藏用户协议
   */
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    })
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    this.setData({
      showPrivacyModal: true
    })
  },

  /**
   * 隐藏隐私政策
   */
  hidePrivacyModal() {
    this.setData({
      showPrivacyModal: false
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 您的专属医疗器械咨询助手',
      path: '/pages/login/login'
    }
  }
})
