/* components/premiumGate/premiumGate.wxss */

.premium-gate-container {
  margin: var(--spacing-md) 0;
}

.premium-gate {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--primary-gradient);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-base);
}

.gate-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.lock-icon {
  font-size: 80rpx;
  margin-bottom: var(--spacing-sm);
}

.gate-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.gate-description {
  font-size: var(--font-size-base);
  opacity: 0.9;
  line-height: var(--line-height-loose);
  margin-bottom: var(--spacing-md);
}

.plan-info {
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-base);
  margin-bottom: var(--spacing-sm);
}

.plan-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.limit-info {
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-base);
  margin-bottom: var(--spacing-sm);
}

.limit-text {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.reset-text {
  display: block;
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

.gate-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
  margin-top: var(--spacing-md);
}

.gate-actions .btn {
  width: 100%;
}

.premium-content {
  /* 内容区域样式 */
}

/* 使用量指示器 */
.usage-indicator {
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-base);
}

.usage-bar {
  width: 100%;
  height: 8rpx;
  background: var(--border-light);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.usage-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 4rpx;
  transition: width var(--transition-base);
}

.usage-text {
  text-align: center;
  display: block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .gate-actions {
    flex-direction: row;
    justify-content: center;
  }
  
  .gate-actions .btn {
    width: auto;
    min-width: 200rpx;
  }
}
