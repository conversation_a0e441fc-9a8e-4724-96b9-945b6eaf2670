// pages/history/history.js
const app = getApp()
const { cloudCall } = require('../../utils/cloudFunction')

Page({
  data: {
    historyList: [],
    searchKeyword: '',
    activeTab: 'all',
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    showDeleteModal: false,
    deleteTargetId: '',
    categoryNames: {
      'rd': '产品研发',
      'registration': '注册申报',
      'sales': '市场销售',
      'quality': '质量管理',
      'general': '通用咨询'
    }
  },

  onLoad() {
    console.log('对话历史页面加载')
    this.loadHistoryList()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshHistoryList()
  },

  onPullDownRefresh() {
    this.refreshHistoryList()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadMore()
    }
  },

  /**
   * 加载对话历史列表
   */
  async loadHistoryList(isLoadMore = false) {
    if (!isLoadMore) {
      this.setData({ isLoading: true })
    } else {
      this.setData({ isLoadingMore: true })
    }

    try {
      const result = await cloudCall.userdata('getChatHistory', {
        page: isLoadMore ? this.data.page : 1,
        pageSize: this.data.pageSize,
        category: this.data.activeTab === 'all' ? null : this.data.activeTab,
        keyword: this.data.searchKeyword
      })

      if (result.result && result.result.success) {
        const newList = result.result.data.list || []
        const processedList = this.processHistoryList(newList)

        this.setData({
          historyList: isLoadMore ? [...this.data.historyList, ...processedList] : processedList,
          hasMore: result.result.data.hasMore || false,
          page: isLoadMore ? this.data.page + 1 : 2
        })
      }

    } catch (error) {
      console.error('加载对话历史失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({
        isLoading: false,
        isLoadingMore: false
      })
      
      if (!isLoadMore) {
        wx.stopPullDownRefresh()
      }
    }
  },

  /**
   * 处理历史记录数据
   */
  processHistoryList(list) {
    return list.map(item => ({
      ...item,
      categoryName: this.data.categoryNames[item.category] || '通用咨询',
      timeAgo: this.formatTimeAgo(item.updatedAt),
      lastMessage: this.truncateMessage(item.lastMessage || ''),
      messageCount: item.messages?.length || 0
    }))
  },

  /**
   * 格式化时间
   */
  formatTimeAgo(timestamp) {
    const now = new Date()
    const time = new Date(timestamp)
    const diff = now.getTime() - time.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return time.toLocaleDateString()
  },

  /**
   * 截断消息内容
   */
  truncateMessage(message, maxLength = 100) {
    if (message.length <= maxLength) return message
    return message.substring(0, maxLength) + '...'
  },

  /**
   * 刷新历史记录
   */
  refreshHistoryList() {
    this.setData({
      page: 1,
      hasMore: true
    })
    this.loadHistoryList()
  },

  /**
   * 加载更多
   */
  loadMore() {
    this.loadHistoryList(true)
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.refreshHistoryList()
  },

  /**
   * 切换分类标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
    this.refreshHistoryList()
  },

  /**
   * 打开对话
   */
  openChat(e) {
    const chatId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/chat/chat?chatId=${chatId}`
    })
  },

  /**
   * 分享对话
   */
  shareChat(e) {
    const chatId = e.currentTarget.dataset.id
    const chatItem = this.data.historyList.find(item => item.id === chatId)
    
    if (!chatItem) return

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    // 可以在这里实现更复杂的分享逻辑
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    })
  },

  /**
   * 删除对话
   */
  deleteChat(e) {
    const chatId = e.currentTarget.dataset.id
    this.setData({
      showDeleteModal: true,
      deleteTargetId: chatId
    })
  },

  /**
   * 隐藏删除弹窗
   */
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteTargetId: ''
    })
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    const chatId = this.data.deleteTargetId
    
    if (!chatId) return

    wx.showLoading({
      title: '删除中...'
    })

    try {
      const result = await cloudCall.userdata('deleteChatHistory', {
        chatId
      })

      if (result.result && result.result.success) {
        // 从列表中移除已删除的项目
        const newList = this.data.historyList.filter(item => item.id !== chatId)
        this.setData({
          historyList: newList
        })

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } else {
        throw new Error(result.result?.message || '删除失败')
      }

    } catch (error) {
      console.error('删除对话失败:', error)
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
      this.hideDeleteModal()
    }
  },

  /**
   * 开始新对话
   */
  startNewChat() {
    wx.navigateTo({
      url: '/pages/chat/chat'
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业医疗器械咨询',
      path: '/pages/index/index'
    }
  }
})
