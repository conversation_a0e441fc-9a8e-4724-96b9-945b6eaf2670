#!/usr/bin/env node

// scripts/remove-debug-features.js
// 移除生产环境中的调试功能

const fs = require('fs');
const path = require('path');

/**
 * 生产环境清理器
 */
class ProductionCleaner {
  constructor() {
    this.projectRoot = process.cwd();
    this.cleanupStats = {
      filesProcessed: 0,
      consoleLogsRemoved: 0,
      debugFeaturesRemoved: 0,
      configsUpdated: 0
    };
  }

  /**
   * 执行完整的生产环境清理
   */
  async cleanForProduction() {
    console.log('🧹 开始清理生产环境...\n');

    // 1. 移除console.log语句
    await this.removeConsoleStatements();

    // 2. 移除调试功能
    await this.removeDebugFeatures();

    // 3. 更新配置文件
    await this.updateConfigurations();

    // 4. 移除测试数据
    await this.removeTestData();

    // 5. 生成清理报告
    this.generateReport();
  }

  /**
   * 移除console语句
   */
  async removeConsoleStatements() {
    console.log('📝 移除console语句...');
    
    const jsFiles = this.findJSFiles();
    
    for (const filePath of jsFiles) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;

        // 移除console.log, console.info, console.warn (保留console.error)
        content = content.replace(/console\.(log|info|warn)\s*\([^)]*\)\s*;?\s*\n?/g, '');
        
        // 移除单行注释中的console调用
        content = content.replace(/\/\/.*console\.(log|info|warn).*\n/g, '');

        if (content !== originalContent) {
          fs.writeFileSync(filePath, content, 'utf8');
          this.cleanupStats.consoleLogsRemoved++;
          console.log(`  ✅ 已清理: ${path.relative(this.projectRoot, filePath)}`);
        }

        this.cleanupStats.filesProcessed++;
      } catch (error) {
        console.error(`  ❌ 处理失败: ${filePath}`, error.message);
      }
    }
  }

  /**
   * 移除调试功能
   */
  async removeDebugFeatures() {
    console.log('\n🔧 移除调试功能...');

    // 移除设置页面中的危险操作
    await this.removeFromSettingsPage();

    // 移除调试按钮和面板
    await this.removeDebugButtons();

    // 移除测试模式代码
    await this.removeTestModeCode();
  }

  /**
   * 移除设置页面中的危险操作
   */
  async removeFromSettingsPage() {
    const settingsWxmlPath = path.join(this.projectRoot, 'pages/settings/settings.wxml');
    const settingsJsPath = path.join(this.projectRoot, 'pages/settings/settings.js');

    // 移除WXML中的危险操作区域
    if (fs.existsSync(settingsWxmlPath)) {
      let content = fs.readFileSync(settingsWxmlPath, 'utf8');
      
      // 移除危险操作区域
      content = content.replace(/<!-- 危险操作 -->[\s\S]*?<\/view>\s*<\/view>/g, '');
      
      fs.writeFileSync(settingsWxmlPath, content, 'utf8');
      console.log('  ✅ 已移除设置页面危险操作区域');
      this.cleanupStats.debugFeaturesRemoved++;
    }

    // 移除JS中的危险操作方法
    if (fs.existsSync(settingsJsPath)) {
      let content = fs.readFileSync(settingsJsPath, 'utf8');
      
      // 移除clearAllData和resetSettings方法
      content = content.replace(/\/\/\s*清空所有数据[\s\S]*?},/g, '');
      content = content.replace(/\/\/\s*重置设置[\s\S]*?},/g, '');
      
      fs.writeFileSync(settingsJsPath, content, 'utf8');
      console.log('  ✅ 已移除设置页面危险操作方法');
      this.cleanupStats.debugFeaturesRemoved++;
    }
  }

  /**
   * 移除调试按钮
   */
  async removeDebugButtons() {
    const files = this.findWXMLFiles();
    
    for (const filePath of files) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;

        // 移除包含debug、test、清空、重置等关键词的按钮
        content = content.replace(/<button[^>]*bindtap="(clearAllData|resetSettings|debugMode|testFunction)"[^>]*>[\s\S]*?<\/button>/g, '');
        
        // 移除调试相关的view
        content = content.replace(/<view[^>]*class="[^"]*debug[^"]*"[^>]*>[\s\S]*?<\/view>/g, '');

        if (content !== originalContent) {
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ 已清理调试按钮: ${path.relative(this.projectRoot, filePath)}`);
          this.cleanupStats.debugFeaturesRemoved++;
        }
      } catch (error) {
        console.error(`  ❌ 处理失败: ${filePath}`, error.message);
      }
    }
  }

  /**
   * 移除测试模式代码
   */
  async removeTestModeCode() {
    const jsFiles = this.findJSFiles();
    
    for (const filePath of jsFiles) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;

        // 移除测试模式相关代码
        content = content.replace(/\/\/\s*TEST[\s\S]*?\n/g, '');
        content = content.replace(/\/\*\s*DEBUG[\s\S]*?\*\//g, '');
        content = content.replace(/if\s*\(\s*DEBUG\s*\)[\s\S]*?}/g, '');
        content = content.replace(/const\s+DEBUG\s*=\s*true\s*;?\s*\n?/g, '');

        if (content !== originalContent) {
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ 已清理测试代码: ${path.relative(this.projectRoot, filePath)}`);
          this.cleanupStats.debugFeaturesRemoved++;
        }
      } catch (error) {
        console.error(`  ❌ 处理失败: ${filePath}`, error.message);
      }
    }
  }

  /**
   * 更新配置文件
   */
  async updateConfigurations() {
    console.log('\n⚙️ 更新配置文件...');

    // 更新app.json
    await this.updateAppJson();

    // 更新project.config.json
    await this.updateProjectConfig();

    // 更新logger配置
    await this.updateLoggerConfig();
  }

  /**
   * 更新app.json
   */
  async updateAppJson() {
    const appJsonPath = path.join(this.projectRoot, 'app.json');
    
    if (fs.existsSync(appJsonPath)) {
      const content = fs.readFileSync(appJsonPath, 'utf8');
      const config = JSON.parse(content);
      
      // 关闭调试模式
      config.debug = false;
      
      fs.writeFileSync(appJsonPath, JSON.stringify(config, null, 2), 'utf8');
      console.log('  ✅ 已更新app.json - 关闭调试模式');
      this.cleanupStats.configsUpdated++;
    }
  }

  /**
   * 更新project.config.json
   */
  async updateProjectConfig() {
    const configPath = path.join(this.projectRoot, 'project.config.json');
    
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(content);
      
      // 生产环境设置
      if (config.setting) {
        config.setting.urlCheck = true;
        config.setting.compileHotReLoad = false;
        config.setting.uploadWithSourceMap = false;
        config.setting.minified = true;
      }
      
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
      console.log('  ✅ 已更新project.config.json - 生产环境配置');
      this.cleanupStats.configsUpdated++;
    }
  }

  /**
   * 更新Logger配置
   */
  async updateLoggerConfig() {
    const loggerPath = path.join(this.projectRoot, 'utils/logger.js');
    
    if (fs.existsSync(loggerPath)) {
      let content = fs.readFileSync(loggerPath, 'utf8');
      
      // 设置为生产环境
      content = content.replace(/this\.isProduction\s*=\s*false/, 'this.isProduction = true');
      content = content.replace(/this\.logLevel\s*=\s*'info'/, 'this.logLevel = \'error\'');
      
      fs.writeFileSync(loggerPath, content, 'utf8');
      console.log('  ✅ 已更新logger.js - 生产环境日志配置');
      this.cleanupStats.configsUpdated++;
    }
  }

  /**
   * 移除测试数据
   */
  async removeTestData() {
    console.log('\n🗑️ 移除测试数据...');

    // 移除测试文件
    const testFiles = [
      'test/',
      'tests/',
      '__tests__/',
      'spec/',
      'scripts/test-data.js',
      'mock/',
      'fixtures/'
    ];

    for (const testPath of testFiles) {
      const fullPath = path.join(this.projectRoot, testPath);
      if (fs.existsSync(fullPath)) {
        this.removeDirectory(fullPath);
        console.log(`  ✅ 已移除测试目录: ${testPath}`);
      }
    }
  }

  /**
   * 递归删除目录
   */
  removeDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
      fs.readdirSync(dirPath).forEach((file) => {
        const curPath = path.join(dirPath, file);
        if (fs.lstatSync(curPath).isDirectory()) {
          this.removeDirectory(curPath);
        } else {
          fs.unlinkSync(curPath);
        }
      });
      fs.rmdirSync(dirPath);
    }
  }

  /**
   * 查找所有JS文件
   */
  findJSFiles() {
    return this.findFiles('.js');
  }

  /**
   * 查找所有WXML文件
   */
  findWXMLFiles() {
    return this.findFiles('.wxml');
  }

  /**
   * 查找指定扩展名的文件
   */
  findFiles(extension) {
    const files = [];
    
    const scanDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', 'miniprogram_npm', 'test', 'tests', '__tests__'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (item.endsWith(extension)) {
          files.push(fullPath);
        }
      }
    };
    
    scanDir(this.projectRoot);
    return files;
  }

  /**
   * 生成清理报告
   */
  generateReport() {
    console.log('\n📊 清理完成报告:');
    console.log('=====================================');
    console.log(`处理文件数: ${this.cleanupStats.filesProcessed}`);
    console.log(`移除console语句: ${this.cleanupStats.consoleLogsRemoved} 个文件`);
    console.log(`移除调试功能: ${this.cleanupStats.debugFeaturesRemoved} 个功能`);
    console.log(`更新配置文件: ${this.cleanupStats.configsUpdated} 个文件`);
    console.log('=====================================');
    console.log('✨ 生产环境清理完成！');
    console.log('\n📝 建议后续操作:');
    console.log('1. 重新编译项目');
    console.log('2. 进行完整测试');
    console.log('3. 检查功能完整性');
    console.log('4. 提交到版本控制');
  }
}

/**
 * 主函数
 */
async function main() {
  const cleaner = new ProductionCleaner();
  await cleaner.cleanForProduction();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ProductionCleaner;
