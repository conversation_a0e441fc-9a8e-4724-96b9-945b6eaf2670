<!--pages/history/history.wxml-->
<view class="page-container">
  <!-- 头部区域 -->
  <view class="header-section">
    <view class="history-header card gradient-bg">
      <view class="header-content">
        <text class="title-primary text-inverse">对话历史</text>
        <text class="text-caption text-inverse">查看和管理您的所有对话记录</text>
      </view>
    </view>
  </view>

  <!-- 搜索和筛选 -->
  <view class="filter-section">
    <view class="search-bar">
      <input 
        class="search-input input-field" 
        placeholder="搜索对话内容..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <button class="search-btn btn btn-primary btn-small" bindtap="onSearch">
        <text>搜索</text>
      </button>
    </view>
    
    <view class="filter-tabs">
      <view class="tab-item {{activeTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">
        <text>全部</text>
      </view>
      <view class="tab-item {{activeTab === 'rd' ? 'active' : ''}}" bindtap="switchTab" data-tab="rd">
        <text>研发</text>
      </view>
      <view class="tab-item {{activeTab === 'registration' ? 'active' : ''}}" bindtap="switchTab" data-tab="registration">
        <text>注册</text>
      </view>
      <view class="tab-item {{activeTab === 'sales' ? 'active' : ''}}" bindtap="switchTab" data-tab="sales">
        <text>销售</text>
      </view>
      <view class="tab-item {{activeTab === 'quality' ? 'active' : ''}}" bindtap="switchTab" data-tab="quality">
        <text>质量</text>
      </view>
    </view>
  </view>

  <!-- 对话列表 -->
  <view class="history-list" wx:if="{{historyList.length > 0}}">
    <view class="history-item card" wx:for="{{historyList}}" wx:key="id" bindtap="openChat" data-id="{{item.id}}">
      <view class="item-header">
        <view class="chat-title">
          <text class="title-text">{{item.title}}</text>
          <view class="category-tag tag-{{item.category}}">
            <text>{{item.categoryName}}</text>
          </view>
        </view>
        <view class="chat-actions">
          <button class="action-btn btn btn-ghost btn-small" bindtap="shareChat" data-id="{{item.id}}" catchtap="stopPropagation">
            <text>分享</text>
          </button>
          <button class="action-btn btn btn-ghost btn-small" bindtap="deleteChat" data-id="{{item.id}}" catchtap="stopPropagation">
            <text>删除</text>
          </button>
        </view>
      </view>
      
      <view class="item-content">
        <text class="chat-preview">{{item.lastMessage}}</text>
      </view>
      
      <view class="item-footer">
        <view class="chat-meta">
          <text class="model-name">{{item.modelName}}</text>
          <text class="message-count">{{item.messageCount}}条消息</text>
          <text class="chat-time">{{item.timeAgo}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{historyList.length === 0 && !isLoading}}">
    <view class="empty-content">
      <view class="empty-icon">💬</view>
      <text class="empty-title">暂无对话记录</text>
      <text class="empty-desc">开始您的第一次AI咨询吧</text>
      <button class="start-chat-btn btn btn-primary" bindtap="startNewChat">
        <text>开始咨询</text>
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && historyList.length > 0}}">
    <button class="load-more-btn btn btn-outline" bindtap="loadMore" disabled="{{isLoadingMore}}">
      <text wx:if="{{isLoadingMore}}">加载中...</text>
      <text wx:else>加载更多</text>
    </button>
  </view>
</view>

<!-- 删除确认弹窗 -->
<view class="modal-overlay" wx:if="{{showDeleteModal}}" bindtap="hideDeleteModal">
  <view class="modal-content card" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="title-tertiary">确认删除</text>
    </view>
    <view class="modal-body">
      <text class="text-body">确定要删除这个对话吗？删除后无法恢复。</text>
    </view>
    <view class="modal-footer">
      <button class="btn btn-ghost" bindtap="hideDeleteModal">
        <text>取消</text>
      </button>
      <button class="btn btn-danger" bindtap="confirmDelete">
        <text>删除</text>
      </button>
    </view>
  </view>
</view>
