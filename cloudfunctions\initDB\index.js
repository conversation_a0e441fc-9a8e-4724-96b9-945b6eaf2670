// cloudfunctions/initDB/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 数据库初始化云函数
 * 创建必要的集合和索引
 */
exports.main = async (event, context) => {
  try {
    const results = []

    // 创建用户集合
    try {
      await db.createCollection('users')
      results.push('用户集合创建成功')
    } catch (error) {
      if (error.errCode === -1) {
        results.push('用户集合已存在')
      } else {
        results.push(`用户集合创建失败: ${error.message}`)
      }
    }

    // 创建聊天记录集合
    try {
      await db.createCollection('chat_history')
      results.push('聊天记录集合创建成功')
    } catch (error) {
      if (error.errCode === -1) {
        results.push('聊天记录集合已存在')
      } else {
        results.push(`聊天记录集合创建失败: ${error.message}`)
      }
    }

    // 创建用户偏好集合
    try {
      await db.createCollection('user_preferences')
      results.push('用户偏好集合创建成功')
    } catch (error) {
      if (error.errCode === -1) {
        results.push('用户偏好集合已存在')
      } else {
        results.push(`用户偏好集合创建失败: ${error.message}`)
      }
    }

    // 创建API密钥集合
    try {
      await db.createCollection('api_keys')
      results.push('API密钥集合创建成功')
    } catch (error) {
      if (error.errCode === -1) {
        results.push('API密钥集合已存在')
      } else {
        results.push(`API密钥集合创建失败: ${error.message}`)
      }
    }

    // 创建用户反馈集合
    try {
      await db.createCollection('user_feedback')
      results.push('用户反馈集合创建成功')
    } catch (error) {
      if (error.errCode === -1) {
        results.push('用户反馈集合已存在')
      } else {
        results.push(`用户反馈集合创建失败: ${error.message}`)
      }
    }

    // 创建系统日志集合
    try {
      await db.createCollection('system_logs')
      results.push('系统日志集合创建成功')
    } catch (error) {
      if (error.errCode === -1) {
        results.push('系统日志集合已存在')
      } else {
        results.push(`系统日志集合创建失败: ${error.message}`)
      }
    }

    // 创建索引
    try {
      // 用户集合索引
      await db.collection('users').createIndex({
        keys: { openid: 1 },
        unique: true
      })
      results.push('用户openid索引创建成功')
    } catch (error) {
      results.push(`用户索引创建失败: ${error.message}`)
    }

    try {
      // 聊天记录集合索引
      await db.collection('chat_history').createIndex({
        keys: { openid: 1, createdAt: -1 }
      })
      results.push('聊天记录索引创建成功')
    } catch (error) {
      results.push(`聊天记录索引创建失败: ${error.message}`)
    }

    try {
      // 会话ID索引
      await db.collection('chat_history').createIndex({
        keys: { sessionId: 1 }
      })
      results.push('会话ID索引创建成功')
    } catch (error) {
      results.push(`会话ID索引创建失败: ${error.message}`)
    }

    return {
      success: true,
      message: '数据库初始化完成',
      details: results
    }

  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      message: '数据库初始化失败',
      error: error.message
    }
  }
}
