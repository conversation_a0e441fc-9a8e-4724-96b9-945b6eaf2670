// utils/userPreferences.js
// 用户偏好设置管理

/**
 * 用户偏好管理器
 */
class UserPreferencesManager {
  constructor() {
    this.storageKey = 'userPreferences';
    this.defaultPreferences = {
      // 聊天偏好
      chat: {
        autoSave: true,
        showTimestamp: true,
        enableVoice: false,
        maxHistoryLength: 1000,
        defaultModel: 'gpt-3.5-turbo',
        responseSpeed: 'normal', // fast, normal, detailed
        language: 'zh-CN'
      },
      
      // 显示偏好
      display: {
        theme: 'auto', // light, dark, auto
        fontSize: 'medium', // small, medium, large
        messageAnimation: true,
        compactMode: false,
        showModelInfo: true
      },
      
      // 通知偏好
      notification: {
        enabled: true,
        sound: true,
        vibrate: false,
        newMessage: true,
        systemUpdate: true
      },
      
      // 专业偏好
      professional: {
        field: '医疗器械研发',
        experience: '中级（3-5年）',
        focusAreas: ['产品研发', '注册申报'],
        preferredRegions: ['china'],
        industryRole: 'rd_engineer' // rd_engineer, reg_specialist, sales_manager, quality_manager
      },
      
      // 隐私偏好
      privacy: {
        saveConversations: true,
        shareUsageData: false,
        allowAnalytics: true,
        dataRetentionDays: 90
      },
      
      // 快捷设置
      shortcuts: {
        quickTemplates: [
          '请介绍IVD产品的研发流程',
          '如何进行NMPA注册申报？',
          '市场销售策略有哪些？',
          'ISO13485体系如何建立？'
        ],
        favoriteModels: ['gpt-3.5-turbo', 'gpt-4'],
        recentCategories: ['rd', 'registration']
      }
    };
    
    this.preferences = this.loadPreferences();
  }

  /**
   * 加载用户偏好
   * @returns {Object} 用户偏好对象
   */
  loadPreferences() {
    try {
      const stored = wx.getStorageSync(this.storageKey);
      if (stored) {
        // 合并默认设置和存储的设置
        return this.mergePreferences(this.defaultPreferences, stored);
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error);
    }
    
    return { ...this.defaultPreferences };
  }

  /**
   * 保存用户偏好
   * @param {Object} preferences - 偏好设置
   * @returns {boolean} 保存是否成功
   */
  savePreferences(preferences = null) {
    try {
      const toSave = preferences || this.preferences;
      wx.setStorageSync(this.storageKey, toSave);
      
      if (preferences) {
        this.preferences = preferences;
      }
      
      return true;
    } catch (error) {
      console.error('保存用户偏好失败:', error);
      return false;
    }
  }

  /**
   * 合并偏好设置
   * @param {Object} defaults - 默认设置
   * @param {Object} stored - 存储的设置
   * @returns {Object} 合并后的设置
   */
  mergePreferences(defaults, stored) {
    const merged = { ...defaults };
    
    Object.keys(stored).forEach(key => {
      if (typeof stored[key] === 'object' && !Array.isArray(stored[key])) {
        merged[key] = { ...defaults[key], ...stored[key] };
      } else {
        merged[key] = stored[key];
      }
    });
    
    return merged;
  }

  /**
   * 获取偏好设置
   * @param {string} category - 分类
   * @param {string} key - 键名
   * @returns {any} 偏好值
   */
  getPreference(category, key = null) {
    if (!this.preferences[category]) {
      return null;
    }
    
    if (key) {
      return this.preferences[category][key];
    }
    
    return this.preferences[category];
  }

  /**
   * 设置偏好
   * @param {string} category - 分类
   * @param {string} key - 键名
   * @param {any} value - 值
   * @returns {boolean} 设置是否成功
   */
  setPreference(category, key, value) {
    if (!this.preferences[category]) {
      this.preferences[category] = {};
    }
    
    this.preferences[category][key] = value;
    return this.savePreferences();
  }

  /**
   * 批量设置偏好
   * @param {string} category - 分类
   * @param {Object} values - 值对象
   * @returns {boolean} 设置是否成功
   */
  setPreferences(category, values) {
    if (!this.preferences[category]) {
      this.preferences[category] = {};
    }
    
    Object.assign(this.preferences[category], values);
    return this.savePreferences();
  }

  /**
   * 重置偏好设置
   * @param {string} category - 分类（可选）
   * @returns {boolean} 重置是否成功
   */
  resetPreferences(category = null) {
    if (category) {
      this.preferences[category] = { ...this.defaultPreferences[category] };
    } else {
      this.preferences = { ...this.defaultPreferences };
    }
    
    return this.savePreferences();
  }

  /**
   * 获取聊天相关偏好
   * @returns {Object} 聊天偏好
   */
  getChatPreferences() {
    return this.getPreference('chat');
  }

  /**
   * 获取显示相关偏好
   * @returns {Object} 显示偏好
   */
  getDisplayPreferences() {
    return this.getPreference('display');
  }

  /**
   * 获取专业相关偏好
   * @returns {Object} 专业偏好
   */
  getProfessionalPreferences() {
    return this.getPreference('professional');
  }

  /**
   * 更新最近使用的分类
   * @param {string} category - 分类
   */
  updateRecentCategory(category) {
    const shortcuts = this.getPreference('shortcuts');
    const recentCategories = shortcuts.recentCategories || [];
    
    // 移除已存在的分类
    const index = recentCategories.indexOf(category);
    if (index > -1) {
      recentCategories.splice(index, 1);
    }
    
    // 添加到开头
    recentCategories.unshift(category);
    
    // 限制数量
    if (recentCategories.length > 5) {
      recentCategories.splice(5);
    }
    
    this.setPreference('shortcuts', 'recentCategories', recentCategories);
  }

  /**
   * 添加收藏模板
   * @param {string} template - 模板内容
   */
  addFavoriteTemplate(template) {
    const shortcuts = this.getPreference('shortcuts');
    const quickTemplates = shortcuts.quickTemplates || [];
    
    if (!quickTemplates.includes(template)) {
      quickTemplates.push(template);
      
      // 限制数量
      if (quickTemplates.length > 10) {
        quickTemplates.shift();
      }
      
      this.setPreference('shortcuts', 'quickTemplates', quickTemplates);
    }
  }

  /**
   * 移除收藏模板
   * @param {string} template - 模板内容
   */
  removeFavoriteTemplate(template) {
    const shortcuts = this.getPreference('shortcuts');
    const quickTemplates = shortcuts.quickTemplates || [];
    
    const index = quickTemplates.indexOf(template);
    if (index > -1) {
      quickTemplates.splice(index, 1);
      this.setPreference('shortcuts', 'quickTemplates', quickTemplates);
    }
  }

  /**
   * 更新收藏模型
   * @param {string} modelId - 模型ID
   */
  updateFavoriteModel(modelId) {
    const shortcuts = this.getPreference('shortcuts');
    const favoriteModels = shortcuts.favoriteModels || [];
    
    if (!favoriteModels.includes(modelId)) {
      favoriteModels.push(modelId);
      
      // 限制数量
      if (favoriteModels.length > 5) {
        favoriteModels.shift();
      }
      
      this.setPreference('shortcuts', 'favoriteModels', favoriteModels);
    }
  }

  /**
   * 获取推荐设置
   * @param {string} userType - 用户类型
   * @returns {Object} 推荐设置
   */
  getRecommendedSettings(userType) {
    const recommendations = {
      beginner: {
        chat: {
          responseSpeed: 'detailed',
          defaultModel: 'gpt-3.5-turbo'
        },
        display: {
          showModelInfo: true,
          compactMode: false
        }
      },
      expert: {
        chat: {
          responseSpeed: 'fast',
          defaultModel: 'gpt-4'
        },
        display: {
          showModelInfo: false,
          compactMode: true
        }
      },
      researcher: {
        chat: {
          responseSpeed: 'detailed',
          defaultModel: 'claude-3'
        },
        privacy: {
          saveConversations: true,
          dataRetentionDays: 180
        }
      }
    };
    
    return recommendations[userType] || recommendations.beginner;
  }

  /**
   * 应用推荐设置
   * @param {string} userType - 用户类型
   * @returns {boolean} 应用是否成功
   */
  applyRecommendedSettings(userType) {
    const recommended = this.getRecommendedSettings(userType);
    
    Object.keys(recommended).forEach(category => {
      this.setPreferences(category, recommended[category]);
    });
    
    return true;
  }

  /**
   * 导出偏好设置
   * @returns {string} JSON字符串
   */
  exportPreferences() {
    return JSON.stringify({
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      preferences: this.preferences
    }, null, 2);
  }

  /**
   * 导入偏好设置
   * @param {string} jsonString - JSON字符串
   * @returns {boolean} 导入是否成功
   */
  importPreferences(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      
      if (data.preferences) {
        this.preferences = this.mergePreferences(
          this.defaultPreferences, 
          data.preferences
        );
        return this.savePreferences();
      }
      
      return false;
    } catch (error) {
      console.error('导入偏好设置失败:', error);
      return false;
    }
  }

  /**
   * 获取偏好统计
   * @returns {Object} 统计信息
   */
  getPreferencesStats() {
    return {
      totalCategories: Object.keys(this.preferences).length,
      customizedSettings: this.getCustomizedSettingsCount(),
      lastModified: this.getLastModifiedTime(),
      storageSize: this.getStorageSize()
    };
  }

  /**
   * 获取自定义设置数量
   * @returns {number} 自定义设置数量
   */
  getCustomizedSettingsCount() {
    let count = 0;
    
    Object.keys(this.preferences).forEach(category => {
      Object.keys(this.preferences[category]).forEach(key => {
        if (this.preferences[category][key] !== this.defaultPreferences[category]?.[key]) {
          count++;
        }
      });
    });
    
    return count;
  }

  /**
   * 获取最后修改时间
   * @returns {Date} 最后修改时间
   */
  getLastModifiedTime() {
    try {
      const info = wx.getStorageInfoSync();
      return new Date(); // 微信小程序无法获取具体修改时间
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取存储大小
   * @returns {number} 存储大小（字节）
   */
  getStorageSize() {
    try {
      const data = JSON.stringify(this.preferences);
      return new Blob([data]).size;
    } catch (error) {
      return 0;
    }
  }
}

// 创建全局实例
const userPreferencesManager = new UserPreferencesManager();

module.exports = {
  userPreferencesManager,
  UserPreferencesManager
};
