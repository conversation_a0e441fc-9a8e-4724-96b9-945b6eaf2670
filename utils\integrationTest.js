// utils/integrationTest.js
// 集成测试工具

/**
 * 集成测试管理器
 */
class IntegrationTestManager {
  constructor() {
    this.testSuites = [
      {
        name: 'AI服务集成测试',
        tests: [
          'testAIServiceInitialization',
          'testMultiModelSupport',
          'testErrorHandling',
          'testResponseFormatting'
        ]
      },
      {
        name: 'IVD知识库测试',
        tests: [
          'testKnowledgeRetrieval',
          'testPromptGeneration',
          'testRegulationData',
          'testTemplateSystem'
        ]
      },
      {
        name: '用户体验测试',
        tests: [
          'testUserPreferences',
          'testConversationExport',
          'testPerformanceOptimization',
          'testOfflineMode'
        ]
      },
      {
        name: '合规性测试',
        tests: [
          'testContentCompliance',
          'testPrivacyProtection',
          'testDataSecurity',
          'testWeChatCompliance'
        ]
      }
    ];
    
    this.testResults = [];
  }

  /**
   * 运行所有集成测试
   * @returns {Promise<Object>} 测试结果
   */
  async runAllTests() {
    console.log('开始运行集成测试...');
    
    const results = {
      timestamp: new Date().toISOString(),
      totalSuites: this.testSuites.length,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      suiteResults: [],
      summary: {},
      recommendations: []
    };

    for (const suite of this.testSuites) {
      const suiteResult = await this.runTestSuite(suite);
      results.suiteResults.push(suiteResult);
      results.totalTests += suiteResult.totalTests;
      results.passedTests += suiteResult.passedTests;
      results.failedTests += suiteResult.failedTests;
    }

    // 生成测试摘要
    results.summary = {
      successRate: Math.round((results.passedTests / results.totalTests) * 100),
      overallStatus: results.failedTests === 0 ? 'PASS' : 'FAIL',
      criticalIssues: this.identifyCriticalIssues(results.suiteResults)
    };

    // 生成建议
    results.recommendations = this.generateRecommendations(results);

    this.testResults.push(results);
    return results;
  }

  /**
   * 运行测试套件
   * @param {Object} suite - 测试套件
   * @returns {Promise<Object>} 套件测试结果
   */
  async runTestSuite(suite) {
    console.log(`运行测试套件: ${suite.name}`);
    
    const suiteResult = {
      name: suite.name,
      totalTests: suite.tests.length,
      passedTests: 0,
      failedTests: 0,
      testResults: []
    };

    for (const testName of suite.tests) {
      try {
        const testResult = await this[testName]();
        suiteResult.testResults.push({
          name: testName,
          status: 'PASS',
          result: testResult,
          duration: testResult.duration || 0
        });
        suiteResult.passedTests++;
      } catch (error) {
        suiteResult.testResults.push({
          name: testName,
          status: 'FAIL',
          error: error.message,
          duration: 0
        });
        suiteResult.failedTests++;
      }
    }

    return suiteResult;
  }

  // AI服务集成测试
  async testAIServiceInitialization() {
    const startTime = Date.now();
    const { aiService } = require('./aiService');
    
    if (!aiService) {
      throw new Error('AI服务未正确初始化');
    }

    // 测试服务状态检查
    const status = await aiService.getAllServiceStatus();
    
    return {
      duration: Date.now() - startTime,
      serviceStatus: status,
      message: 'AI服务初始化成功'
    };
  }

  async testMultiModelSupport() {
    const startTime = Date.now();
    const { aiService } = require('./aiService');
    
    const models = ['gpt-3.5-turbo', 'gpt-4', 'claude-3', 'gemini-pro'];
    const results = {};
    
    for (const model of models) {
      try {
        const response = await aiService.sendMessage('测试消息', model, { maxTokens: 10 });
        results[model] = 'success';
      } catch (error) {
        results[model] = 'failed';
      }
    }

    return {
      duration: Date.now() - startTime,
      modelResults: results,
      message: '多模型支持测试完成'
    };
  }

  async testErrorHandling() {
    const startTime = Date.now();
    const { errorHandler } = require('./apiUtils');
    
    // 测试错误处理
    const testError = new Error('测试错误');
    const result = errorHandler.handleError(testError, '集成测试');
    
    if (!result.userMessage) {
      throw new Error('错误处理器未返回用户友好消息');
    }

    return {
      duration: Date.now() - startTime,
      errorHandled: true,
      userMessage: result.userMessage,
      message: '错误处理测试通过'
    };
  }

  async testResponseFormatting() {
    const startTime = Date.now();
    const { ivdPromptManager } = require('./ivdPrompts');
    
    const testMessage = '请介绍IVD产品研发流程';
    const enhancedMessage = ivdPromptManager.enhanceUserMessage(testMessage, 'rd');
    
    if (!enhancedMessage.includes(testMessage)) {
      throw new Error('消息增强失败');
    }

    return {
      duration: Date.now() - startTime,
      originalMessage: testMessage,
      enhancedMessage: enhancedMessage.substring(0, 100) + '...',
      message: '响应格式化测试通过'
    };
  }

  // IVD知识库测试
  async testKnowledgeRetrieval() {
    const startTime = Date.now();
    const { ivdKnowledge } = require('./ivdKnowledge');
    
    const searchResults = ivdKnowledge.searchKnowledge('产品研发');
    
    if (searchResults.length === 0) {
      throw new Error('知识库搜索未返回结果');
    }

    return {
      duration: Date.now() - startTime,
      searchResults: searchResults.length,
      message: '知识库检索测试通过'
    };
  }

  async testPromptGeneration() {
    const startTime = Date.now();
    const { ivdPromptManager } = require('./ivdPrompts');
    
    const systemPrompt = ivdPromptManager.getSystemPrompt('rd', 'planning');
    
    if (!systemPrompt || systemPrompt.length < 100) {
      throw new Error('系统提示词生成失败');
    }

    return {
      duration: Date.now() - startTime,
      promptLength: systemPrompt.length,
      message: '提示词生成测试通过'
    };
  }

  async testRegulationData() {
    const startTime = Date.now();
    const { ivdRegulationManager } = require('./ivdRegulations');
    
    const chinaRegulation = ivdRegulationManager.getRegulation('china');
    
    if (!chinaRegulation || !chinaRegulation.classification) {
      throw new Error('法规数据获取失败');
    }

    return {
      duration: Date.now() - startTime,
      regulationData: Object.keys(chinaRegulation.classification).length,
      message: '法规数据测试通过'
    };
  }

  async testTemplateSystem() {
    const startTime = Date.now();
    const { consultationTemplateManager } = require('./consultationTemplates');
    
    const rdTemplates = consultationTemplateManager.getCategoryTemplates('rd');
    
    if (!rdTemplates || rdTemplates.templates.length === 0) {
      throw new Error('模板系统获取失败');
    }

    return {
      duration: Date.now() - startTime,
      templateCount: rdTemplates.templates.length,
      message: '模板系统测试通过'
    };
  }

  // 用户体验测试
  async testUserPreferences() {
    const startTime = Date.now();
    const { userPreferencesManager } = require('./userPreferences');
    
    // 测试偏好设置
    const testValue = 'test_value_' + Date.now();
    userPreferencesManager.setPreference('test', 'testKey', testValue);
    const retrievedValue = userPreferencesManager.getPreference('test', 'testKey');
    
    if (retrievedValue !== testValue) {
      throw new Error('用户偏好设置失败');
    }

    return {
      duration: Date.now() - startTime,
      preferencesWorking: true,
      message: '用户偏好测试通过'
    };
  }

  async testConversationExport() {
    const startTime = Date.now();
    const { conversationExporter } = require('./exportUtils');
    
    const testMessages = [
      { id: '1', role: 'user', content: '测试消息1', timestamp: Date.now() },
      { id: '2', role: 'assistant', content: '测试回复1', timestamp: Date.now() }
    ];
    
    const exportedText = conversationExporter.exportToText(testMessages);
    
    if (!exportedText.includes('测试消息1')) {
      throw new Error('对话导出失败');
    }

    return {
      duration: Date.now() - startTime,
      exportLength: exportedText.length,
      message: '对话导出测试通过'
    };
  }

  async testPerformanceOptimization() {
    const startTime = Date.now();
    const { performanceManager } = require('./performance');
    
    const report = performanceManager.getPerformanceReport();
    
    if (!report || typeof report.score === 'undefined') {
      throw new Error('性能优化模块异常');
    }

    return {
      duration: Date.now() - startTime,
      performanceScore: report.score,
      message: '性能优化测试通过'
    };
  }

  async testOfflineMode() {
    const startTime = Date.now();
    const app = getApp();
    
    // 模拟离线模式
    const originalOfflineMode = app.globalData.offlineMode;
    app.globalData.offlineMode = true;
    
    // 测试离线功能
    const offlineModeActive = app.globalData.offlineMode;
    
    // 恢复原始状态
    app.globalData.offlineMode = originalOfflineMode;
    
    if (!offlineModeActive) {
      throw new Error('离线模式设置失败');
    }

    return {
      duration: Date.now() - startTime,
      offlineModeWorking: true,
      message: '离线模式测试通过'
    };
  }

  // 合规性测试
  async testContentCompliance() {
    const startTime = Date.now();
    const { wechatComplianceChecker } = require('./wechatCompliance');
    
    const complianceResult = wechatComplianceChecker.performFullCheck();
    
    if (!complianceResult || !complianceResult.overall) {
      throw new Error('合规性检查失败');
    }

    return {
      duration: Date.now() - startTime,
      complianceStatus: complianceResult.overall,
      issueCount: complianceResult.issues.length,
      message: '内容合规测试完成'
    };
  }

  async testPrivacyProtection() {
    const startTime = Date.now();
    
    // 检查隐私设置
    const { userPreferencesManager } = require('./userPreferences');
    const privacySettings = userPreferencesManager.getPreference('privacy');
    
    if (!privacySettings) {
      throw new Error('隐私设置未初始化');
    }

    return {
      duration: Date.now() - startTime,
      privacySettingsExist: true,
      message: '隐私保护测试通过'
    };
  }

  async testDataSecurity() {
    const startTime = Date.now();
    
    // 检查API密钥安全
    const app = getApp();
    const apiKeys = app.globalData.apiKeys;
    
    // 确保API密钥不为空对象
    if (typeof apiKeys !== 'object') {
      throw new Error('API密钥存储结构异常');
    }

    return {
      duration: Date.now() - startTime,
      apiKeysSecure: true,
      message: '数据安全测试通过'
    };
  }

  async testWeChatCompliance() {
    const startTime = Date.now();
    
    // 检查微信小程序基本合规性
    try {
      const systemInfo = wx.getSystemInfoSync();
      if (!systemInfo.platform) {
        throw new Error('微信环境检查失败');
      }
    } catch (error) {
      throw new Error('微信API调用失败');
    }

    return {
      duration: Date.now() - startTime,
      wechatCompliant: true,
      message: '微信合规测试通过'
    };
  }

  /**
   * 识别关键问题
   * @param {Array} suiteResults - 测试套件结果
   * @returns {Array} 关键问题列表
   */
  identifyCriticalIssues(suiteResults) {
    const criticalIssues = [];
    
    suiteResults.forEach(suite => {
      suite.testResults.forEach(test => {
        if (test.status === 'FAIL') {
          criticalIssues.push({
            suite: suite.name,
            test: test.name,
            error: test.error
          });
        }
      });
    });
    
    return criticalIssues;
  }

  /**
   * 生成测试建议
   * @param {Object} results - 测试结果
   * @returns {Array} 建议列表
   */
  generateRecommendations(results) {
    const recommendations = [];
    
    if (results.summary.successRate < 100) {
      recommendations.push('修复失败的测试用例');
    }
    
    if (results.summary.successRate >= 90) {
      recommendations.push('测试通过率良好，可以考虑部署');
    } else if (results.summary.successRate >= 70) {
      recommendations.push('测试通过率一般，建议修复主要问题后再部署');
    } else {
      recommendations.push('测试通过率较低，不建议部署');
    }
    
    if (results.summary.criticalIssues.length > 0) {
      recommendations.push('优先处理关键问题');
    }
    
    recommendations.push('定期运行集成测试确保代码质量');
    
    return recommendations;
  }

  /**
   * 导出测试报告
   * @returns {string} JSON格式的测试报告
   */
  exportTestReport() {
    const latestResult = this.testResults[this.testResults.length - 1];
    
    if (!latestResult) {
      return JSON.stringify({
        message: '尚未运行测试',
        recommendation: '请先运行集成测试'
      }, null, 2);
    }
    
    return JSON.stringify(latestResult, null, 2);
  }
}

// 创建全局实例
const integrationTestManager = new IntegrationTestManager();

module.exports = {
  integrationTestManager,
  IntegrationTestManager
};
