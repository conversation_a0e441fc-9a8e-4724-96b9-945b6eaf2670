// utils/apiUtils.js
// API工具类和错误处理

/**
 * API请求管理器
 */
class APIManager {
  constructor() {
    this.requestQueue = [];
    this.maxRetries = 3;
    this.retryDelay = 1000;
    this.timeout = 30000;
  }

  /**
   * 发送HTTP请求
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(options) {
    const requestId = this.generateRequestId();
    
    try {
      // 添加到请求队列
      this.requestQueue.push({
        id: requestId,
        options,
        timestamp: Date.now()
      });

      const result = await this.executeRequest(options);
      
      // 从队列中移除
      this.removeFromQueue(requestId);
      
      return result;
    } catch (error) {
      this.removeFromQueue(requestId);
      throw error;
    }
  }

  /**
   * 执行请求
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  executeRequest(options) {
    return new Promise((resolve, reject) => {
      const requestOptions = {
        url: options.url,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        timeout: options.timeout || this.timeout,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            reject(new APIError(res.statusCode, res.data.message || '请求失败', res.data));
          }
        },
        fail: (error) => {
          reject(new NetworkError(error.errMsg || '网络请求失败'));
        }
      };

      wx.request(requestOptions);
    });
  }

  /**
   * 带重试的请求
   * @param {Object} options - 请求选项
   * @param {number} retries - 重试次数
   * @returns {Promise} 请求结果
   */
  async requestWithRetry(options, retries = this.maxRetries) {
    try {
      return await this.request(options);
    } catch (error) {
      if (retries > 0 && this.shouldRetry(error)) {
        console.log(`请求失败，${this.retryDelay}ms后重试，剩余重试次数：${retries - 1}`);
        await this.delay(this.retryDelay);
        return this.requestWithRetry(options, retries - 1);
      }
      throw error;
    }
  }

  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否重试
   */
  shouldRetry(error) {
    if (error instanceof NetworkError) return true;
    if (error instanceof APIError) {
      // 5xx错误可以重试
      return error.statusCode >= 500;
    }
    return false;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成请求ID
   * @returns {string} 请求ID
   */
  generateRequestId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 从队列中移除请求
   * @param {string} requestId - 请求ID
   */
  removeFromQueue(requestId) {
    this.requestQueue = this.requestQueue.filter(req => req.id !== requestId);
  }

  /**
   * 取消所有请求
   */
  cancelAllRequests() {
    this.requestQueue.forEach(req => {
      // 微信小程序没有直接取消请求的API，这里只是清空队列
      console.log(`取消请求: ${req.id}`);
    });
    this.requestQueue = [];
  }

  /**
   * 获取请求统计
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      activeRequests: this.requestQueue.length,
      oldestRequest: this.requestQueue.length > 0 ? 
        Math.min(...this.requestQueue.map(req => req.timestamp)) : null
    };
  }
}

/**
 * API错误类
 */
class APIError extends Error {
  constructor(statusCode, message, data = null) {
    super(message);
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.data = data;
  }

  toString() {
    return `APIError: ${this.statusCode} - ${this.message}`;
  }
}

/**
 * 网络错误类
 */
class NetworkError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NetworkError';
  }

  toString() {
    return `NetworkError: ${this.message}`;
  }
}

/**
 * 错误处理器
 */
class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.maxLogSize = 100;
  }

  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleError(error, context = '') {
    const errorInfo = {
      type: error.name || 'UnknownError',
      message: error.message,
      context,
      timestamp: Date.now(),
      stack: error.stack
    };

    // 记录错误
    this.logError(errorInfo);

    // 根据错误类型返回用户友好的消息
    const userMessage = this.getUserFriendlyMessage(error);
    
    return {
      success: false,
      error: errorInfo,
      userMessage,
      shouldRetry: this.shouldRetry(error)
    };
  }

  /**
   * 记录错误
   * @param {Object} errorInfo - 错误信息
   */
  logError(errorInfo) {
    this.errorLog.push(errorInfo);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize);
    }

    // 输出到控制台
    console.error('Error logged:', errorInfo);
  }

  /**
   * 获取用户友好的错误消息
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的消息
   */
  getUserFriendlyMessage(error) {
    if (error instanceof NetworkError) {
      return '网络连接异常，请检查网络设置后重试';
    }
    
    if (error instanceof APIError) {
      switch (error.statusCode) {
        case 400:
          return '请求参数有误，请检查输入内容';
        case 401:
          return 'API密钥无效，请检查配置';
        case 403:
          return '访问被拒绝，请检查权限设置';
        case 404:
          return '请求的服务不存在';
        case 429:
          return '请求过于频繁，请稍后再试';
        case 500:
          return '服务器内部错误，请稍后重试';
        case 502:
        case 503:
        case 504:
          return '服务暂时不可用，请稍后重试';
        default:
          return `服务异常（${error.statusCode}），请稍后重试`;
      }
    }

    return '发生未知错误，请稍后重试';
  }

  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    if (error instanceof NetworkError) return true;
    
    if (error instanceof APIError) {
      // 5xx错误和429错误可以重试
      return error.statusCode >= 500 || error.statusCode === 429;
    }
    
    return false;
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorLog.length,
      byType: {},
      recent: this.errorLog.slice(-10)
    };

    this.errorLog.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
  }
}

/**
 * 响应缓存管理器
 */
class ResponseCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 50;
    this.defaultTTL = 5 * 60 * 1000; // 5分钟
  }

  /**
   * 生成缓存键
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @returns {string} 缓存键
   */
  generateKey(url, data = {}) {
    return `${url}_${JSON.stringify(data)}`;
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存值
   */
  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 生存时间（毫秒）
   */
  set(key, data, ttl = this.defaultTTL) {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    this.cache.delete(key);
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局实例
const apiManager = new APIManager();
const errorHandler = new ErrorHandler();
const responseCache = new ResponseCache();

module.exports = {
  apiManager,
  errorHandler,
  responseCache,
  APIManager,
  ErrorHandler,
  ResponseCache,
  APIError,
  NetworkError
};
