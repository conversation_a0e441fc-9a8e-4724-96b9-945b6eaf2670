// utils/ivdKnowledge.js
// IVD领域知识库

/**
 * IVD知识库管理器
 */
class IVDKnowledgeBase {
  constructor() {
    this.knowledgeBase = {
      // 产品研发流程
      rd: {
        title: '产品研发流程',
        description: 'IVD产品从概念到上市的完整研发流程',
        categories: {
          planning: {
            title: '项目规划',
            topics: [
              '市场需求分析',
              '技术可行性评估',
              '竞争对手分析',
              '项目立项评审',
              '资源配置规划'
            ],
            keywords: ['规划', '立项', '评估', '分析', '可行性']
          },
          design: {
            title: '设计开发',
            topics: [
              '产品设计输入',
              '设计方案制定',
              '原型开发',
              '设计验证',
              '设计确认'
            ],
            keywords: ['设计', '开发', '原型', '验证', '确认']
          },
          testing: {
            title: '测试验证',
            topics: [
              '分析性能验证',
              '稳定性研究',
              '参考区间建立',
              '临床试验设计',
              '质量控制体系'
            ],
            keywords: ['测试', '验证', '性能', '稳定性', '临床']
          }
        }
      },

      // 注册申报
      registration: {
        title: '注册申报要点',
        description: 'IVD产品注册申报的法规要求和流程指导',
        categories: {
          classification: {
            title: '产品分类',
            topics: [
              'I类产品备案',
              'II类产品注册',
              'III类产品注册',
              '分类界定原则',
              '风险等级评估'
            ],
            keywords: ['分类', '备案', '注册', '风险', '等级']
          },
          documentation: {
            title: '技术文档',
            topics: [
              '产品技术要求',
              '研究资料汇总',
              '临床评价资料',
              '质量管理体系文件',
              '标签说明书'
            ],
            keywords: ['文档', '资料', '技术要求', '说明书', '标签']
          },
          process: {
            title: '申报流程',
            topics: [
              'NMPA申报流程',
              'CE认证流程',
              'FDA 510(k)流程',
              '技术审评要点',
              '现场检查准备'
            ],
            keywords: ['流程', '申报', 'NMPA', 'CE', 'FDA', '审评']
          }
        }
      },

      // 市场销售策略
      sales: {
        title: '市场销售策略',
        description: 'IVD产品市场推广和销售策略制定',
        categories: {
          market: {
            title: '市场分析',
            topics: [
              '目标市场定位',
              '客户需求分析',
              '竞争格局分析',
              '市场容量评估',
              '价格策略制定'
            ],
            keywords: ['市场', '定位', '客户', '竞争', '价格']
          },
          channels: {
            title: '渠道建设',
            topics: [
              '直销团队建设',
              '经销商网络',
              '代理商管理',
              '线上销售平台',
              '国际市场拓展'
            ],
            keywords: ['渠道', '直销', '经销商', '代理', '平台']
          },
          support: {
            title: '销售支持',
            topics: [
              '技术支持体系',
              '培训服务',
              '售后服务',
              '客户关系管理',
              '市场推广活动'
            ],
            keywords: ['支持', '培训', '售后', '客户关系', '推广']
          }
        }
      },

      // 质量管理
      quality: {
        title: '质量管理体系',
        description: 'ISO13485质量管理体系建立和维护',
        categories: {
          system: {
            title: '体系建立',
            topics: [
              'ISO13485标准要求',
              '质量手册编制',
              '程序文件制定',
              '作业指导书',
              '记录表单设计'
            ],
            keywords: ['ISO13485', '质量手册', '程序', '作业指导', '记录']
          },
          implementation: {
            title: '体系实施',
            topics: [
              '组织架构设计',
              '职责权限分配',
              '培训计划制定',
              '内部审核',
              '管理评审'
            ],
            keywords: ['组织', '职责', '培训', '内审', '管理评审']
          },
          improvement: {
            title: '持续改进',
            topics: [
              '不合格品控制',
              '纠正预防措施',
              '风险管理',
              '变更控制',
              '供应商管理'
            ],
            keywords: ['改进', '不合格', '纠正', '风险', '变更', '供应商']
          }
        }
      }
    };

    // 常见问题模板
    this.faqTemplates = {
      rd: [
        '如何进行IVD产品的需求分析？',
        '产品设计开发的关键控制点有哪些？',
        '临床试验设计需要注意什么？',
        '如何建立产品的质量控制体系？'
      ],
      registration: [
        'II类IVD产品注册需要哪些资料？',
        'NMPA技术审评一般需要多长时间？',
        '如何准备临床评价资料？',
        'CE认证和NMPA注册有什么区别？'
      ],
      sales: [
        '如何制定IVD产品的定价策略？',
        '医院客户开发的关键要素是什么？',
        '如何建立有效的经销商网络？',
        '国际市场拓展需要注意什么？'
      ],
      quality: [
        '如何建立ISO13485质量管理体系？',
        '内部审核应该如何开展？',
        '风险管理在IVD行业的应用？',
        '供应商管理的关键要点？'
      ]
    };
  }

  /**
   * 根据关键词搜索相关知识
   * @param {string} query - 搜索关键词
   * @param {string} category - 分类（可选）
   * @returns {Array} 搜索结果
   */
  searchKnowledge(query, category = null) {
    const results = [];
    const searchTerms = query.toLowerCase().split(/\s+/);

    const searchCategories = category ? [category] : Object.keys(this.knowledgeBase);

    for (const cat of searchCategories) {
      const knowledge = this.knowledgeBase[cat];
      if (!knowledge) continue;

      for (const [subCat, subKnowledge] of Object.entries(knowledge.categories)) {
        // 检查关键词匹配
        const keywordMatch = subKnowledge.keywords.some(keyword =>
          searchTerms.some(term => keyword.includes(term) || term.includes(keyword))
        );

        // 检查主题匹配
        const topicMatch = subKnowledge.topics.some(topic =>
          searchTerms.some(term => topic.toLowerCase().includes(term))
        );

        if (keywordMatch || topicMatch) {
          results.push({
            category: cat,
            subCategory: subCat,
            title: subKnowledge.title,
            topics: subKnowledge.topics,
            relevance: this.calculateRelevance(query, subKnowledge)
          });
        }
      }
    }

    // 按相关性排序
    return results.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * 计算相关性得分
   * @param {string} query - 查询词
   * @param {Object} knowledge - 知识条目
   * @returns {number} 相关性得分
   */
  calculateRelevance(query, knowledge) {
    let score = 0;
    const queryLower = query.toLowerCase();

    // 关键词匹配得分
    knowledge.keywords.forEach(keyword => {
      if (queryLower.includes(keyword) || keyword.includes(queryLower)) {
        score += 2;
      }
    });

    // 主题匹配得分
    knowledge.topics.forEach(topic => {
      if (topic.toLowerCase().includes(queryLower)) {
        score += 1;
      }
    });

    return score;
  }

  /**
   * 获取分类的FAQ
   * @param {string} category - 分类
   * @returns {Array} FAQ列表
   */
  getFAQ(category) {
    return this.faqTemplates[category] || [];
  }

  /**
   * 获取所有分类
   * @returns {Array} 分类列表
   */
  getCategories() {
    return Object.keys(this.knowledgeBase).map(key => ({
      id: key,
      title: this.knowledgeBase[key].title,
      description: this.knowledgeBase[key].description
    }));
  }

  /**
   * 获取分类详情
   * @param {string} category - 分类ID
   * @returns {Object} 分类详情
   */
  getCategoryDetail(category) {
    return this.knowledgeBase[category] || null;
  }

  /**
   * 生成智能建议
   * @param {string} userInput - 用户输入
   * @param {string} category - 分类
   * @returns {Array} 建议列表
   */
  generateSuggestions(userInput, category = null) {
    const suggestions = [];
    
    // 基于搜索结果生成建议
    const searchResults = this.searchKnowledge(userInput, category);
    
    searchResults.slice(0, 3).forEach(result => {
      result.topics.slice(0, 2).forEach(topic => {
        suggestions.push(`请详细介绍${topic}的相关内容`);
      });
    });

    // 如果没有搜索结果，返回分类相关的FAQ
    if (suggestions.length === 0 && category) {
      const faq = this.getFAQ(category);
      suggestions.push(...faq.slice(0, 4));
    }

    return suggestions;
  }

  /**
   * 获取上下文相关的提示
   * @param {string} category - 分类
   * @param {string} subCategory - 子分类
   * @returns {string} 上下文提示
   */
  getContextPrompt(category, subCategory = null) {
    const knowledge = this.knowledgeBase[category];
    if (!knowledge) return '';

    let prompt = `作为IVD行业专家，专注于${knowledge.title}领域。`;

    if (subCategory && knowledge.categories[subCategory]) {
      const subKnowledge = knowledge.categories[subCategory];
      prompt += `特别关注${subKnowledge.title}相关的问题，包括：${subKnowledge.topics.join('、')}等方面。`;
    }

    return prompt;
  }

  /**
   * 验证用户输入的专业性
   * @param {string} input - 用户输入
   * @returns {Object} 验证结果
   */
  validateProfessionalInput(input) {
    const professionalTerms = [
      'IVD', '体外诊断', 'NMPA', 'CE', 'FDA', 'ISO13485',
      '注册', '申报', '临床试验', '质量管理', '风险管理',
      '技术要求', '说明书', '标签', '验证', '确认'
    ];

    const foundTerms = professionalTerms.filter(term => 
      input.toLowerCase().includes(term.toLowerCase())
    );

    return {
      isProfessional: foundTerms.length > 0,
      foundTerms,
      confidence: foundTerms.length / professionalTerms.length
    };
  }
}

// 创建全局知识库实例
const ivdKnowledge = new IVDKnowledgeBase();

module.exports = {
  ivdKnowledge,
  IVDKnowledgeBase
};
