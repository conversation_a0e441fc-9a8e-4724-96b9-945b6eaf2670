{"name": "ivd-intelligent-advisor", "version": "2.0.0", "description": "IVD智能顾问微信小程序 - 专业的医疗器械研发、注册、销售咨询助手", "main": "app.js", "scripts": {"fix-logger": "node scripts/fix-logger.js", "dev": "echo '请在微信开发者工具中打开项目'", "build": "echo '请在微信开发者工具中构建项目'", "lint": "echo '代码检查功能'", "test": "echo '测试功能'"}, "keywords": ["微信小程序", "IVD", "医疗器械", "AI咨询", "人工智能", "研发", "注册申报", "销售策略"], "author": "IVD智能顾问团队", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/ivd-intelligent-advisor.git"}, "bugs": {"url": "https://github.com/your-username/ivd-intelligent-advisor/issues"}, "homepage": "https://github.com/your-username/ivd-intelligent-advisor#readme", "devDependencies": {"miniprogram-api-typings": "^3.12.0"}, "engines": {"node": ">=14.0.0"}, "miniprogram": {"cloudfunctionRoot": "cloudfunctions/", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "appid": "wx40de5ae4b1c122b6", "projectname": "IVD智能顾问", "libVersion": "2.32.0", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}}}