// app.js
const { logger, SafeStorage, ErrorBoundary } = require('./utils/logger')

App({
  globalData: {
    userInfo: null,
    selectedAIModel: 'deepseek-v3-0324',
    apiKeys: {
      deepseek: '',
      alibaba: ''
    },
    chatHistory: [],
    sessionStart: Date.now(),
    offlineMode: false,
    ivdKnowledgeBase: {
      rd: {
        title: '产品研发流程',
        topics: [
          '需求分析与市场调研',
          '技术可行性评估',
          '产品设计与开发',
          '原型制作与测试',
          '临床试验设计',
          '质量管理体系建立'
        ]
      },
      registration: {
        title: '注册申报要点',
        topics: [
          'NMPA注册流程',
          'CE认证要求',
          'FDA 510(k)申报',
          '临床试验要求',
          '技术文档准备',
          '质量管理体系认证'
        ]
      },
      sales: {
        title: '市场销售策略',
        topics: [
          '市场定位与分析',
          '渠道建设策略',
          '价格策略制定',
          '客户关系管理',
          '竞争对手分析',
          '销售团队建设'
        ]
      }
    }
  },

  onLaunch() {
    // 小程序启动时执行
    logger.info('IVD智能顾问小程序启动');

    // 记录启动时间
    this.globalData.sessionStart = Date.now();

    // 安全执行初始化流程
    ErrorBoundary.safeExecute(() => {
      // 初始化云开发（最优先）
      this.initializeCloud();

      // 检查本地存储的用户设置
      this.loadUserSettings();

      // 检查登录状态
      this.checkLoginStatus();

      // 初始化性能监控
      this.initializePerformanceMonitoring();

      // 检查更新
      this.checkForUpdate();

      // 执行启动时的合规检查
      this.performStartupChecks();
    });
  },

  onShow() {
    // 小程序显示时执行
    logger.info('小程序显示');
  },

  onHide() {
    // 小程序隐藏时执行
    logger.info('小程序隐藏');
    ErrorBoundary.safeExecute(() => {
      this.saveUserSettings();
    });
  },

  loadUserSettings() {
    try {
      const settings = SafeStorage.getSync('userSettings');
      if (settings) {
        this.globalData.selectedAIModel = settings.selectedAIModel || 'deepseek-v3-0324';
        this.globalData.apiKeys = { ...this.globalData.apiKeys, ...(settings.apiKeys || {}) };
      }

      const chatHistory = SafeStorage.getSync('chatHistory', []);
      if (chatHistory && Array.isArray(chatHistory)) {
        this.globalData.chatHistory = chatHistory;
      }

      logger.info('用户设置加载成功');
    } catch (e) {
      logger.error('加载用户设置失败:', e);
    }
  },

  saveUserSettings() {
    try {
      const settings = {
        selectedAIModel: this.globalData.selectedAIModel,
        apiKeys: this.globalData.apiKeys,
        chatHistory: this.globalData.chatHistory
      };

      // 保存到本地
      const saveSuccess = SafeStorage.setSync('userSettings', settings);
      const chatSaveSuccess = SafeStorage.setSync('chatHistory', this.globalData.chatHistory);

      if (saveSuccess && chatSaveSuccess) {
        logger.info('用户设置保存成功');

        // 如果用户已登录，同步到云端
        if (this.globalData.userInfo && this.globalData.userInfo.openid) {
          ErrorBoundary.safeExecuteAsync(() => this.syncUserSettingsToCloud(settings));
        }
      } else {
        logger.warn('用户设置保存部分失败');
      }
    } catch (e) {
      logger.error('保存用户设置失败:', e);
    }
  },

  // 同步用户设置到云端
  async syncUserSettingsToCloud(settings) {
    try {
      const { cloudSyncManager } = require('./utils/cloudSync');

      // 同步偏好设置
      const { userPreferencesManager } = require('./utils/userPreferences');
      cloudSyncManager.addSyncTask({
        type: 'preferences',
        data: userPreferencesManager.preferences
      });

      // 同步API密钥
      if (settings.apiKeys) {
        cloudSyncManager.addSyncTask({
          type: 'apiKeys',
          data: settings.apiKeys
        });
      }

      // 同步聊天记录（最近的）
      if (settings.chatHistory && settings.chatHistory.length > 0) {
        cloudSyncManager.addSyncTask({
          type: 'chatHistory',
          data: {
            messages: settings.chatHistory.slice(-20), // 最近20条
            sessionId: cloudSyncManager.generateSessionId()
          }
        });
      }

    } catch (error) {
      console.error('云端同步失败:', error);
    }
  },

  // 初始化云开发
  initializeCloud() {
    try {
      // 检查云开发是否可用
      if (!wx.cloud) {
        logger.error('云开发不可用，请升级基础库到2.2.3+');
        return;
      }

      // 初始化云开发
      wx.cloud.init({
        env: 'cloudbase-7g8nxwah43c62b19', // 请替换为实际的云开发环境ID
        traceUser: true
      });

      logger.info('云开发初始化完成');

      // 初始化云同步管理器
      try {
        const { cloudSyncManager } = require('./utils/cloudSync');
        cloudSyncManager.init();
        logger.info('云同步管理器初始化完成');
      } catch (syncError) {
        logger.warn('云同步管理器初始化失败:', syncError);
      }

    } catch (error) {
      logger.error('云开发初始化失败:', error);
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    try {
      const userInfo = SafeStorage.getSync('userInfo');
      if (userInfo && userInfo.openid) {
        this.globalData.userInfo = userInfo;
        logger.info('用户已登录:', userInfo.nickName);

        // 如果已登录，从云端同步数据
        ErrorBoundary.safeExecuteAsync(() => this.syncDataFromCloud());
      } else {
        logger.info('用户未登录');
        // 跳转到登录页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }, 1000);
      }
    } catch (error) {
      logger.error('检查登录状态失败:', error);
    }
  },

  // 从云端同步数据
  async syncDataFromCloud() {
    try {
      const { cloudSyncManager } = require('./utils/cloudSync');

      // 恢复用户偏好设置
      const preferencesResult = await cloudSyncManager.getUserPreferences();
      if (preferencesResult.success) {
        const { userPreferencesManager } = require('./utils/userPreferences');
        userPreferencesManager.preferences = {
          ...userPreferencesManager.preferences,
          ...preferencesResult.data
        };
      }

      // 恢复API密钥
      const apiKeysResult = await cloudSyncManager.getAPIKeys();
      if (apiKeysResult.success) {
        this.globalData.apiKeys = {
          ...this.globalData.apiKeys,
          ...apiKeysResult.data
        };
      }

      logger.info('云端数据同步完成');
    } catch (error) {
      logger.error('云端数据同步失败:', error);
    }
  },

  // 初始化性能监控
  initializePerformanceMonitoring() {
    try {
      const { performanceManager } = require('./utils/performance');
      performanceManager.startMonitoring(10000); // 每10秒收集一次性能数据
      logger.info('性能监控已启动');
    } catch (error) {
      logger.error('性能监控启动失败:', error);
    }
  },

  // 执行启动时检查
  performStartupChecks() {
    try {
      // 检查存储空间
      const storageInfo = SafeStorage.getInfo();
      if (storageInfo.currentSize > storageInfo.limitSize * 0.9) {
        logger.warn('存储空间不足，建议清理数据');
      }

      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            this.globalData.offlineMode = true;
            logger.info('网络不可用，启用离线模式');
          }
        }
      });
    } catch (error) {
      logger.error('启动检查失败:', error);
    }
  },

  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          logger.info('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        logger.error('新版本下载失败');
      });
    }
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo);
      } else {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: (res) => {
            this.globalData.userInfo = res.userInfo;
            resolve(res.userInfo);
          },
          fail: reject
        });
      }
    });
  },

  // 添加聊天记录
  addChatMessage(message) {
    this.globalData.chatHistory.push({
      ...message,
      timestamp: Date.now(),
      id: this.generateId()
    });
    
    // 限制历史记录数量
    if (this.globalData.chatHistory.length > 1000) {
      this.globalData.chatHistory = this.globalData.chatHistory.slice(-500);
    }
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // 获取IVD知识库
  getIVDKnowledge(category) {
    return this.globalData.ivdKnowledgeBase[category] || null;
  }
});
