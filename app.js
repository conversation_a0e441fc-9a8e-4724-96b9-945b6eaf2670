// app.js
App({
  globalData: {
    userInfo: null,
    selectedAIModel: 'gpt-3.5-turbo',
    apiKeys: {
      openai: '',
      anthropic: '',
      google: ''
    },
    chatHistory: [],
    sessionStart: Date.now(),
    offlineMode: false,
    ivdKnowledgeBase: {
      rd: {
        title: '产品研发流程',
        topics: [
          '需求分析与市场调研',
          '技术可行性评估',
          '产品设计与开发',
          '原型制作与测试',
          '临床试验设计',
          '质量管理体系建立'
        ]
      },
      registration: {
        title: '注册申报要点',
        topics: [
          'NMPA注册流程',
          'CE认证要求',
          'FDA 510(k)申报',
          '临床试验要求',
          '技术文档准备',
          '质量管理体系认证'
        ]
      },
      sales: {
        title: '市场销售策略',
        topics: [
          '市场定位与分析',
          '渠道建设策略',
          '价格策略制定',
          '客户关系管理',
          '竞争对手分析',
          '销售团队建设'
        ]
      }
    }
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('IVD智能顾问小程序启动');

    // 记录启动时间
    this.globalData.sessionStart = Date.now();

    // 检查本地存储的用户设置
    this.loadUserSettings();

    // 初始化性能监控
    this.initializePerformanceMonitoring();

    // 检查更新
    this.checkForUpdate();

    // 执行启动时的合规检查
    this.performStartupChecks();
  },

  onShow() {
    // 小程序显示时执行
    console.log('小程序显示');
  },

  onHide() {
    // 小程序隐藏时执行
    console.log('小程序隐藏');
    this.saveUserSettings();
  },

  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('userSettings');
      if (settings) {
        this.globalData.selectedAIModel = settings.selectedAIModel || 'gpt-3.5-turbo';
        this.globalData.apiKeys = settings.apiKeys || this.globalData.apiKeys;
      }
      
      const chatHistory = wx.getStorageSync('chatHistory');
      if (chatHistory) {
        this.globalData.chatHistory = chatHistory;
      }
    } catch (e) {
      console.error('加载用户设置失败:', e);
    }
  },

  saveUserSettings() {
    try {
      wx.setStorageSync('userSettings', {
        selectedAIModel: this.globalData.selectedAIModel,
        apiKeys: this.globalData.apiKeys
      });
      
      wx.setStorageSync('chatHistory', this.globalData.chatHistory);
    } catch (e) {
      console.error('保存用户设置失败:', e);
    }
  },

  // 初始化性能监控
  initializePerformanceMonitoring() {
    try {
      const { performanceManager } = require('./utils/performance');
      performanceManager.startMonitoring(10000); // 每10秒收集一次性能数据
      console.log('性能监控已启动');
    } catch (error) {
      console.error('性能监控启动失败:', error);
    }
  },

  // 执行启动时检查
  performStartupChecks() {
    try {
      // 检查存储空间
      const storageInfo = wx.getStorageInfoSync();
      if (storageInfo.currentSize > storageInfo.limitSize * 0.9) {
        console.warn('存储空间不足，建议清理数据');
      }

      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            this.globalData.offlineMode = true;
            console.log('网络不可用，启用离线模式');
          }
        }
      });
    } catch (error) {
      console.error('启动检查失败:', error);
    }
  },

  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo);
      } else {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: (res) => {
            this.globalData.userInfo = res.userInfo;
            resolve(res.userInfo);
          },
          fail: reject
        });
      }
    });
  },

  // 添加聊天记录
  addChatMessage(message) {
    this.globalData.chatHistory.push({
      ...message,
      timestamp: Date.now(),
      id: this.generateId()
    });
    
    // 限制历史记录数量
    if (this.globalData.chatHistory.length > 1000) {
      this.globalData.chatHistory = this.globalData.chatHistory.slice(-500);
    }
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // 获取IVD知识库
  getIVDKnowledge(category) {
    return this.globalData.ivdKnowledgeBase[category] || null;
  }
});
