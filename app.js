// app.js
App({
  globalData: {
    userInfo: null,
    selectedAIModel: 'gpt-3.5-turbo',
    apiKeys: {
      openai: '',
      anthropic: '',
      google: ''
    },
    chatHistory: [],
    sessionStart: Date.now(),
    offlineMode: false,
    ivdKnowledgeBase: {
      rd: {
        title: '产品研发流程',
        topics: [
          '需求分析与市场调研',
          '技术可行性评估',
          '产品设计与开发',
          '原型制作与测试',
          '临床试验设计',
          '质量管理体系建立'
        ]
      },
      registration: {
        title: '注册申报要点',
        topics: [
          'NMPA注册流程',
          'CE认证要求',
          'FDA 510(k)申报',
          '临床试验要求',
          '技术文档准备',
          '质量管理体系认证'
        ]
      },
      sales: {
        title: '市场销售策略',
        topics: [
          '市场定位与分析',
          '渠道建设策略',
          '价格策略制定',
          '客户关系管理',
          '竞争对手分析',
          '销售团队建设'
        ]
      }
    }
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('IVD智能顾问小程序启动');

    // 记录启动时间
    this.globalData.sessionStart = Date.now();

    // 初始化云开发
    this.initializeCloud();

    // 检查本地存储的用户设置
    this.loadUserSettings();

    // 检查登录状态
    this.checkLoginStatus();

    // 初始化性能监控
    this.initializePerformanceMonitoring();

    // 检查更新
    this.checkForUpdate();

    // 执行启动时的合规检查
    this.performStartupChecks();
  },

  onShow() {
    // 小程序显示时执行
    console.log('小程序显示');
  },

  onHide() {
    // 小程序隐藏时执行
    console.log('小程序隐藏');
    this.saveUserSettings();
  },

  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('userSettings');
      if (settings) {
        this.globalData.selectedAIModel = settings.selectedAIModel || 'gpt-3.5-turbo';
        this.globalData.apiKeys = settings.apiKeys || this.globalData.apiKeys;
      }
      
      const chatHistory = wx.getStorageSync('chatHistory');
      if (chatHistory) {
        this.globalData.chatHistory = chatHistory;
      }
    } catch (e) {
      console.error('加载用户设置失败:', e);
    }
  },

  saveUserSettings() {
    try {
      const settings = {
        selectedAIModel: this.globalData.selectedAIModel,
        apiKeys: this.globalData.apiKeys,
        chatHistory: this.globalData.chatHistory
      };

      // 保存到本地
      wx.setStorageSync('userSettings', settings);
      wx.setStorageSync('chatHistory', this.globalData.chatHistory);

      // 如果用户已登录，同步到云端
      if (this.globalData.userInfo && this.globalData.userInfo.openid) {
        this.syncUserSettingsToCloud(settings);
      }

      console.log('用户设置保存成功');
    } catch (e) {
      console.error('保存用户设置失败:', e);
    }
  },

  // 同步用户设置到云端
  async syncUserSettingsToCloud(settings) {
    try {
      const { cloudSyncManager } = require('./utils/cloudSync');

      // 同步偏好设置
      const { userPreferencesManager } = require('./utils/userPreferences');
      cloudSyncManager.addSyncTask({
        type: 'preferences',
        data: userPreferencesManager.preferences
      });

      // 同步API密钥
      if (settings.apiKeys) {
        cloudSyncManager.addSyncTask({
          type: 'apiKeys',
          data: settings.apiKeys
        });
      }

      // 同步聊天记录（最近的）
      if (settings.chatHistory && settings.chatHistory.length > 0) {
        cloudSyncManager.addSyncTask({
          type: 'chatHistory',
          data: {
            messages: settings.chatHistory.slice(-20), // 最近20条
            sessionId: cloudSyncManager.generateSessionId()
          }
        });
      }

    } catch (error) {
      console.error('云端同步失败:', error);
    }
  },

  // 初始化云开发
  initializeCloud() {
    try {
      const { cloudSyncManager } = require('./utils/cloudSync');
      cloudSyncManager.init();
      console.log('云开发初始化完成');
    } catch (error) {
      console.error('云开发初始化失败:', error);
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.openid) {
        this.globalData.userInfo = userInfo;
        console.log('用户已登录:', userInfo.nickName);

        // 如果已登录，从云端同步数据
        this.syncDataFromCloud();
      } else {
        console.log('用户未登录');
        // 跳转到登录页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }, 1000);
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  },

  // 从云端同步数据
  async syncDataFromCloud() {
    try {
      const { cloudSyncManager } = require('./utils/cloudSync');

      // 恢复用户偏好设置
      const preferencesResult = await cloudSyncManager.getUserPreferences();
      if (preferencesResult.success) {
        const { userPreferencesManager } = require('./utils/userPreferences');
        userPreferencesManager.preferences = {
          ...userPreferencesManager.preferences,
          ...preferencesResult.data
        };
      }

      // 恢复API密钥
      const apiKeysResult = await cloudSyncManager.getAPIKeys();
      if (apiKeysResult.success) {
        this.globalData.apiKeys = {
          ...this.globalData.apiKeys,
          ...apiKeysResult.data
        };
      }

      console.log('云端数据同步完成');
    } catch (error) {
      console.error('云端数据同步失败:', error);
    }
  },

  // 初始化性能监控
  initializePerformanceMonitoring() {
    try {
      const { performanceManager } = require('./utils/performance');
      performanceManager.startMonitoring(10000); // 每10秒收集一次性能数据
      console.log('性能监控已启动');
    } catch (error) {
      console.error('性能监控启动失败:', error);
    }
  },

  // 执行启动时检查
  performStartupChecks() {
    try {
      // 检查存储空间
      const storageInfo = wx.getStorageInfoSync();
      if (storageInfo.currentSize > storageInfo.limitSize * 0.9) {
        console.warn('存储空间不足，建议清理数据');
      }

      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            this.globalData.offlineMode = true;
            console.log('网络不可用，启用离线模式');
          }
        }
      });
    } catch (error) {
      console.error('启动检查失败:', error);
    }
  },

  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo);
      } else {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: (res) => {
            this.globalData.userInfo = res.userInfo;
            resolve(res.userInfo);
          },
          fail: reject
        });
      }
    });
  },

  // 添加聊天记录
  addChatMessage(message) {
    this.globalData.chatHistory.push({
      ...message,
      timestamp: Date.now(),
      id: this.generateId()
    });
    
    // 限制历史记录数量
    if (this.globalData.chatHistory.length > 1000) {
      this.globalData.chatHistory = this.globalData.chatHistory.slice(-500);
    }
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // 获取IVD知识库
  getIVDKnowledge(category) {
    return this.globalData.ivdKnowledgeBase[category] || null;
  }
});
