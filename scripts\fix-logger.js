// scripts/fix-logger.js
// 快速修复脚本 - 替换所有页面的console调用和存储操作

const fs = require('fs');
const path = require('path');

/**
 * 修复单个文件
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否已经导入了logger
    if (!content.includes("require('../../utils/logger')") && 
        !content.includes("require('./utils/logger')")) {
      
      // 在文件开头添加logger导入
      const lines = content.split('\n');
      let insertIndex = 0;
      
      // 找到最后一个require语句的位置
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('require(') && lines[i].includes('const')) {
          insertIndex = i + 1;
        }
        if (lines[i].includes('Page({') || lines[i].includes('App({')) {
          break;
        }
      }
      
      // 确定正确的路径
      const relativePath = filePath.includes('pages/') ? '../../utils/logger' : './utils/logger';
      const importLine = `const { logger, SafeStorage, ErrorBoundary } = require('${relativePath}');`;
      
      lines.splice(insertIndex, 0, importLine);
      content = lines.join('\n');
      modified = true;
    }

    // 替换console调用
    const consoleReplacements = [
      { from: /console\.log\(/g, to: 'logger.info(' },
      { from: /console\.info\(/g, to: 'logger.info(' },
      { from: /console\.warn\(/g, to: 'logger.warn(' },
      { from: /console\.error\(/g, to: 'logger.error(' }
    ];

    consoleReplacements.forEach(replacement => {
      if (replacement.from.test(content)) {
        content = content.replace(replacement.from, replacement.to);
        modified = true;
      }
    });

    // 替换存储操作
    const storageReplacements = [
      { 
        from: /wx\.getStorageSync\(([^)]+)\)/g, 
        to: 'SafeStorage.getSync($1)' 
      },
      { 
        from: /wx\.setStorageSync\(([^)]+)\)/g, 
        to: 'SafeStorage.setSync($1)' 
      },
      { 
        from: /wx\.removeStorageSync\(([^)]+)\)/g, 
        to: 'SafeStorage.removeSync($1)' 
      },
      { 
        from: /wx\.clearStorageSync\(\)/g, 
        to: 'SafeStorage.clearSync()' 
      },
      { 
        from: /wx\.getStorageInfoSync\(\)/g, 
        to: 'SafeStorage.getInfo()' 
      }
    ];

    storageReplacements.forEach(replacement => {
      if (replacement.from.test(content)) {
        content = content.replace(replacement.from, replacement.to);
        modified = true;
      }
    });

    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  跳过: ${filePath} (无需修复)`);
      return false;
    }

  } catch (error) {
    console.error(`❌ 修复失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 递归扫描目录
 */
function scanDirectory(dirPath, fileList = []) {
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 跳过node_modules和.git目录
      if (!['node_modules', '.git', 'miniprogram_npm'].includes(file)) {
        scanDirectory(fullPath, fileList);
      }
    } else if (file.endsWith('.js') && !file.includes('.min.')) {
      fileList.push(fullPath);
    }
  });
  
  return fileList;
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复日志和存储调用...\n');
  
  const projectRoot = process.cwd();
  const jsFiles = scanDirectory(projectRoot);
  
  let fixedCount = 0;
  let totalCount = jsFiles.length;
  
  jsFiles.forEach(filePath => {
    if (fixFile(filePath)) {
      fixedCount++;
    }
  });
  
  console.log('\n📊 修复统计:');
  console.log(`总文件数: ${totalCount}`);
  console.log(`已修复: ${fixedCount}`);
  console.log(`跳过: ${totalCount - fixedCount}`);
  
  if (fixedCount > 0) {
    console.log('\n✨ 修复完成！建议重新编译项目。');
  } else {
    console.log('\n✅ 所有文件都已是最新状态。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, scanDirectory, main };
