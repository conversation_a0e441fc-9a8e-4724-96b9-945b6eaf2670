// pages/cloudStatus/cloudStatus.js
const { cloudStatusManager } = require('../../utils/cloudStatus')
const { logger } = require('../../utils/logger')

Page({
  data: {
    isLoading: false,
    lastCheckTime: '',
    statusSummary: {
      total: 0,
      available: 0,
      unavailable: 0,
      required_missing: 0
    },
    functionList: [],
    recommendations: [],
    showGuideModal: false
  },

  onLoad(options) {
    logger.info('云函数状态页面加载')
    this.checkCloudFunctions()
  },

  onShow() {
    // 页面显示时刷新状态
    this.checkCloudFunctions()
  },

  /**
   * 检查云函数状态
   */
  async checkCloudFunctions() {
    this.setData({
      isLoading: true
    })

    try {
      const status = await cloudStatusManager.checkAllFunctions()
      const report = cloudStatusManager.generateReport(status)
      
      this.processStatusData(status, report)
      
      this.setData({
        isLoading: false,
        lastCheckTime: this.formatTime(new Date())
      })

    } catch (error) {
      logger.error('检查云函数状态失败:', error)
      
      this.setData({
        isLoading: false
      })
      
      wx.showToast({
        title: '检查失败',
        icon: 'error'
      })
    }
  },

  /**
   * 处理状态数据
   */
  processStatusData(status, report) {
    const functionList = []
    
    // 处理云函数列表
    for (const [name, info] of Object.entries(status)) {
      const statusIcon = this.getStatusIcon(info)
      const statusText = this.getStatusText(info)
      
      functionList.push({
        name,
        description: info.description,
        available: info.available,
        required: info.required,
        status: info.status || (info.available ? 'healthy' : 'error'),
        statusIcon,
        statusText,
        responseTime: info.responseTime,
        lastCheck: info.lastCheck ? this.formatTime(new Date(info.lastCheck)) : '',
        error: info.error
      })
    }

    // 处理建议
    const recommendations = report.recommendations.map(rec => ({
      ...rec,
      priorityText: this.getPriorityText(rec.priority)
    }))

    this.setData({
      statusSummary: report.summary,
      functionList,
      recommendations
    })
  },

  /**
   * 获取状态图标
   */
  getStatusIcon(info) {
    if (!info.available) {
      return '🔴'
    } else if (info.status === 'healthy') {
      return '🟢'
    } else {
      return '🟡'
    }
  },

  /**
   * 获取状态文本
   */
  getStatusText(info) {
    if (!info.available) {
      return '不可用'
    } else if (info.status === 'healthy') {
      return '正常'
    } else {
      return '异常'
    }
  },

  /**
   * 获取优先级文本
   */
  getPriorityText(priority) {
    const priorityMap = {
      high: '🔴 紧急',
      medium: '🟡 重要',
      low: '🟢 建议',
      info: 'ℹ️ 信息'
    }
    return priorityMap[priority] || priority
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  },

  /**
   * 刷新状态
   */
  refreshStatus() {
    // 清除缓存，强制重新检查
    cloudStatusManager.clearCache()
    this.checkCloudFunctions()
  },

  /**
   * 显示部署指南
   */
  showDeploymentGuide() {
    this.setData({
      showGuideModal: true
    })
  },

  /**
   * 隐藏部署指南
   */
  hideGuideModal() {
    this.setData({
      showGuideModal: false
    })
  },

  /**
   * 导出报告
   */
  exportReport() {
    const { statusSummary, functionList, recommendations } = this.data
    
    let report = '云函数状态报告\n'
    report += `生成时间: ${new Date().toLocaleString()}\n\n`
    
    report += '状态概览:\n'
    report += `总计: ${statusSummary.total}\n`
    report += `可用: ${statusSummary.available}\n`
    report += `不可用: ${statusSummary.unavailable}\n`
    report += `必需缺失: ${statusSummary.required_missing}\n\n`
    
    report += '云函数详情:\n'
    functionList.forEach(func => {
      report += `${func.name}: ${func.statusText}`
      if (func.required) report += ' (必需)'
      if (func.responseTime) report += ` - ${func.responseTime}ms`
      if (func.error) report += ` - ${func.error}`
      report += '\n'
    })
    
    if (recommendations.length > 0) {
      report += '\n修复建议:\n'
      recommendations.forEach((rec, index) => {
        report += `${index + 1}. ${rec.action}\n`
      })
    }

    // 复制到剪贴板
    wx.setClipboardData({
      data: report,
      success: () => {
        wx.showToast({
          title: '报告已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 云函数状态检查',
      path: '/pages/cloudStatus/cloudStatus'
    }
  }
})
