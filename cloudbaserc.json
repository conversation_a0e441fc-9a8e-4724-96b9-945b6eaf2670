{"envId": "your-env-id", "version": "2.0", "framework": {"name": "wechat-miniprogram", "plugins": {"client": {"use": "@cloudbase/framework-plugin-mp", "inputs": {}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "login", "timeout": 60, "envVariables": {}, "runtime": "Nodejs12.16", "memorySize": 256}, {"name": "userdata", "timeout": 60, "envVariables": {}, "runtime": "Nodejs12.16", "memorySize": 256}, {"name": "initDB", "timeout": 60, "envVariables": {}, "runtime": "Nodejs12.16", "memorySize": 256}]}}}}}