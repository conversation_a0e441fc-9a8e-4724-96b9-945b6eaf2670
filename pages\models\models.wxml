<!--pages/models/models.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header-section">
    <view class="header-content">
      <text class="page-title">AI模型选择</text>
      <text class="page-desc">选择最适合您需求的AI助手</text>
    </view>
  </view>

  <!-- 当前选中的模型 -->
  <view class="current-model-section">
    <view class="section-title">
      <text>当前模型</text>
    </view>
    <view class="current-model card">
      <view class="model-info">
        <view class="model-header">
          <text class="model-name">{{currentModel.name}}</text>
          <view class="model-status {{currentModel.status}}">
            <text>{{currentModel.statusText}}</text>
          </view>
        </view>
        <text class="model-desc">{{currentModel.description}}</text>
        <view class="model-features">
          <text class="feature-item" wx:for="{{currentModel.features}}" wx:key="index">{{item}}</text>
        </view>
      </view>
      <view class="model-icon">
        <text>{{currentModel.icon}}</text>
      </view>
    </view>
  </view>

  <!-- 可用模型列表 -->
  <view class="models-section">
    <view class="section-title">
      <text>可用模型</text>
    </view>
    
    <view class="models-list">
      <view class="model-item card {{item.id === selectedModelId ? 'selected' : ''}}" 
            wx:for="{{availableModels}}" 
            wx:key="id" 
            bindtap="selectModel" 
            data-model="{{item}}">
        
        <view class="model-content">
          <view class="model-header">
            <view class="model-basic-info">
              <text class="model-name">{{item.name}}</text>
              <view class="model-status {{item.status}}">
                <text>{{item.statusText}}</text>
              </view>
            </view>
            <view class="model-icon">
              <text>{{item.icon}}</text>
            </view>
          </view>
          
          <text class="model-desc">{{item.description}}</text>
          
          <!-- 模型特性 -->
          <view class="model-features">
            <text class="feature-item" wx:for="{{item.features}}" wx:key="index" wx:for-item="feature">{{feature}}</text>
          </view>
          
          <!-- 模型性能指标 -->
          <view class="model-metrics">
            <view class="metric-item">
              <text class="metric-label">响应速度</text>
              <view class="metric-bar">
                <view class="metric-fill" style="width: {{item.metrics.speed}}%"></view>
              </view>
              <text class="metric-value">{{item.metrics.speed}}%</text>
            </view>
            
            <view class="metric-item">
              <text class="metric-label">准确性</text>
              <view class="metric-bar">
                <view class="metric-fill" style="width: {{item.metrics.accuracy}}%"></view>
              </view>
              <text class="metric-value">{{item.metrics.accuracy}}%</text>
            </view>
            
            <view class="metric-item">
              <text class="metric-label">专业性</text>
              <view class="metric-bar">
                <view class="metric-fill" style="width: {{item.metrics.expertise}}%"></view>
              </view>
              <text class="metric-value">{{item.metrics.expertise}}%</text>
            </view>
          </view>
          
          <!-- 适用场景 -->
          <view class="use-cases">
            <text class="use-cases-title">适用场景：</text>
            <text class="use-cases-content">{{item.useCases.join('、')}}</text>
          </view>
        </view>
        
        <!-- 选中标识 -->
        <view class="selection-indicator" wx:if="{{item.id === selectedModelId}}">
          <text>✓</text>
        </view>
      </view>
    </view>
  </view>



  <!-- 保存按钮 -->
  <view class="save-section">
    <button class="save-btn btn btn-primary" bindtap="saveSettings">
      <text>保存设置</text>
    </button>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <view class="help-card card">
      <view class="help-header">
        <text class="help-title">💡 使用提示</text>
      </view>
      <view class="help-content">
        <text class="help-item">• 不同AI模型有各自的特点，建议根据咨询内容选择</text>
        <text class="help-item">• DeepSeek适合技术分析，通义千问响应迅速，腾讯混元稳定可靠</text>
        <text class="help-item">• 所有模型均为国产AI，数据安全有保障</text>
        <text class="help-item">• 根据使用场景选择合适的模型以获得最佳体验</text>
      </view>
    </view>
  </view>
</view>
