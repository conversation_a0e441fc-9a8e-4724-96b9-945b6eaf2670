// utils/testUtils.js
// 测试工具和性能监控

/**
 * 测试工具管理器
 */
class TestManager {
  constructor() {
    this.testResults = [];
    this.performanceMetrics = [];
    this.isTestMode = false;
  }

  /**
   * 启用测试模式
   */
  enableTestMode() {
    this.isTestMode = true;
    console.log('测试模式已启用');
  }

  /**
   * 禁用测试模式
   */
  disableTestMode() {
    this.isTestMode = false;
    console.log('测试模式已禁用');
  }

  /**
   * 运行AI服务测试
   * @returns {Promise<Object>} 测试结果
   */
  async testAIServices() {
    const testCases = [
      {
        name: 'OpenAI服务连接测试',
        test: () => this.testOpenAIConnection()
      },
      {
        name: 'Claude服务连接测试',
        test: () => this.testClaudeConnection()
      },
      {
        name: 'Gemini服务连接测试',
        test: () => this.testGeminiConnection()
      },
      {
        name: 'AI响应质量测试',
        test: () => this.testAIResponseQuality()
      }
    ];

    const results = [];
    
    for (const testCase of testCases) {
      try {
        const startTime = Date.now();
        const result = await testCase.test();
        const duration = Date.now() - startTime;
        
        results.push({
          name: testCase.name,
          status: 'passed',
          duration,
          result
        });
      } catch (error) {
        results.push({
          name: testCase.name,
          status: 'failed',
          error: error.message
        });
      }
    }

    this.testResults.push({
      category: 'AI Services',
      timestamp: Date.now(),
      results
    });

    return results;
  }

  /**
   * 测试OpenAI连接
   */
  async testOpenAIConnection() {
    const { aiService } = require('./aiService');
    
    try {
      const response = await aiService.sendMessage(
        '测试消息', 
        'gpt-3.5-turbo', 
        { maxTokens: 10 }
      );
      return { success: true, response: response.substring(0, 50) };
    } catch (error) {
      throw new Error(`OpenAI连接失败: ${error.message}`);
    }
  }

  /**
   * 测试Claude连接
   */
  async testClaudeConnection() {
    // 模拟Claude连接测试
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const app = getApp();
        if (app.globalData.apiKeys.anthropic) {
          resolve({ success: true, message: 'Claude连接正常' });
        } else {
          reject(new Error('Claude API密钥未配置'));
        }
      }, 1000);
    });
  }

  /**
   * 测试Gemini连接
   */
  async testGeminiConnection() {
    // 模拟Gemini连接测试
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const app = getApp();
        if (app.globalData.apiKeys.google) {
          resolve({ success: true, message: 'Gemini连接正常' });
        } else {
          reject(new Error('Gemini API密钥未配置'));
        }
      }, 1000);
    });
  }

  /**
   * 测试AI响应质量
   */
  async testAIResponseQuality() {
    const testQuestions = [
      '什么是IVD？',
      '如何进行产品注册？',
      '质量管理体系的要点是什么？'
    ];

    const results = [];
    
    for (const question of testQuestions) {
      try {
        const { aiService } = require('./aiService');
        const response = await aiService.sendMessage(question, 'gpt-3.5-turbo');
        
        results.push({
          question,
          responseLength: response.length,
          containsKeywords: this.checkIVDKeywords(response),
          isProfessional: this.assessProfessionalism(response)
        });
      } catch (error) {
        results.push({
          question,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * 检查IVD关键词
   * @param {string} text - 文本内容
   * @returns {boolean} 是否包含关键词
   */
  checkIVDKeywords(text) {
    const keywords = ['IVD', '体外诊断', '医疗器械', 'NMPA', 'CE', 'FDA'];
    return keywords.some(keyword => text.includes(keyword));
  }

  /**
   * 评估专业性
   * @param {string} text - 文本内容
   * @returns {number} 专业性评分 (0-1)
   */
  assessProfessionalism(text) {
    const professionalTerms = [
      '注册申报', '质量管理', '风险管理', '临床试验',
      '技术要求', '验证确认', '法规要求'
    ];
    
    const foundTerms = professionalTerms.filter(term => text.includes(term));
    return foundTerms.length / professionalTerms.length;
  }

  /**
   * 运行UI测试
   * @returns {Object} 测试结果
   */
  testUI() {
    const tests = [
      {
        name: '页面加载测试',
        test: () => this.testPageLoad()
      },
      {
        name: '组件渲染测试',
        test: () => this.testComponentRender()
      },
      {
        name: '交互功能测试',
        test: () => this.testInteractions()
      }
    ];

    const results = tests.map(testCase => {
      try {
        const result = testCase.test();
        return {
          name: testCase.name,
          status: 'passed',
          result
        };
      } catch (error) {
        return {
          name: testCase.name,
          status: 'failed',
          error: error.message
        };
      }
    });

    return results;
  }

  /**
   * 测试页面加载
   */
  testPageLoad() {
    // 检查关键页面是否存在
    const pages = ['pages/index/index', 'pages/chat/chat', 'pages/models/models', 'pages/settings/settings'];
    const app = getApp();
    
    return {
      pagesExist: pages.length === 4,
      globalDataInitialized: !!app.globalData,
      userInfoLoaded: !!app.globalData.userInfo || true // 可选
    };
  }

  /**
   * 测试组件渲染
   */
  testComponentRender() {
    // 模拟组件渲染测试
    return {
      chatInterface: true,
      messageList: true,
      inputArea: true,
      modelSelector: true
    };
  }

  /**
   * 测试交互功能
   */
  testInteractions() {
    // 模拟交互测试
    return {
      sendMessage: true,
      switchModel: true,
      exportChat: true,
      settingsUpdate: true
    };
  }

  /**
   * 运行性能测试
   * @returns {Object} 性能测试结果
   */
  testPerformance() {
    const metrics = {
      memoryUsage: this.getMemoryUsage(),
      storageUsage: this.getStorageUsage(),
      apiResponseTime: this.measureAPIResponseTime(),
      renderTime: this.measureRenderTime()
    };

    this.performanceMetrics.push({
      timestamp: Date.now(),
      metrics
    });

    return metrics;
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    // 微信小程序无法直接获取内存使用情况
    // 这里返回模拟数据
    return {
      used: Math.random() * 100,
      total: 512,
      unit: 'MB'
    };
  }

  /**
   * 获取存储使用情况
   */
  getStorageUsage() {
    try {
      const info = wx.getStorageInfoSync();
      return {
        used: info.currentSize,
        total: info.limitSize,
        keys: info.keys.length,
        unit: 'KB'
      };
    } catch (error) {
      return {
        error: error.message
      };
    }
  }

  /**
   * 测量API响应时间
   */
  async measureAPIResponseTime() {
    const startTime = Date.now();
    
    try {
      // 发送一个简单的测试请求
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 500));
      
      return {
        responseTime: Date.now() - startTime,
        status: 'success'
      };
    } catch (error) {
      return {
        responseTime: Date.now() - startTime,
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 测量渲染时间
   */
  measureRenderTime() {
    // 模拟渲染时间测量
    return {
      initialRender: 50 + Math.random() * 100,
      updateRender: 10 + Math.random() * 50,
      unit: 'ms'
    };
  }

  /**
   * 运行兼容性测试
   * @returns {Object} 兼容性测试结果
   */
  testCompatibility() {
    const systemInfo = wx.getSystemInfoSync();
    
    return {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion,
      brand: systemInfo.brand,
      model: systemInfo.model,
      pixelRatio: systemInfo.pixelRatio,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      statusBarHeight: systemInfo.statusBarHeight,
      language: systemInfo.language,
      fontSizeSetting: systemInfo.fontSizeSetting,
      benchmarkLevel: systemInfo.benchmarkLevel,
      albumAuthorized: systemInfo.albumAuthorized,
      cameraAuthorized: systemInfo.cameraAuthorized,
      locationAuthorized: systemInfo.locationAuthorized,
      microphoneAuthorized: systemInfo.microphoneAuthorized,
      notificationAuthorized: systemInfo.notificationAuthorized,
      bluetoothEnabled: systemInfo.bluetoothEnabled,
      locationEnabled: systemInfo.locationEnabled,
      wifiEnabled: systemInfo.wifiEnabled,
      safeArea: systemInfo.safeArea
    };
  }

  /**
   * 生成测试报告
   * @returns {Object} 测试报告
   */
  generateTestReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.testResults.length,
        passedTests: 0,
        failedTests: 0
      },
      details: this.testResults,
      performance: this.performanceMetrics.slice(-5), // 最近5次性能数据
      recommendations: this.generateRecommendations()
    };

    // 计算通过和失败的测试数量
    this.testResults.forEach(category => {
      category.results.forEach(result => {
        if (result.status === 'passed') {
          report.summary.passedTests++;
        } else {
          report.summary.failedTests++;
        }
      });
    });

    return report;
  }

  /**
   * 生成优化建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    const recommendations = [];
    
    // 基于性能数据生成建议
    if (this.performanceMetrics.length > 0) {
      const latestMetrics = this.performanceMetrics[this.performanceMetrics.length - 1];
      
      if (latestMetrics.metrics.storageUsage.used > latestMetrics.metrics.storageUsage.total * 0.8) {
        recommendations.push({
          type: 'storage',
          priority: 'high',
          message: '存储空间使用率过高，建议清理聊天记录或缓存数据'
        });
      }
      
      if (latestMetrics.metrics.apiResponseTime.responseTime > 5000) {
        recommendations.push({
          type: 'performance',
          priority: 'medium',
          message: 'API响应时间较长，建议检查网络连接或切换AI模型'
        });
      }
    }

    // 基于测试结果生成建议
    this.testResults.forEach(category => {
      const failedTests = category.results.filter(r => r.status === 'failed');
      if (failedTests.length > 0) {
        recommendations.push({
          type: 'functionality',
          priority: 'high',
          message: `${category.category}存在${failedTests.length}个失败的测试，需要修复`
        });
      }
    });

    return recommendations;
  }

  /**
   * 清理测试数据
   */
  clearTestData() {
    this.testResults = [];
    this.performanceMetrics = [];
    console.log('测试数据已清理');
  }

  /**
   * 导出测试报告
   * @returns {string} JSON格式的测试报告
   */
  exportTestReport() {
    const report = this.generateTestReport();
    return JSON.stringify(report, null, 2);
  }
}

// 创建全局实例
const testManager = new TestManager();

module.exports = {
  testManager,
  TestManager
};
