/* pages/subscription/subscription.wxss */

/* 头部信息 */
.header-section {
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
}

.current-plan {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl);
}

.plan-info {
  flex: 1;
}

.plan-title {
  display: block;
  font-size: var(--font-size-base);
  opacity: 0.9;
  margin-bottom: var(--spacing-xs);
}

.plan-name {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.plan-desc {
  display: block;
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

.plan-icon {
  font-size: 80rpx;
}

/* 使用统计 */
.usage-stats {
  margin-bottom: var(--spacing-lg);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.stats-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.stats-period {
  font-size: var(--font-size-sm);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-base);
}

.stat-value {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xs);
}

.stat-limit {
  font-size: var(--font-size-xs);
}

.usage-progress {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.progress-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.progress-label {
  width: 120rpx;
  font-size: var(--font-size-sm);
}

.progress {
  flex: 1;
  height: 12rpx;
  background: var(--bg-quaternary);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-base);
}

.progress-text {
  width: 80rpx;
  text-align: right;
  font-size: var(--font-size-xs);
}

/* 订阅计划 */
.subscription-plans {
  margin-bottom: var(--spacing-lg);
}

.plans-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.plan-card {
  position: relative;
  padding: var(--spacing-lg);
  transition: all var(--transition-base);
  border: 2rpx solid transparent;
}

.plan-card:active {
  transform: scale(0.98);
}

.current-plan-card {
  border-color: var(--primary-color);
  background: rgba(46, 124, 232, 0.05);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.plan-card-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.plan-price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.price-value {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  color: var(--primary-color);
}

.price-unit {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--success-color);
  color: white;
  border-radius: 50%;
  font-size: var(--font-size-xs);
  font-weight: bold;
}

.feature-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.plan-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-round);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.popular-badge {
  background: var(--warning-color);
}

/* 模型访问权限 */
.model-access {
  margin-bottom: var(--spacing-lg);
}

.access-header {
  margin-bottom: var(--spacing-lg);
}

.access-title {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.access-subtitle {
  display: block;
  font-size: var(--font-size-sm);
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.model-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-base);
}

.model-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: white;
  margin-right: var(--spacing-md);
}

.model-info {
  flex: 1;
}

.model-name {
  display: block;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.model-provider {
  display: block;
  font-size: var(--font-size-sm);
}

.model-tier {
  font-size: var(--font-size-xs);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

/* 升级弹窗 */
.duration-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.duration-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border: 2rpx solid var(--border-base);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
}

.duration-item.selected {
  border-color: var(--primary-color);
  background: rgba(46, 124, 232, 0.05);
}

.duration-text {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
}

.duration-price {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.duration-discount {
  position: absolute;
  top: -8rpx;
  right: var(--spacing-md);
  padding: 4rpx var(--spacing-xs);
  background: var(--warning-color);
  color: white;
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-base);
}

.total-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.total-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.modal-footer {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.modal-footer .btn {
  flex: 1;
}
