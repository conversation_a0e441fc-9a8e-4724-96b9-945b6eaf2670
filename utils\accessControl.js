// utils/accessControl.js
// 付费用户访问控制系统

const { logger } = require('./logger')
const { cloudCall } = require('./cloudFunction')

/**
 * 访问控制管理器
 */
class AccessControlManager {
  constructor() {
    this.userSubscription = null
    this.usageStats = null
    this.lastCheck = 0
    this.checkInterval = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 初始化用户订阅信息
   */
  async initialize(userInfo) {
    try {
      if (!userInfo || userInfo.isGuest) {
        this.userSubscription = this.getGuestSubscription()
        return
      }

      // 获取用户订阅信息
      const result = await cloudCall.payment('getUserSubscription')
      if (result.result && result.result.success) {
        this.userSubscription = result.result.data
      } else {
        // 默认为免费版
        this.userSubscription = this.getFreeSubscription()
      }

      // 获取使用统计
      await this.updateUsageStats()
      
      this.lastCheck = Date.now()
      logger.info('访问控制初始化完成:', this.userSubscription)

    } catch (error) {
      logger.error('访问控制初始化失败:', error)
      this.userSubscription = this.getFreeSubscription()
    }
  }

  /**
   * 检查模型访问权限
   */
  async checkModelAccess(modelId) {
    await this.ensureSubscriptionValid()

    if (!this.userSubscription) {
      return {
        allowed: false,
        reason: '用户订阅信息不可用',
        action: 'login'
      }
    }

    // 检查模型是否在允许列表中
    if (!this.userSubscription.modelAccess.includes(modelId)) {
      return {
        allowed: false,
        reason: '当前订阅计划不支持此模型',
        action: 'upgrade',
        requiredPlan: this.getRequiredPlan(modelId)
      }
    }

    // 检查使用限制
    const usageCheck = await this.checkUsageLimits()
    if (!usageCheck.allowed) {
      return usageCheck
    }

    return {
      allowed: true,
      remaining: {
        daily: this.userSubscription.dailyLimit - (this.usageStats?.daily || 0),
        monthly: this.userSubscription.monthlyLimit - (this.usageStats?.monthly || 0)
      }
    }
  }

  /**
   * 检查功能访问权限
   */
  async checkFeatureAccess(featureName) {
    await this.ensureSubscriptionValid()

    const featurePermissions = {
      'chat_history_export': ['basic', 'standard', 'premium'],
      'advanced_analytics': ['standard', 'premium'],
      'priority_support': ['premium'],
      'custom_models': ['premium'],
      'api_access': ['standard', 'premium'],
      'team_collaboration': ['premium'],
      'data_backup': ['basic', 'standard', 'premium']
    }

    const requiredPlans = featurePermissions[featureName]
    if (!requiredPlans) {
      return { allowed: true } // 功能对所有用户开放
    }

    if (!requiredPlans.includes(this.userSubscription.tier)) {
      return {
        allowed: false,
        reason: `此功能需要 ${requiredPlans.join(' 或 ')} 订阅`,
        action: 'upgrade',
        requiredPlan: requiredPlans[0]
      }
    }

    return { allowed: true }
  }

  /**
   * 检查使用限制
   */
  async checkUsageLimits() {
    if (!this.usageStats) {
      await this.updateUsageStats()
    }

    const { daily = 0, monthly = 0 } = this.usageStats

    // 检查日限制
    if (daily >= this.userSubscription.dailyLimit) {
      return {
        allowed: false,
        reason: '今日使用次数已达上限',
        action: 'upgrade',
        resetTime: this.getNextResetTime('daily')
      }
    }

    // 检查月限制
    if (monthly >= this.userSubscription.monthlyLimit) {
      return {
        allowed: false,
        reason: '本月使用次数已达上限',
        action: 'upgrade',
        resetTime: this.getNextResetTime('monthly')
      }
    }

    return { allowed: true }
  }

  /**
   * 记录使用量
   */
  async recordUsage(modelId, tokensUsed = 1) {
    try {
      const result = await cloudCall.payment('recordUsage', {
        modelId,
        tokensUsed,
        timestamp: new Date().toISOString()
      })

      if (result.result && result.result.success) {
        // 更新本地统计
        if (this.usageStats) {
          this.usageStats.daily += tokensUsed
          this.usageStats.monthly += tokensUsed
          this.usageStats.total += tokensUsed
        }
      }

      return result.result?.success || false

    } catch (error) {
      logger.error('记录使用量失败:', error)
      return false
    }
  }

  /**
   * 获取订阅状态
   */
  getSubscriptionStatus() {
    if (!this.userSubscription) {
      return {
        tier: 'unknown',
        status: 'inactive',
        features: []
      }
    }

    return {
      tier: this.userSubscription.tier,
      name: this.userSubscription.name,
      status: this.userSubscription.status,
      expiresAt: this.userSubscription.expiresAt,
      features: this.getAvailableFeatures(),
      limits: {
        daily: this.userSubscription.dailyLimit,
        monthly: this.userSubscription.monthlyLimit,
        models: this.userSubscription.modelAccess
      },
      usage: this.usageStats
    }
  }

  /**
   * 获取可用功能列表
   */
  getAvailableFeatures() {
    const allFeatures = {
      'chat_history_export': '聊天记录导出',
      'advanced_analytics': '高级分析',
      'priority_support': '优先支持',
      'custom_models': '自定义模型',
      'api_access': 'API访问',
      'team_collaboration': '团队协作',
      'data_backup': '数据备份'
    }

    const availableFeatures = []
    for (const [feature, name] of Object.entries(allFeatures)) {
      const access = this.checkFeatureAccessSync(feature)
      if (access.allowed) {
        availableFeatures.push({ id: feature, name })
      }
    }

    return availableFeatures
  }

  /**
   * 同步检查功能访问权限（不发起网络请求）
   */
  checkFeatureAccessSync(featureName) {
    const featurePermissions = {
      'chat_history_export': ['basic', 'standard', 'premium'],
      'advanced_analytics': ['standard', 'premium'],
      'priority_support': ['premium'],
      'custom_models': ['premium'],
      'api_access': ['standard', 'premium'],
      'team_collaboration': ['premium'],
      'data_backup': ['basic', 'standard', 'premium']
    }

    const requiredPlans = featurePermissions[featureName]
    if (!requiredPlans) {
      return { allowed: true }
    }

    if (!this.userSubscription || !requiredPlans.includes(this.userSubscription.tier)) {
      return {
        allowed: false,
        reason: `此功能需要 ${requiredPlans.join(' 或 ')} 订阅`,
        requiredPlan: requiredPlans[0]
      }
    }

    return { allowed: true }
  }

  /**
   * 确保订阅信息有效
   */
  async ensureSubscriptionValid() {
    const now = Date.now()
    if (now - this.lastCheck > this.checkInterval) {
      await this.updateUsageStats()
      this.lastCheck = now
    }
  }

  /**
   * 更新使用统计
   */
  async updateUsageStats() {
    try {
      const result = await cloudCall.payment('getUsageStats')
      if (result.result && result.result.success) {
        this.usageStats = result.result.data
      }
    } catch (error) {
      logger.error('更新使用统计失败:', error)
    }
  }

  /**
   * 获取所需订阅计划
   */
  getRequiredPlan(modelId) {
    const modelPlanMap = {
      'deepseek-v3-0324': 'free',
      'deepseek-r1-0528': 'basic',
      'qwen3': 'free',
      'qwen-max': 'standard'
    }

    return modelPlanMap[modelId] || 'premium'
  }

  /**
   * 获取下次重置时间
   */
  getNextResetTime(type) {
    const now = new Date()
    if (type === 'daily') {
      const tomorrow = new Date(now)
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)
      return tomorrow.toISOString()
    } else if (type === 'monthly') {
      const nextMonth = new Date(now)
      nextMonth.setMonth(nextMonth.getMonth() + 1)
      nextMonth.setDate(1)
      nextMonth.setHours(0, 0, 0, 0)
      return nextMonth.toISOString()
    }
    return null
  }

  /**
   * 获取游客订阅配置
   */
  getGuestSubscription() {
    return {
      tier: 'guest',
      name: '游客模式',
      status: 'active',
      dailyLimit: 3,
      monthlyLimit: 10,
      modelAccess: ['deepseek-v3-0324'],
      expiresAt: null
    }
  }

  /**
   * 获取免费订阅配置
   */
  getFreeSubscription() {
    return {
      tier: 'free',
      name: '免费版',
      status: 'active',
      dailyLimit: 10,
      monthlyLimit: 100,
      modelAccess: ['deepseek-v3-0324', 'qwen3'],
      expiresAt: null
    }
  }
}

// 创建全局实例
const accessControl = new AccessControlManager()

/**
 * 便捷的访问控制方法
 */
const checkAccess = {
  /**
   * 初始化
   */
  async init(userInfo) {
    return await accessControl.initialize(userInfo)
  },

  /**
   * 检查模型访问
   */
  async model(modelId) {
    return await accessControl.checkModelAccess(modelId)
  },

  /**
   * 检查功能访问
   */
  async feature(featureName) {
    return await accessControl.checkFeatureAccess(featureName)
  },

  /**
   * 记录使用
   */
  async record(modelId, tokensUsed) {
    return await accessControl.recordUsage(modelId, tokensUsed)
  },

  /**
   * 获取状态
   */
  getStatus() {
    return accessControl.getSubscriptionStatus()
  }
}

module.exports = {
  accessControl,
  checkAccess,
  AccessControlManager
}
