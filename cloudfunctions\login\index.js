// cloudfunctions/login/index.js
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 用户登录云函数
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @returns {Object} 登录结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    const { code, userInfo, loginType = 'wechat' } = event

    // 获取用户的openid和unionid
    const { OPENID, UNIONID, APPID } = wxContext

    console.log('登录类型:', loginType)
    console.log('OPENID:', OPENID)

    // 支持一键登录
    if (loginType === 'wechat_oneclick') {
      return await handleOneClickLogin(code, wxContext)
    }

    // 传统登录方式
    if (!OPENID && code) {
      console.log('使用code登录，OPENID将在云函数中自动获取')
    }
    
    // 检查用户是否已存在
    const userCollection = db.collection('users')
    const existingUser = await userCollection.where({
      openid: OPENID
    }).get()
    
    let userData = {
      openid: OPENID,
      unionid: UNIONID,
      appid: APPID,
      loginType,
      lastLoginTime: new Date(),
      loginCount: 1
    }
    
    if (existingUser.data.length > 0) {
      // 用户已存在，更新登录信息
      const user = existingUser.data[0]
      userData = {
        ...user,
        lastLoginTime: new Date(),
        loginCount: (user.loginCount || 0) + 1
      }
      
      // 如果提供了新的用户信息，更新用户资料
      if (userInfo) {
        userData.nickName = userInfo.nickName
        userData.avatarUrl = userInfo.avatarUrl
        userData.gender = userInfo.gender
        userData.country = userInfo.country
        userData.province = userInfo.province
        userData.city = userInfo.city
        userData.language = userInfo.language
        userData.updatedAt = new Date()
      }
      
      await userCollection.doc(user._id).update({
        data: userData
      })
      
      return {
        success: true,
        message: '登录成功',
        data: {
          userId: user._id,
          openid: OPENID,
          userInfo: userData,
          isNewUser: false
        }
      }
    } else {
      // 新用户，创建用户记录
      if (userInfo) {
        userData.nickName = userInfo.nickName
        userData.avatarUrl = userInfo.avatarUrl
        userData.gender = userInfo.gender
        userData.country = userInfo.country
        userData.province = userInfo.province
        userData.city = userInfo.city
        userData.language = userInfo.language
      } else {
        // 如果没有用户信息，使用默认值
        userData.nickName = '微信用户'
        userData.avatarUrl = '/images/default-avatar.png'
        userData.gender = 0
        userData.country = ''
        userData.province = ''
        userData.city = ''
        userData.language = 'zh_CN'
      }
      
      userData.createdAt = new Date()
      userData.updatedAt = new Date()
      userData.status = 'active'
      userData.preferences = {
        selectedAIModel: 'gpt-3.5-turbo',
        professionalField: '医疗器械研发',
        experienceLevel: '中级（3-5年）',
        focusAreas: ['产品研发', '注册申报']
      }
      
      const result = await userCollection.add({
        data: userData
      })
      
      return {
        success: true,
        message: '注册成功',
        data: {
          userId: result._id,
          openid: OPENID,
          userInfo: userData,
          isNewUser: true
        }
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败',
      error: error.message
    }
  }
}

/**
 * 处理微信一键登录
 */
async function handleOneClickLogin(code, wxContext) {
  try {
    const { OPENID, UNIONID, APPID } = wxContext

    if (!OPENID) {
      throw new Error('无法获取用户身份信息')
    }

    console.log('一键登录 - OPENID:', OPENID)

    // 检查用户是否已存在
    const userCollection = db.collection('users')
    const existingUser = await userCollection.where({ openid: OPENID }).get()

    let userData
    let isNewUser = false

    if (existingUser.data.length === 0) {
      // 新用户，创建用户记录
      isNewUser = true
      userData = {
        openid: OPENID,
        unionid: UNIONID,
        appid: APPID,
        nickName: '微信用户',
        avatarUrl: '/images/default-avatar.png',
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginTime: new Date(),
        loginCount: 1,
        loginType: 'wechat_oneclick',
        status: 'active',
        // 云端存储登录数据
        loginHistory: [{
          loginTime: new Date(),
          loginType: 'wechat_oneclick',
          ip: wxContext.CLIENTIP || '',
          userAgent: wxContext.CLIENTIPV6 || ''
        }]
      }

      const result = await userCollection.add({
        data: userData
      })

      userData.userId = result._id

      // 同步到云存储
      await syncToCloudStorage(OPENID, userData)

    } else {
      // 现有用户，更新登录信息
      userData = existingUser.data[0]

      const updateData = {
        lastLoginTime: new Date(),
        loginCount: db.command.inc(1),
        updatedAt: new Date()
      }

      // 添加登录历史记录
      const loginRecord = {
        loginTime: new Date(),
        loginType: 'wechat_oneclick',
        ip: wxContext.CLIENTIP || '',
        userAgent: wxContext.CLIENTIPV6 || ''
      }

      updateData.loginHistory = db.command.push([loginRecord])

      await userCollection.doc(userData._id).update({
        data: updateData
      })

      userData.userId = userData._id
      userData.lastLoginTime = updateData.lastLoginTime
      userData.loginCount = (userData.loginCount || 0) + 1

      // 同步到云存储
      await syncToCloudStorage(OPENID, userData)
    }

    return {
      success: true,
      data: {
        openid: OPENID,
        unionid: UNIONID,
        userId: userData.userId,
        nickName: userData.nickName,
        avatarUrl: userData.avatarUrl,
        isNewUser,
        loginType: 'wechat_oneclick',
        cloudStorageEnabled: true
      }
    }

  } catch (error) {
    console.error('一键登录失败:', error)
    return {
      success: false,
      message: '一键登录失败',
      error: error.message
    }
  }
}

/**
 * 同步用户数据到云存储
 */
async function syncToCloudStorage(openid, userData) {
  try {
    // 使用云存储保存用户登录数据
    const cloudPath = `user-data/${openid}/profile.json`

    const uploadResult = await cloud.uploadFile({
      cloudPath,
      fileContent: Buffer.from(JSON.stringify({
        ...userData,
        syncTime: new Date().toISOString()
      }))
    })

    console.log('用户数据已同步到云存储:', uploadResult.fileID)
    return uploadResult.fileID

  } catch (error) {
    console.error('同步到云存储失败:', error)
    // 不抛出错误，避免影响登录流程
    return null
  }
}
