// cloudfunctions/login/index.js
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 用户登录云函数
 * @param {Object} event - 事件参数
 * @param {Object} context - 上下文
 * @returns {Object} 登录结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { userInfo, loginType = 'wechat' } = event
    
    // 获取用户的openid和unionid
    const { OPENID, UNIONID, APPID } = wxContext
    
    // 检查用户是否已存在
    const userCollection = db.collection('users')
    const existingUser = await userCollection.where({
      openid: OPENID
    }).get()
    
    let userData = {
      openid: OPENID,
      unionid: UNIONID,
      appid: APPID,
      loginType,
      lastLoginTime: new Date(),
      loginCount: 1
    }
    
    if (existingUser.data.length > 0) {
      // 用户已存在，更新登录信息
      const user = existingUser.data[0]
      userData = {
        ...user,
        lastLoginTime: new Date(),
        loginCount: (user.loginCount || 0) + 1
      }
      
      // 如果提供了新的用户信息，更新用户资料
      if (userInfo) {
        userData.nickName = userInfo.nickName
        userData.avatarUrl = userInfo.avatarUrl
        userData.gender = userInfo.gender
        userData.country = userInfo.country
        userData.province = userInfo.province
        userData.city = userInfo.city
        userData.language = userInfo.language
        userData.updatedAt = new Date()
      }
      
      await userCollection.doc(user._id).update({
        data: userData
      })
      
      return {
        success: true,
        message: '登录成功',
        data: {
          userId: user._id,
          openid: OPENID,
          userInfo: userData,
          isNewUser: false
        }
      }
    } else {
      // 新用户，创建用户记录
      if (userInfo) {
        userData.nickName = userInfo.nickName
        userData.avatarUrl = userInfo.avatarUrl
        userData.gender = userInfo.gender
        userData.country = userInfo.country
        userData.province = userInfo.province
        userData.city = userInfo.city
        userData.language = userInfo.language
      }
      
      userData.createdAt = new Date()
      userData.updatedAt = new Date()
      userData.status = 'active'
      userData.preferences = {
        selectedAIModel: 'gpt-3.5-turbo',
        professionalField: '医疗器械研发',
        experienceLevel: '中级（3-5年）',
        focusAreas: ['产品研发', '注册申报']
      }
      
      const result = await userCollection.add({
        data: userData
      })
      
      return {
        success: true,
        message: '注册成功',
        data: {
          userId: result._id,
          openid: OPENID,
          userInfo: userData,
          isNewUser: true
        }
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败',
      error: error.message
    }
  }
}
