<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 头部欢迎区域 -->
  <view class="header-section">
    <view class="welcome-card card gradient-bg">
      <view class="welcome-content">
        <view class="logo-section">
          <view class="logo-icon">🔬</view>
        </view>
        <view class="title-section">
          <text class="main-title text-inverse">IVD智能顾问</text>
          <text class="subtitle text-inverse">您的专属研发、注册、销售顾问</text>
        </view>
        <view class="user-info" wx:if="{{userInfo.nickName && !userInfo.isGuest}}">
          <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
          <view class="user-details">
            <text class="user-name text-inverse">{{userInfo.nickName}}</text>
            <view class="user-tier tag tag-{{subscriptionTier}}">{{subscriptionName}}</view>
          </view>
        </view>
        <view class="guest-info" wx:elif="{{userInfo.isGuest}}">
          <text class="guest-text text-inverse">游客模式</text>
          <text class="login-tip text-inverse" bindtap="goToLogin">点击登录获得完整体验</text>
        </view>
        <view class="login-prompt" wx:else>
          <text class="login-text text-inverse" bindtap="goToLogin">点击登录开始使用</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速咨询入口 -->
  <view class="content-container">
    <view class="quick-actions">
      <view class="section-title">
        <text>快速咨询</text>
      </view>

      <view class="action-grid">
        <view class="action-item card card-hover" bindtap="startChat" data-category="rd">
          <view class="action-icon rd-icon">
            <text>🔬</text>
          </view>
          <view class="action-content">
            <text class="action-title">产品研发流程</text>
            <text class="action-desc text-secondary">需求分析、技术评估、产品设计</text>
          </view>
          <view class="action-arrow">></view>
        </view>

        <view class="action-item card card-hover" bindtap="startChat" data-category="registration">
          <view class="action-icon reg-icon">
            <text>📋</text>
          </view>
          <view class="action-content">
            <text class="action-title">注册申报要点</text>
            <text class="action-desc text-secondary">NMPA、CE、FDA注册指导</text>
          </view>
          <view class="action-arrow">></view>
        </view>

        <view class="action-item card card-hover" bindtap="startChat" data-category="sales">
          <view class="action-icon sales-icon">
            <text>📈</text>
          </view>
          <view class="action-content">
            <text class="action-title">市场销售策略</text>
            <text class="action-desc text-secondary">市场定位、渠道建设、客户管理</text>
          </view>
          <view class="action-arrow">></view>
        </view>

        <view class="action-item card card-hover" bindtap="startChat" data-category="quality">
          <view class="action-icon quality-icon">
            <text>✅</text>
          </view>
          <view class="action-content">
            <text class="action-title">质量管理体系</text>
            <text class="action-desc text-secondary">ISO13485、风险管理、审核</text>
          </view>
          <view class="action-arrow">></view>
        </view>
      </view>
    </view>

    <!-- AI模型状态 -->
    <view class="ai-status card">
      <view class="status-header flex flex-between">
        <view class="status-left">
          <text class="status-title">当前AI模型</text>
          <text class="status-subtitle text-secondary">{{currentModel.provider}} • {{currentModel.tier}}</text>
        </view>
        <button class="btn btn-outline btn-small" bindtap="changeModel">切换</button>
      </view>
      <view class="current-model">
        <view class="model-info flex">
          <view class="model-icon" style="background-color: {{currentModel.color}};">
            <text>{{currentModel.icon}}</text>
          </view>
          <view class="model-details flex-1">
            <text class="model-name">{{currentModel.name}}</text>
            <text class="model-desc text-secondary">{{currentModel.description}}</text>
            <view class="model-features">
              <view class="feature-tag tag tag-primary" wx:for="{{currentModel.features}}" wx:key="*this">
                {{item}}
              </view>
            </view>
          </view>
        </view>
        <view class="model-metrics">
          <view class="metric-item">
            <text class="metric-label text-tertiary">速度</text>
            <view class="metric-bar">
              <view class="metric-fill" style="width: {{currentModel.metrics.speed}}%;"></view>
            </view>
          </view>
          <view class="metric-item">
            <text class="metric-label text-tertiary">准确性</text>
            <view class="metric-bar">
              <view class="metric-fill" style="width: {{currentModel.metrics.accuracy}}%;"></view>
            </view>
          </view>
          <view class="metric-item">
            <text class="metric-label text-tertiary">专业性</text>
            <view class="metric-bar">
              <view class="metric-fill" style="width: {{currentModel.metrics.expertise}}%;"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

  <!-- 最近对话 -->
  <view class="recent-chats" wx:if="{{recentChats.length > 0}}">
    <view class="section-title">
      <text>最近对话</text>
      <text class="view-all" bindtap="viewAllChats">查看全部</text>
    </view>
    
    <view class="chat-list">
      <view class="chat-item card" wx:for="{{recentChats}}" wx:key="id" bindtap="openChat" data-id="{{item.id}}">
        <view class="chat-content">
          <text class="chat-title">{{item.title}}</text>
          <text class="chat-preview">{{item.lastMessage}}</text>
        </view>
        <view class="chat-meta">
          <text class="chat-time">{{item.timeAgo}}</text>
          <text class="chat-model">{{item.model}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 开始聊天按钮 -->
  <view class="start-chat-section">
    <button class="start-chat-btn btn btn-primary" bindtap="startNewChat">
      <text>开始新的咨询</text>
    </button>
  </view>

  <!-- 功能介绍 -->
  <view class="features-section">
    <view class="section-title">
      <text>功能特色</text>
    </view>
    
    <view class="features-grid">
      <view class="feature-item">
        <view class="feature-icon">🤖</view>
        <text class="feature-title">多AI模型</text>
        <text class="feature-desc">集成多种先进AI模型</text>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">🎯</view>
        <text class="feature-title">专业领域</text>
        <text class="feature-desc">专注IVD行业知识</text>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">⚡</view>
        <text class="feature-title">即时响应</text>
        <text class="feature-desc">快速获得专业建议</text>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">💾</view>
        <text class="feature-title">对话保存</text>
        <text class="feature-desc">自动保存咨询记录</text>
      </view>
    </view>
  </view>
  </view> <!-- 闭合 content-container -->
</view> <!-- 闭合 page-container -->
