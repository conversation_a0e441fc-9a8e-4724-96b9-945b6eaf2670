// pages/profile/profile.js
const app = getApp()
const { checkAccess } = require('../../utils/accessControl')
const { cloudCall } = require('../../utils/cloudFunction')

Page({
  data: {
    userInfo: {},
    subscriptionInfo: {
      tier: 'free',
      name: '免费版',
      icon: '🆓',
      monthlyLimit: 100
    },
    usageStats: {
      monthly: 0,
      totalChats: 0,
      favoriteModel: 'DeepSeek-V3'
    },
    usagePercentage: 0,
    settings: {
      notifications: true,
      autoSave: true,
      cloudSync: true
    },
    showSettingsModal: false,
    showHelpModal: false,
    showAboutModal: false,
    appVersion: '1.0.0',
    updateTime: '2024-01-15'
  },

  onLoad() {
    console.log('个人资料页面加载')
    this.initializeData()
  },

  onShow() {
    this.refreshUserData()
  },

  /**
   * 初始化数据
   */
  async initializeData() {
    await this.loadUserInfo()
    await this.loadSubscriptionInfo()
    await this.loadUsageStats()
    await this.loadSettings()
  },

  /**
   * 刷新用户数据
   */
  async refreshUserData() {
    await this.loadSubscriptionInfo()
    await this.loadUsageStats()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = app.globalData.userInfo || {}
    this.setData({
      userInfo: {
        nickName: userInfo.nickName || '',
        avatarUrl: userInfo.avatarUrl || '/images/default-avatar.png',
        isGuest: userInfo.isGuest || false
      }
    })
  },

  /**
   * 加载订阅信息
   */
  async loadSubscriptionInfo() {
    try {
      await checkAccess.init(app.globalData.userInfo)
      const status = checkAccess.getStatus()
      
      const tierNames = {
        'guest': '游客模式',
        'free': '免费版',
        'basic': '基础版',
        'standard': '标准版',
        'premium': '专业版'
      }
      
      const tierIcons = {
        'guest': '👤',
        'free': '🆓',
        'basic': '⭐',
        'standard': '🌟',
        'premium': '💎'
      }

      this.setData({
        subscriptionInfo: {
          tier: status.tier,
          name: tierNames[status.tier] || '未知',
          icon: tierIcons[status.tier] || '❓',
          monthlyLimit: status.limits?.monthly || 0,
          expiresAt: status.expiresAt
        }
      })

    } catch (error) {
      console.error('加载订阅信息失败:', error)
    }
  },

  /**
   * 加载使用统计
   */
  async loadUsageStats() {
    try {
      const result = await cloudCall.payment('getUsageStats')
      
      if (result.result && result.result.success) {
        const stats = result.result.data
        
        this.setData({
          usageStats: {
            monthly: stats.monthly || 0,
            totalChats: stats.totalChats || 0,
            favoriteModel: stats.favoriteModel || 'DeepSeek-V3'
          }
        })

        // 计算使用进度
        this.calculateUsagePercentage()
      }

    } catch (error) {
      console.error('加载使用统计失败:', error)
    }
  },

  /**
   * 计算使用进度
   */
  calculateUsagePercentage() {
    const { monthly } = this.data.usageStats
    const { monthlyLimit } = this.data.subscriptionInfo
    
    if (monthlyLimit > 0) {
      const percentage = Math.min((monthly / monthlyLimit) * 100, 100)
      this.setData({
        usagePercentage: percentage
      })
    }
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const settings = wx.getStorageSync('userSettings') || {}
      this.setData({
        settings: {
          notifications: settings.notifications !== false,
          autoSave: settings.autoSave !== false,
          cloudSync: settings.cloudSync !== false
        }
      })
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  },

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      wx.setStorageSync('userSettings', this.data.settings)
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  /**
   * 跳转到订阅页面
   */
  goToSubscription() {
    wx.navigateTo({
      url: '/pages/subscription/subscription'
    })
  },

  /**
   * 跳转到对话历史
   */
  goToHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  /**
   * 跳转到模型选择
   */
  goToModels() {
    wx.navigateTo({
      url: '/pages/models/models'
    })
  },

  /**
   * 显示设置弹窗
   */
  showSettings() {
    this.setData({
      showSettingsModal: true
    })
  },

  /**
   * 隐藏设置弹窗
   */
  hideSettingsModal() {
    this.setData({
      showSettingsModal: false
    })
  },

  /**
   * 显示帮助弹窗
   */
  showHelp() {
    this.setData({
      showHelpModal: true
    })
  },

  /**
   * 隐藏帮助弹窗
   */
  hideHelpModal() {
    this.setData({
      showHelpModal: false
    })
  },

  /**
   * 显示关于弹窗
   */
  showAbout() {
    this.setData({
      showAboutModal: true
    })
  },

  /**
   * 隐藏关于弹窗
   */
  hideAboutModal() {
    this.setData({
      showAboutModal: false
    })
  },

  /**
   * 切换通知设置
   */
  toggleNotifications(e) {
    this.setData({
      'settings.notifications': e.detail.value
    })
    this.saveSettings()
  },

  /**
   * 切换自动保存设置
   */
  toggleAutoSave(e) {
    this.setData({
      'settings.autoSave': e.detail.value
    })
    this.saveSettings()
  },

  /**
   * 切换云端同步设置
   */
  toggleCloudSync(e) {
    this.setData({
      'settings.cloudSync': e.detail.value
    })
    this.saveSettings()
  },

  /**
   * 显示常见问题
   */
  showFAQ() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 联系客服
   */
  contactSupport() {
    wx.showToast({
      title: '客服功能开发中',
      icon: 'none'
    })
  },

  /**
   * 显示隐私政策
   */
  showPrivacy() {
    wx.showToast({
      title: '隐私政策开发中',
      icon: 'none'
    })
  },

  /**
   * 显示服务条款
   */
  showTerms() {
    wx.showToast({
      title: '服务条款开发中',
      icon: 'none'
    })
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          app.globalData.userInfo = {}
          wx.removeStorageSync('userInfo')
          
          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/login/login'
          })
        }
      }
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 专业医疗器械咨询',
      path: '/pages/index/index'
    }
  }
})
