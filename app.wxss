/**app.wxss**/
@import "styles/theme.wxss";

/* 全局样式重置 */
page {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: var(--line-height-base);
}

/* 容器样式 */
.container {
  min-height: 100vh;
  padding: var(--spacing-md);
  box-sizing: border-box;
}

.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.content-container {
  padding: var(--spacing-md);
  padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom));
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #1976D2, #1565C0);
  color: #fff;
}

.btn-primary:active {
  background: linear-gradient(135deg, #1565C0, #0D47A1);
  transform: scale(0.98);
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-secondary:active {
  background: #e0e0e0;
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: #fff;
}

.btn-warning {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: #fff;
}

.btn-danger {
  background: linear-gradient(135deg, #F44336, #D32F2F);
  color: #fff;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
}

.input:focus {
  border-color: #1976D2;
  outline: none;
}

/* 文本样式 */
.text-primary {
  color: #1976D2;
}

.text-secondary {
  color: #666;
}

.text-success {
  color: #4CAF50;
}

.text-warning {
  color: #FF9800;
}

.text-danger {
  color: #F44336;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 40rpx;
}

/* 间距 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

/* 布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

/* 显示/隐藏 */
.hidden {
  display: none;
}

.visible {
  display: block;
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, #1976D2, #1565C0);
}

/* 阴影 */
.shadow-sm {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.shadow {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 圆角 */
.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-full {
  border-radius: 50%;
}
