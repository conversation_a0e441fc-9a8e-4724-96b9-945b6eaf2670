<!--pages/subscription/subscription.wxml-->
<view class="page-container">
  <!-- 头部信息 -->
  <view class="header-section">
    <view class="current-plan card gradient-bg">
      <view class="plan-info">
        <text class="plan-title text-inverse">当前订阅</text>
        <text class="plan-name text-inverse">{{currentSubscription.name}}</text>
        <text class="plan-desc text-inverse" wx:if="{{currentSubscription.tier !== 'free'}}">
          到期时间：{{currentSubscription.expiresAt}}
        </text>
        <text class="plan-desc text-inverse" wx:else>
          免费用户，功能受限
        </text>
      </view>
      <view class="plan-icon">{{currentSubscription.icon}}</view>
    </view>
  </view>

  <!-- 使用统计 -->
  <view class="content-container">
    <view class="usage-stats card">
      <view class="stats-header">
        <text class="stats-title">使用统计</text>
        <text class="stats-period text-secondary">本月</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{usageStats.daily}}</text>
          <text class="stat-label text-secondary">今日使用</text>
          <view class="stat-limit text-tertiary">/ {{currentSubscription.dailyLimit}}</view>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{usageStats.monthly}}</text>
          <text class="stat-label text-secondary">本月使用</text>
          <view class="stat-limit text-tertiary">/ {{currentSubscription.monthlyLimit}}</view>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{usageStats.total}}</text>
          <text class="stat-label text-secondary">总计使用</text>
          <view class="stat-limit text-tertiary">次对话</view>
        </view>
      </view>
      <view class="usage-progress">
        <view class="progress-item">
          <text class="progress-label text-secondary">今日进度</text>
          <view class="progress">
            <view class="progress-bar" style="width: {{dailyProgress}}%;"></view>
          </view>
          <text class="progress-text text-tertiary">{{dailyProgress}}%</text>
        </view>
        <view class="progress-item">
          <text class="progress-label text-secondary">月度进度</text>
          <view class="progress">
            <view class="progress-bar" style="width: {{monthlyProgress}}%;"></view>
          </view>
          <text class="progress-text text-tertiary">{{monthlyProgress}}%</text>
        </view>
      </view>
    </view>

    <!-- 订阅计划 -->
    <view class="subscription-plans">
      <view class="section-title">
        <text>订阅计划</text>
      </view>
      
      <view class="plans-grid">
        <!-- 免费版 -->
        <view class="plan-card card {{currentSubscription.tier === 'free' ? 'current-plan-card' : ''}}" 
              data-plan="free" bindtap="selectPlan">
          <view class="plan-header">
            <text class="plan-card-title">免费版</text>
            <view class="plan-price">
              <text class="price-value">¥0</text>
              <text class="price-unit">/月</text>
            </view>
          </view>
          <view class="plan-features">
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">每日10次对话</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">基础AI模型</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">基础功能</text>
            </view>
          </view>
          <view class="plan-badge" wx:if="{{currentSubscription.tier === 'free'}}">
            <text>当前计划</text>
          </view>
        </view>

        <!-- 基础版 -->
        <view class="plan-card card {{currentSubscription.tier === 'basic' ? 'current-plan-card' : ''}}" 
              data-plan="basic" bindtap="selectPlan">
          <view class="plan-header">
            <text class="plan-card-title">基础版</text>
            <view class="plan-price">
              <text class="price-value">¥19.9</text>
              <text class="price-unit">/月</text>
            </view>
          </view>
          <view class="plan-features">
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">每日100次对话</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">更多AI模型</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">云端同步</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">优先支持</text>
            </view>
          </view>
          <view class="plan-badge" wx:if="{{currentSubscription.tier === 'basic'}}">
            <text>当前计划</text>
          </view>
        </view>

        <!-- 标准版 -->
        <view class="plan-card card {{currentSubscription.tier === 'standard' ? 'current-plan-card' : ''}}" 
              data-plan="standard" bindtap="selectPlan">
          <view class="plan-header">
            <text class="plan-card-title">标准版</text>
            <view class="plan-price">
              <text class="price-value">¥39.9</text>
              <text class="price-unit">/月</text>
            </view>
          </view>
          <view class="plan-features">
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">每日300次对话</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">高级AI模型</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">专业分析</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">数据导出</text>
            </view>
          </view>
          <view class="plan-badge popular-badge" wx:if="{{currentSubscription.tier !== 'standard'}}">
            <text>推荐</text>
          </view>
          <view class="plan-badge" wx:if="{{currentSubscription.tier === 'standard'}}">
            <text>当前计划</text>
          </view>
        </view>

        <!-- 专业版 -->
        <view class="plan-card card {{currentSubscription.tier === 'premium' ? 'current-plan-card' : ''}}" 
              data-plan="premium" bindtap="selectPlan">
          <view class="plan-header">
            <text class="plan-card-title">专业版</text>
            <view class="plan-price">
              <text class="price-value">¥99.9</text>
              <text class="price-unit">/月</text>
            </view>
          </view>
          <view class="plan-features">
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">每日1000次对话</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">所有AI模型</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">企业级功能</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">✓</text>
              <text class="feature-text">专属客服</text>
            </view>
          </view>
          <view class="plan-badge" wx:if="{{currentSubscription.tier === 'premium'}}">
            <text>当前计划</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 模型访问权限 -->
    <view class="model-access card">
      <view class="access-header">
        <text class="access-title">AI模型访问权限</text>
        <text class="access-subtitle text-secondary">当前计划可使用的模型</text>
      </view>
      <view class="model-list">
        <view class="model-item" wx:for="{{availableModels}}" wx:key="id">
          <view class="model-icon" style="background-color: {{item.color}};">
            <text>{{item.icon}}</text>
          </view>
          <view class="model-info">
            <text class="model-name">{{item.name}}</text>
            <text class="model-provider text-secondary">{{item.provider}}</text>
          </view>
          <view class="model-tier tag tag-{{item.tier}}">{{item.tierName}}</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" wx:if="{{currentSubscription.tier === 'free'}}">
      <button class="btn btn-primary btn-large" bindtap="showUpgradeModal">
        <text>立即升级</text>
      </button>
    </view>

    <view class="action-buttons" wx:else>
      <button class="btn btn-outline btn-large" bindtap="showUpgradeModal">
        <text>更改计划</text>
      </button>
      <button class="btn btn-secondary btn-large" bindtap="showCancelModal">
        <text>取消订阅</text>
      </button>
    </view>
  </view>
</view>

<!-- 升级弹窗 -->
<view class="modal-overlay" wx:if="{{showUpgradeModal}}" bindtap="hideUpgradeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择订阅时长</text>
      <view class="modal-close" bindtap="hideUpgradeModal">×</view>
    </view>
    <view class="modal-body">
      <view class="duration-options">
        <view class="duration-item {{selectedDuration === 1 ? 'selected' : ''}}" 
              data-duration="1" bindtap="selectDuration">
          <text class="duration-text">1个月</text>
          <text class="duration-price">¥{{selectedPlan.price}}</text>
        </view>
        <view class="duration-item {{selectedDuration === 3 ? 'selected' : ''}}" 
              data-duration="3" bindtap="selectDuration">
          <text class="duration-text">3个月</text>
          <text class="duration-price">¥{{selectedPlan.price * 3 * 0.9}}</text>
          <view class="duration-discount">9折</view>
        </view>
        <view class="duration-item {{selectedDuration === 12 ? 'selected' : ''}}" 
              data-duration="12" bindtap="selectDuration">
          <text class="duration-text">12个月</text>
          <text class="duration-price">¥{{selectedPlan.price * 12 * 0.8}}</text>
          <view class="duration-discount">8折</view>
        </view>
      </view>
      <view class="total-price">
        <text class="total-label">总价：</text>
        <text class="total-value">¥{{totalPrice}}</text>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="hideUpgradeModal">取消</button>
      <button class="btn btn-primary" bindtap="confirmUpgrade">确认支付</button>
    </view>
  </view>
</view>
