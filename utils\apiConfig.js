// utils/apiConfig.js
// API配置管理，支持环境变量

const { API_CONFIG, AI_MODELS } = require('./constants')
const { logger } = require('./logger')

/**
 * API配置管理器
 */
class ApiConfigManager {
  constructor() {
    this.configs = new Map()
    this.initialized = false
  }

  /**
   * 初始化API配置
   */
  async initialize() {
    if (this.initialized) return

    try {
      // 从环境变量或云函数配置中获取API密钥
      await this.loadApiKeys()
      this.initialized = true
      logger.info('API配置初始化完成')
    } catch (error) {
      logger.error('API配置初始化失败:', error)
      throw error
    }
  }

  /**
   * 加载API密钥
   */
  async loadApiKeys() {
    // DeepSeek配置
    const deepseekKey = process.env.DEEPSEEK_API_KEY || await this.getCloudConfig('DEEPSEEK_API_KEY')
    if (deepseekKey) {
      this.configs.set('deepseek', {
        ...API_CONFIG.deepseek,
        apiKey: deepseekKey,
        headers: {
          'Authorization': `Bearer ${deepseekKey}`,
          'Content-Type': 'application/json'
        }
      })
    }

    // 通义千问配置
    const qwenKey = process.env.QWEN_API_KEY || await this.getCloudConfig('QWEN_API_KEY')
    if (qwenKey) {
      this.configs.set('alibaba', {
        ...API_CONFIG.alibaba,
        apiKey: qwenKey,
        headers: {
          'Authorization': `Bearer ${qwenKey}`,
          'Content-Type': 'application/json'
        }
      })
    }

    logger.info(`已加载 ${this.configs.size} 个API配置`)
  }

  /**
   * 从云函数配置中获取API密钥
   */
  async getCloudConfig(key) {
    try {
      // 在云函数环境中，可以从云函数配置中获取
      if (typeof wx !== 'undefined' && wx.cloud) {
        const result = await wx.cloud.callFunction({
          name: 'getApiConfig',
          data: { key }
        })
        return result.result?.value
      }
    } catch (error) {
      logger.warn(`获取云配置 ${key} 失败:`, error)
    }
    return null
  }

  /**
   * 获取指定提供商的配置
   */
  getConfig(provider) {
    if (!this.initialized) {
      throw new Error('API配置未初始化，请先调用initialize()')
    }
    return this.configs.get(provider)
  }

  /**
   * 获取模型的API配置
   */
  getModelConfig(modelId) {
    const model = AI_MODELS[modelId]
    if (!model) {
      throw new Error(`未知的模型: ${modelId}`)
    }

    const config = this.getConfig(model.provider)
    if (!config) {
      throw new Error(`提供商 ${model.provider} 的API配置不可用`)
    }

    return {
      ...config,
      model: modelId
    }
  }

  /**
   * 检查API配置是否可用
   */
  isConfigured(provider) {
    return this.configs.has(provider)
  }

  /**
   * 获取所有可用的提供商
   */
  getAvailableProviders() {
    return Array.from(this.configs.keys())
  }

  /**
   * 测试API连接
   */
  async testConnection(provider) {
    const config = this.getConfig(provider)
    if (!config) {
      throw new Error(`提供商 ${provider} 的配置不可用`)
    }

    try {
      const response = await this.makeRequest(config, {
        model: config.models[0],
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1
      })

      return {
        success: true,
        provider,
        responseTime: response.responseTime
      }
    } catch (error) {
      return {
        success: false,
        provider,
        error: error.message
      }
    }
  }

  /**
   * 发起API请求
   */
  async makeRequest(config, data) {
    const startTime = Date.now()

    try {
      const response = await fetch(`${config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: config.headers,
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      const responseTime = Date.now() - startTime

      return {
        ...result,
        responseTime
      }
    } catch (error) {
      logger.error('API请求失败:', error)
      throw error
    }
  }

  /**
   * 调用AI模型
   */
  async callModel(modelId, messages, options = {}) {
    const config = this.getModelConfig(modelId)
    
    const requestData = {
      model: modelId,
      messages,
      max_tokens: options.maxTokens || config.maxTokens || 2000,
      temperature: options.temperature || 0.7,
      stream: options.stream || false,
      ...options
    }

    return await this.makeRequest(config, requestData)
  }

  /**
   * 获取配置状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      configuredProviders: this.getAvailableProviders(),
      totalConfigs: this.configs.size,
      availableModels: Object.keys(AI_MODELS).filter(modelId => {
        const model = AI_MODELS[modelId]
        return this.isConfigured(model.provider)
      })
    }
  }
}

// 创建全局实例
const apiConfigManager = new ApiConfigManager()

/**
 * 便捷的API调用方法
 */
const apiCall = {
  /**
   * 初始化API配置
   */
  async init() {
    return await apiConfigManager.initialize()
  },

  /**
   * 调用AI模型
   */
  async chat(modelId, messages, options = {}) {
    return await apiConfigManager.callModel(modelId, messages, options)
  },

  /**
   * 测试连接
   */
  async test(provider) {
    return await apiConfigManager.testConnection(provider)
  },

  /**
   * 获取状态
   */
  getStatus() {
    return apiConfigManager.getStatus()
  },

  /**
   * 检查模型是否可用
   */
  isModelAvailable(modelId) {
    try {
      apiConfigManager.getModelConfig(modelId)
      return true
    } catch (error) {
      return false
    }
  }
}

module.exports = {
  apiConfigManager,
  apiCall
}
