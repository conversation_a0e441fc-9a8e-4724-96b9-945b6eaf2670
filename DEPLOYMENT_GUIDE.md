# IVD智能顾问 - 部署指南

## 项目概述

IVD智能顾问是一款专为医疗器械（IVD）行业打造的微信小程序，集成多种AI大语言模型，为用户提供专业的产品研发、注册申报、市场销售等方面的智能咨询服务。

## 核心功能特性

### 🤖 多AI模型集成
- **GPT-3.5 Turbo**: 快速响应，适合日常咨询
- **GPT-4**: 深度推理，复杂问题分析  
- **Claude-3**: 长文本处理，深度分析
- **Gemini Pro**: 多模态AI助手
- **本地回复**: 离线备用方案

### 🎯 IVD专业领域
- **产品研发流程**: 需求分析、技术评估、产品设计、临床试验
- **注册申报要点**: NMPA、CE、FDA注册流程指导
- **市场销售策略**: 市场定位、渠道建设、客户管理
- **质量管理体系**: ISO13485体系建立和维护

### 💬 智能对话体验
- 实时AI对话
- 上下文理解
- 专业术语识别
- 智能建议生成
- 对话历史管理
- 多种导出格式

### ⚙️ 高级功能
- 用户偏好管理
- 性能监控优化
- 合规性检查
- 快速咨询模板
- 离线模式支持

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生开发
- **样式**: WXSS + Flexbox布局
- **状态管理**: 全局数据管理
- **组件化**: 模块化页面设计

### 核心模块
- **AI服务管理** (`utils/aiService.js`): 统一AI服务接口
- **IVD知识库** (`utils/ivdKnowledge.js`): 专业知识管理
- **专业提示词** (`utils/ivdPrompts.js`): 增强AI回复质量
- **法规数据库** (`utils/ivdRegulations.js`): 全球法规信息
- **用户偏好** (`utils/userPreferences.js`): 个性化设置
- **性能监控** (`utils/performance.js`): 性能优化
- **合规检查** (`utils/wechatCompliance.js`): 微信规范检查

## 部署前准备

### 1. 环境检查
```bash
# 检查微信开发者工具版本
# 确保版本 >= 1.06.2307260

# 检查Node.js版本（可选）
node --version  # >= 14.0.0
```

### 2. 项目配置
1. 修改 `project.config.json` 中的 `appid`
2. 配置 `app.json` 中的页面路径
3. 检查 `sitemap.json` 配置

### 3. API密钥配置（可选）
- OpenAI API Key
- Anthropic API Key  
- Google API Key

### 4. 运行部署前检查
```javascript
// 在微信开发者工具控制台运行
const { deploymentPrepManager } = require('./utils/deploymentPrep');
deploymentPrepManager.performPreDeploymentCheck().then(console.log);
```

## 部署流程

### 第一步：代码审查
- [ ] 检查代码质量
- [ ] 移除调试代码
- [ ] 确认错误处理完善
- [ ] 验证用户体验流程

### 第二步：功能测试
```javascript
// 运行集成测试
const { integrationTestManager } = require('./utils/integrationTest');
integrationTestManager.runAllTests().then(console.log);
```

### 第三步：性能优化
```javascript
// 检查性能指标
const { performanceManager } = require('./utils/performance');
console.log(performanceManager.getPerformanceReport());
```

### 第四步：合规检查
```javascript
// 执行合规检查
const { wechatComplianceChecker } = require('./utils/wechatCompliance');
console.log(wechatComplianceChecker.performFullCheck());
```

### 第五步：上传发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 等待审核通过后发布

## 部署后验证

### 1. 功能验证清单
- [ ] 首页正常加载
- [ ] AI对话功能正常
- [ ] 模型切换功能
- [ ] 设置页面功能
- [ ] 导出功能正常
- [ ] 离线模式测试

### 2. 性能监控
- 启动时间 < 3秒
- 对话响应时间 < 5秒
- 内存使用合理
- 存储空间控制

### 3. 用户体验检查
- 界面响应流畅
- 错误提示友好
- 功能引导清晰
- 帮助文档完整

## 运维监控

### 1. 性能监控
```javascript
// 启动性能监控
const { performanceManager } = require('./utils/performance');
performanceManager.startMonitoring();
```

### 2. 错误监控
```javascript
// 查看错误统计
const { errorHandler } = require('./utils/apiUtils');
console.log(errorHandler.getErrorStats());
```

### 3. 用户反馈收集
- 设置页面反馈入口
- 定期收集用户建议
- 监控用户使用数据

## 故障排除

### 常见问题

**1. AI服务无响应**
- 检查网络连接
- 验证API密钥配置
- 查看错误日志
- 尝试切换AI模型

**2. 性能问题**
- 清理聊天记录
- 重启小程序
- 检查存储空间
- 优化图片资源

**3. 合规问题**
- 运行合规检查工具
- 更新隐私政策
- 检查内容过滤
- 联系技术支持

### 回滚方案
1. 保留上一版本代码
2. 准备快速回滚流程
3. 监控关键指标
4. 及时响应用户反馈

## 版本管理

### 版本号规则
- 主版本号：重大功能更新
- 次版本号：新功能添加
- 修订号：Bug修复

### 发布周期
- 重大版本：季度发布
- 功能版本：月度发布
- 修复版本：按需发布

## 技术支持

### 联系方式
- 技术支持邮箱：<EMAIL>
- 开发团队微信：IVD_AI_Dev
- 用户反馈邮箱：<EMAIL>

### 文档资源
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [OpenAI API文档](https://platform.openai.com/docs)
- [项目GitHub仓库](https://github.com/ivd-ai/miniprogram)

## 安全注意事项

### 1. API密钥安全
- 不在代码中硬编码密钥
- 使用安全的存储方式
- 定期轮换密钥
- 监控使用情况

### 2. 用户数据保护
- 最小化数据收集
- 本地存储优先
- 明确隐私政策
- 用户同意机制

### 3. 内容安全
- 实施内容过滤
- 添加免责声明
- 监控AI回复质量
- 建立举报机制

---

**部署完成后，请及时更新本文档并通知相关团队成员。**
