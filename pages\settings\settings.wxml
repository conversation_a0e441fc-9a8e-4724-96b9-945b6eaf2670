<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-card card">
      <view class="user-avatar" bindtap="getUserInfo">
        <image wx:if="{{userInfo.avatarUrl}}" src="{{userInfo.avatarUrl}}" mode="aspectFit"></image>
        <view wx:else class="default-avatar">👤</view>
      </view>
      <view class="user-info">
        <text class="user-name">{{userInfo.nickName || '点击登录'}}</text>
        <text class="user-desc" wx:if="{{userInfo.nickName && !userInfo.isGuest}}">{{subscriptionName}} • IVD专业顾问用户</text>
        <text class="user-desc" wx:elif="{{userInfo.isGuest}}">游客模式 • 功能受限</text>
        <text class="user-desc" wx:else>IVD专业顾问用户</text>
      </view>
      <view class="login-btn" wx:if="{{!userInfo.nickName}}" bindtap="getUserInfo">
        <text>登录</text>
      </view>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="settings-section">
    <view class="section-title">
      <text>应用设置</text>
    </view>
    
    <view class="settings-list">
      <!-- 订阅管理 -->
      <view class="setting-item card" wx:if="{{userInfo.nickName && !userInfo.isGuest}}" bindtap="goToSubscription">
        <view class="setting-icon">💎</view>
        <view class="setting-content">
          <text class="setting-title">订阅管理</text>
          <text class="setting-desc">{{subscriptionName}} • 管理您的订阅</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- AI模型设置 -->
      <view class="setting-item card" bindtap="goToModels">
        <view class="setting-icon">🤖</view>
        <view class="setting-content">
          <text class="setting-title">AI模型管理</text>
          <text class="setting-desc">当前：{{currentModel.name}}</text>
        </view>
        <view class="setting-arrow">></view>
      </view>



      <!-- 聊天设置 -->
      <view class="setting-item card">
        <view class="setting-icon">💬</view>
        <view class="setting-content">
          <text class="setting-title">聊天设置</text>
          <text class="setting-desc">个性化聊天体验</text>
        </view>
        <view class="setting-toggle">
          <switch checked="{{chatSettings.autoSave}}" bindchange="onAutoSaveChange" color="#1976D2"/>
        </view>
      </view>

      <!-- 消息通知 -->
      <view class="setting-item card">
        <view class="setting-icon">🔔</view>
        <view class="setting-content">
          <text class="setting-title">消息通知</text>
          <text class="setting-desc">接收重要更新通知</text>
        </view>
        <view class="setting-toggle">
          <switch checked="{{notificationSettings.enabled}}" bindchange="onNotificationChange" color="#1976D2"/>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="setting-item card" bindtap="showDataManagement">
        <view class="setting-icon">💾</view>
        <view class="setting-content">
          <text class="setting-title">数据管理</text>
          <text class="setting-desc">聊天记录：{{chatCount}}条</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- 云端同步 -->
      <view class="setting-item card" wx:if="{{userInfo.nickName && !userInfo.isGuest}}">
        <view class="setting-icon">☁️</view>
        <view class="setting-content">
          <text class="setting-title">云端同步</text>
          <text class="setting-desc">自动同步数据到云端</text>
        </view>
        <view class="setting-toggle">
          <switch checked="{{cloudSyncEnabled}}" bindchange="onCloudSyncChange" color="#1976D2"/>
        </view>
      </view>

      <!-- 手动同步 -->
      <view class="setting-item card" wx:if="{{userInfo.nickName && !userInfo.isGuest}}" bindtap="manualSync">
        <view class="setting-icon">🔄</view>
        <view class="setting-content">
          <text class="setting-title">手动同步</text>
          <text class="setting-desc">立即同步所有数据</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- 数据恢复 -->
      <view class="setting-item card" wx:if="{{userInfo.nickName && !userInfo.isGuest}}" bindtap="restoreData">
        <view class="setting-icon">📥</view>
        <view class="setting-content">
          <text class="setting-title">数据恢复</text>
          <text class="setting-desc">从云端恢复数据</text>
        </view>
        <view class="setting-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 专业设置 -->
  <view class="professional-section">
    <view class="section-title">
      <text>专业设置</text>
    </view>
    
    <view class="settings-list">
      <!-- 专业领域 -->
      <view class="setting-item card" bindtap="selectProfessionalField">
        <view class="setting-icon">🎯</view>
        <view class="setting-content">
          <text class="setting-title">专业领域</text>
          <text class="setting-desc">{{professionalSettings.field}}</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- 经验级别 -->
      <view class="setting-item card" bindtap="selectExperienceLevel">
        <view class="setting-icon">📊</view>
        <view class="setting-content">
          <text class="setting-title">经验级别</text>
          <text class="setting-desc">{{professionalSettings.experience}}</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- 关注重点 -->
      <view class="setting-item card" bindtap="selectFocusAreas">
        <view class="setting-icon">🔍</view>
        <view class="setting-content">
          <text class="setting-title">关注重点</text>
          <text class="setting-desc">{{professionalSettings.focusAreas.length}}个领域</text>
        </view>
        <view class="setting-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 应用信息 -->
  <view class="app-info-section">
    <view class="section-title">
      <text>应用信息</text>
    </view>
    
    <view class="settings-list">
      <!-- 版本信息 -->
      <view class="setting-item card">
        <view class="setting-icon">📱</view>
        <view class="setting-content">
          <text class="setting-title">版本信息</text>
          <text class="setting-desc">v{{appVersion}}</text>
        </view>
      </view>

      <!-- 帮助中心 -->
      <view class="setting-item card" bindtap="showHelp">
        <view class="setting-icon">❓</view>
        <view class="setting-content">
          <text class="setting-title">帮助中心</text>
          <text class="setting-desc">使用指南和常见问题</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- 意见反馈 -->
      <view class="setting-item card" bindtap="showFeedback">
        <view class="setting-icon">💌</view>
        <view class="setting-content">
          <text class="setting-title">意见反馈</text>
          <text class="setting-desc">帮助我们改进产品</text>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <!-- 关于我们 -->
      <view class="setting-item card" bindtap="showAbout">
        <view class="setting-icon">ℹ️</view>
        <view class="setting-content">
          <text class="setting-title">关于我们</text>
          <text class="setting-desc">了解IVD智能顾问</text>
        </view>
        <view class="setting-arrow">></view>
      </view>
    </view>
  </view>


</view>

<!-- 专业领域选择弹窗 -->
<view class="modal-overlay" wx:if="{{showFieldModal}}" bindtap="hideFieldModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择专业领域</text>
      <view class="modal-close" bindtap="hideFieldModal">×</view>
    </view>
    <view class="field-list">
      <view class="field-option {{item === selectedField ? 'selected' : ''}}" 
            wx:for="{{professionalFields}}" 
            wx:key="index" 
            bindtap="selectField" 
            data-field="{{item}}">
        <text>{{item}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 经验级别选择弹窗 -->
<view class="modal-overlay" wx:if="{{showExperienceModal}}" bindtap="hideExperienceModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择经验级别</text>
      <view class="modal-close" bindtap="hideExperienceModal">×</view>
    </view>
    <view class="experience-list">
      <view class="experience-option {{item === selectedExperience ? 'selected' : ''}}" 
            wx:for="{{experienceLevels}}" 
            wx:key="index" 
            bindtap="selectExperience" 
            data-experience="{{item}}">
        <text>{{item}}</text>
      </view>
    </view>
  </view>
</view>
