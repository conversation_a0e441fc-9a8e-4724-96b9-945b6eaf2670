/* pages/profile/profile.wxss */

.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding-bottom: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}

/* 用户信息区域 - 小程序风格 */
.user-section {
  padding: var(--spacing-page);
  padding-bottom: 0;
}

.user-card {
  padding: var(--spacing-xxl) var(--spacing-xl);
  background: var(--primary-gradient);
  border-radius: var(--radius-large);
  margin-bottom: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.user-name {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--text-inverse);
}

.user-tier {
  display: flex;
  align-items: center;
}

.tier-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-base);
}

.tier-icon {
  font-size: var(--font-size-sm);
}

.tier-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.user-actions {
  display: flex;
  flex-direction: column;
}

/* 使用统计区域 - 小程序卡片风格 */
.stats-section {
  padding: 0 var(--spacing-page);
  margin-bottom: var(--spacing-lg);
}

.stats-card {
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-card);
  border: 1rpx solid var(--border-light);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.stats-period {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.usage-progress {
  border-top: 1rpx solid var(--border-light);
  padding-top: var(--spacing-md);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.progress-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: var(--border-light);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 6rpx;
  transition: width var(--transition-base);
}

/* 功能菜单区域 - 小程序列表风格 */
.menu-section {
  padding: 0 var(--spacing-page);
}

.menu-group {
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  box-shadow: var(--shadow-card);
  border: 1rpx solid var(--border-light);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color var(--transition-base);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background: var(--bg-tertiary);
}

.logout-item {
  color: var(--error-color);
}

.menu-icon {
  width: 60rpx;
  font-size: var(--font-size-lg);
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-left: var(--spacing-md);
}

.logout-item .menu-text {
  color: var(--error-color);
}

.menu-arrow {
  font-size: var(--font-size-lg);
  color: var(--text-quaternary);
}

/* 版本信息 */
.version-section {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-md);
}

.version-text {
  font-size: var(--font-size-sm);
  color: var(--text-quaternary);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
}

.modal-content {
  width: 85%;
  max-width: 700rpx;
  max-height: 80vh;
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1rpx solid var(--border-light);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

/* 帮助内容 */
.help-section {
  margin-bottom: var(--spacing-lg);
}

.help-section:last-child {
  margin-bottom: 0;
}

.help-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: block;
}

.help-item {
  padding: var(--spacing-md) 0;
  border-bottom: 1rpx solid var(--border-light);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.help-item:last-child {
  border-bottom: none;
}

/* 关于内容 */
.about-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.app-logo {
  font-size: 120rpx;
  margin-bottom: var(--spacing-sm);
}

.app-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.app-desc {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  text-align: center;
  line-height: var(--line-height-loose);
}

.about-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin: var(--spacing-lg) 0;
}

.info-item {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.about-links {
  display: flex;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .user-card {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .about-links {
    flex-direction: column;
    width: 100%;
  }
}
