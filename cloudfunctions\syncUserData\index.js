// cloudfunctions/syncUserData/index.js
// 用户数据同步云函数

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 用户数据同步
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, userInfo, data } = event
  
  try {
    const openid = wxContext.OPENID
    
    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }
    
    switch (action) {
      case 'sync':
        return await syncUserData(openid, userInfo)
      case 'upload':
        return await uploadUserData(openid, data)
      case 'download':
        return await downloadUserData(openid)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
    
  } catch (error) {
    console.error('用户数据同步失败:', error)
    return {
      success: false,
      message: '数据同步失败',
      error: error.message
    }
  }
}

/**
 * 同步用户数据
 */
async function syncUserData(openid, userInfo) {
  try {
    // 获取用户的云端数据
    const userDataCollection = db.collection('user_data')
    const existingData = await userDataCollection.where({ openid }).get()
    
    let cloudData = {}
    
    if (existingData.data.length > 0) {
      cloudData = existingData.data[0]
      console.log('找到用户云端数据')
    } else {
      console.log('用户首次同步，创建云端数据')
    }
    
    // 从云存储获取详细数据
    const cloudStorageData = await getFromCloudStorage(openid)
    
    // 合并数据
    const syncedData = {
      userSettings: cloudStorageData?.userSettings || {},
      chatHistory: cloudStorageData?.chatHistory || [],
      preferences: cloudStorageData?.preferences || {},
      lastSyncTime: new Date().toISOString()
    }
    
    // 更新或创建云端数据记录
    if (existingData.data.length > 0) {
      await userDataCollection.doc(existingData.data[0]._id).update({
        data: {
          ...syncedData,
          updatedAt: new Date()
        }
      })
    } else {
      await userDataCollection.add({
        data: {
          openid,
          ...syncedData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    }
    
    return {
      success: true,
      data: syncedData,
      message: '数据同步成功'
    }
    
  } catch (error) {
    console.error('同步用户数据失败:', error)
    throw error
  }
}

/**
 * 上传用户数据到云存储
 */
async function uploadUserData(openid, data) {
  try {
    const cloudPath = `user-data/${openid}/sync-data.json`
    
    const uploadResult = await cloud.uploadFile({
      cloudPath,
      fileContent: Buffer.from(JSON.stringify({
        ...data,
        uploadTime: new Date().toISOString()
      }))
    })
    
    console.log('用户数据已上传到云存储:', uploadResult.fileID)
    
    return {
      success: true,
      fileID: uploadResult.fileID,
      message: '数据上传成功'
    }
    
  } catch (error) {
    console.error('上传用户数据失败:', error)
    throw error
  }
}

/**
 * 从云存储下载用户数据
 */
async function downloadUserData(openid) {
  try {
    const data = await getFromCloudStorage(openid)
    
    return {
      success: true,
      data: data || {},
      message: '数据下载成功'
    }
    
  } catch (error) {
    console.error('下载用户数据失败:', error)
    throw error
  }
}

/**
 * 从云存储获取数据
 */
async function getFromCloudStorage(openid) {
  try {
    // 尝试获取同步数据
    const syncDataPath = `user-data/${openid}/sync-data.json`
    
    try {
      const downloadResult = await cloud.downloadFile({
        fileID: `cloud://your-env-id.your-env-id/${syncDataPath}`
      })
      
      if (downloadResult.fileContent) {
        const dataStr = downloadResult.fileContent.toString()
        return JSON.parse(dataStr)
      }
    } catch (downloadError) {
      console.log('云存储文件不存在或下载失败:', downloadError.message)
    }
    
    // 如果同步数据不存在，尝试获取用户配置文件
    const profilePath = `user-data/${openid}/profile.json`
    
    try {
      const profileResult = await cloud.downloadFile({
        fileID: `cloud://your-env-id.your-env-id/${profilePath}`
      })
      
      if (profileResult.fileContent) {
        const profileStr = profileResult.fileContent.toString()
        const profileData = JSON.parse(profileStr)
        
        // 从用户配置中提取相关数据
        return {
          userSettings: profileData.userSettings || {},
          preferences: profileData.preferences || {},
          chatHistory: [],
          lastSyncTime: profileData.syncTime
        }
      }
    } catch (profileError) {
      console.log('用户配置文件不存在:', profileError.message)
    }
    
    return null
    
  } catch (error) {
    console.error('从云存储获取数据失败:', error)
    return null
  }
}
