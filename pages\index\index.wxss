/* pages/index/index.wxss */

/* 头部区域 */
.header-section {
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
}

.welcome-card {
  text-align: center;
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.logo-section {
  margin-bottom: var(--spacing-lg);
}

.logo-icon {
  font-size: 120rpx;
  display: block;
}

.title-section {
  margin-bottom: var(--spacing-lg);
}

.main-title {
  display: block;
  font-size: var(--font-size-title);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.subtitle {
  display: block;
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-base);
  backdrop-filter: blur(10rpx);
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: var(--spacing-md);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.guest-info, .login-prompt {
  text-align: center;
  margin-top: var(--spacing-lg);
}

.guest-text, .login-text {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 500;
}

.login-tip {
  display: block;
  font-size: var(--font-size-base);
  opacity: 0.8;
  margin-top: var(--spacing-xs);
  text-decoration: underline;
}

/* 快速咨询区域 */
.quick-actions {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.action-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.action-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  transition: all var(--transition-base);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 40rpx;
}

.rd-icon {
  background: linear-gradient(135deg, #2E7CE8, #5A9EF4);
}

.reg-icon {
  background: linear-gradient(135deg, #00C896, #33D4B0);
}

.sales-icon {
  background: linear-gradient(135deg, #FAAD14, #FFD666);
}

.quality-icon {
  background: linear-gradient(135deg, #52C41A, #73D13D);
}

.action-content {
  flex: 1;
}

.action-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.action-desc {
  display: block;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-loose);
}

.action-arrow {
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
  margin-left: var(--spacing-sm);
}

/* AI状态区域 */
.ai-status {
  margin-bottom: var(--spacing-lg);
}

.status-header {
  margin-bottom: var(--spacing-md);
}

.status-left {
  flex: 1;
}

.status-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.status-subtitle {
  font-size: var(--font-size-sm);
}

.current-model {
  margin-bottom: var(--spacing-md);
}

.model-info {
  margin-bottom: var(--spacing-md);
}

.model-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: var(--spacing-md);
  color: white;
}

.model-details {
  margin-left: var(--spacing-md);
}

.model-name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.model-desc {
  display: block;
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
}

.model-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.feature-tag {
  font-size: var(--font-size-xs);
}

.model-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.metric-label {
  width: 80rpx;
  font-size: var(--font-size-xs);
  text-align: right;
}

.metric-bar {
  flex: 1;
  height: 8rpx;
  background: var(--bg-quaternary);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-base);
}

/* 最近对话区域 */
.recent-chats {
  padding: 0 30rpx 30rpx;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.chat-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.chat-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.9);
}

.chat-content {
  flex: 1;
  margin-right: 20rpx;
}

.chat-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.chat-preview {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.chat-meta {
  text-align: right;
}

.chat-time {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.chat-model {
  display: block;
  font-size: 20rpx;
  color: #1976D2;
  background: rgba(25, 118, 210, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

/* 开始聊天按钮 */
.start-chat-section {
  padding: 0 30rpx 40rpx;
}

.start-chat-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
}

/* 功能介绍区域 */
.features-section {
  background: #fff;
  padding: 40rpx 30rpx;
  border-radius: 40rpx 40rpx 0 0;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.feature-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.feature-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}
