/* pages/index/index.wxss */

/* 头部区域 - 小程序风格 */
.header-section {
  padding: var(--spacing-page);
  padding-bottom: 0;
}

.welcome-card {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-xl);
  position: relative;
  overflow: hidden;
  background: var(--primary-gradient);
  border-radius: var(--radius-large);
  margin-bottom: var(--spacing-lg);
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.logo-section {
  margin-bottom: var(--spacing-lg);
}

.logo-icon {
  font-size: 100rpx;
  display: block;
  margin-bottom: var(--spacing-sm);
}

.title-section {
  margin-bottom: var(--spacing-xl);
}

.main-title {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-inverse);
}

.subtitle {
  display: block;
  font-size: var(--font-size-base);
  opacity: 0.9;
  color: var(--text-inverse);
  line-height: 1.5;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-base);
  backdrop-filter: blur(10rpx);
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: var(--spacing-md);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.guest-info, .login-prompt {
  text-align: center;
  margin-top: var(--spacing-lg);
}

.guest-text, .login-text {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 500;
}

.login-tip {
  display: block;
  font-size: var(--font-size-base);
  opacity: 0.8;
  margin-top: var(--spacing-xs);
  text-decoration: underline;
}

/* 快速咨询区域 - 小程序卡片风格 */
.quick-actions {
  padding: 0 var(--spacing-page);
  margin-bottom: var(--spacing-section);
}

.section-title {
  font-size: var(--font-size-title);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-left: var(--spacing-sm);
}

.action-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.action-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-card);
  border: 1rpx solid var(--border-light);
  transition: all var(--transition-base);
}

.action-item:active {
  background: var(--bg-tertiary);
  transform: scale(0.98);
}

.action-icon {
  width: 72rpx;
  height: 72rpx;
  margin-right: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-base);
  font-size: 36rpx;
}

.rd-icon {
  background: var(--primary-color);
  color: var(--text-inverse);
}

.reg-icon {
  background: var(--secondary-color);
  color: var(--text-inverse);
}

.sales-icon {
  background: var(--warning-color);
  color: var(--text-inverse);
}

.quality-icon {
  background: var(--success-color);
  color: var(--text-inverse);
}

.action-content {
  flex: 1;
}

.action-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.action-desc {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.action-arrow {
  font-size: var(--font-size-lg);
  color: var(--text-quaternary);
  margin-left: var(--spacing-sm);
}

/* AI状态区域 */
.ai-status {
  margin-bottom: var(--spacing-lg);
}

.status-header {
  margin-bottom: var(--spacing-md);
}

.status-left {
  flex: 1;
}

.status-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.status-subtitle {
  font-size: var(--font-size-sm);
}

.current-model {
  margin-bottom: var(--spacing-md);
}

.model-info {
  margin-bottom: var(--spacing-md);
}

.model-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: var(--spacing-md);
  color: white;
}

.model-details {
  margin-left: var(--spacing-md);
}

.model-name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.model-desc {
  display: block;
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
}

.model-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.feature-tag {
  font-size: var(--font-size-xs);
}

.model-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.metric-label {
  width: 80rpx;
  font-size: var(--font-size-xs);
  text-align: right;
}

.metric-bar {
  flex: 1;
  height: 8rpx;
  background: var(--bg-quaternary);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--transition-base);
}

/* 最近对话区域 */
.recent-chats {
  padding: 0 30rpx 30rpx;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.chat-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.chat-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.9);
}

.chat-content {
  flex: 1;
  margin-right: 20rpx;
}

.chat-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.chat-preview {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.chat-meta {
  text-align: right;
}

.chat-time {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.chat-model {
  display: block;
  font-size: 20rpx;
  color: #1976D2;
  background: rgba(25, 118, 210, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

/* 开始聊天按钮 */
.start-chat-section {
  padding: 0 30rpx 40rpx;
}

.start-chat-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
}

/* 功能介绍区域 */
.features-section {
  background: #fff;
  padding: 40rpx 30rpx;
  border-radius: 40rpx 40rpx 0 0;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.feature-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.feature-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}
